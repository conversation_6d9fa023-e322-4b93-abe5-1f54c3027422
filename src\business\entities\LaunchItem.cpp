#include "LaunchItem.h"
#include <QFileInfo>
#include <QDir>
#include <QUrl>
#include <QCryptographicHash>
#include <QStandardPaths>
#include <QMimeDatabase>
#include <QLoggingCategory>
#include <QRegularExpression>
#include <algorithm>
#include <cmath>

Q_LOGGING_CATEGORY(lcLaunchItem, "launchitem")

LaunchItem::LaunchItem()
{
    initializeDefaults();
}

LaunchItem::LaunchItem(const QString &name, const QString &path, LaunchItemType type)
    : name(name)
    , path(normalizePath(path))
    , type(type)
{
    initializeDefaults();
    
    // 如果名称为空，使用文件名
    if (this->name.isEmpty()) {
        this->name = getFileName();
    }
    
    // 更新文件信息
    updateFileInfo();
}

LaunchItem::LaunchItem(const LaunchItem &other)
{
    *this = other;
}

LaunchItem& LaunchItem::operator=(const LaunchItem &other)
{
    if (this != &other) {
        id = other.id;
        name = other.name;
        path = other.path;
        description = other.description;
        iconPath = other.iconPath;
        type = other.type;
        categoryId = other.categoryId;
        
        arguments = other.arguments;
        workingDirectory = other.workingDirectory;
        runAsUser = other.runAsUser;
        runAsAdmin = other.runAsAdmin;
        runInTerminal = other.runInTerminal;
        
        tags = other.tags;
        keywords = other.keywords;
        shortcut = other.shortcut;
        priority = other.priority;
        isEnabled = other.isEnabled;
        isVisible = other.isVisible;
        isPinned = other.isPinned;
        
        useCount = other.useCount;
        lastUsed = other.lastUsed;
        firstUsed = other.firstUsed;
        totalRunTime = other.totalRunTime;
        avgRunTime = other.avgRunTime;
        rating = other.rating;
        
        fileSize = other.fileSize;
        fileModified = other.fileModified;
        fileVersion = other.fileVersion;
        fileDescription = other.fileDescription;
        fileCompany = other.fileCompany;
        fileCopyright = other.fileCopyright;
        
        createdAt = other.createdAt;
        updatedAt = other.updatedAt;
        createdBy = other.createdBy;
        source = other.source;
        
        metadata = other.metadata;
        settings = other.settings;
    }
    return *this;
}

LaunchItem::LaunchItem(LaunchItem &&other) noexcept
{
    *this = std::move(other);
}

LaunchItem& LaunchItem::operator=(LaunchItem &&other) noexcept
{
    if (this != &other) {
        id = other.id;
        name = std::move(other.name);
        path = std::move(other.path);
        description = std::move(other.description);
        iconPath = std::move(other.iconPath);
        type = other.type;
        categoryId = other.categoryId;
        
        arguments = std::move(other.arguments);
        workingDirectory = std::move(other.workingDirectory);
        runAsUser = std::move(other.runAsUser);
        runAsAdmin = other.runAsAdmin;
        runInTerminal = other.runInTerminal;
        
        tags = std::move(other.tags);
        keywords = std::move(other.keywords);
        shortcut = std::move(other.shortcut);
        priority = other.priority;
        isEnabled = other.isEnabled;
        isVisible = other.isVisible;
        isPinned = other.isPinned;
        
        useCount = other.useCount;
        lastUsed = other.lastUsed;
        firstUsed = other.firstUsed;
        totalRunTime = other.totalRunTime;
        avgRunTime = other.avgRunTime;
        rating = other.rating;
        
        fileSize = other.fileSize;
        fileModified = other.fileModified;
        fileVersion = std::move(other.fileVersion);
        fileDescription = std::move(other.fileDescription);
        fileCompany = std::move(other.fileCompany);
        fileCopyright = std::move(other.fileCopyright);
        
        createdAt = other.createdAt;
        updatedAt = other.updatedAt;
        createdBy = std::move(other.createdBy);
        source = std::move(other.source);
        
        metadata = std::move(other.metadata);
        settings = std::move(other.settings);
        
        // 重置源对象
        other.id = -1;
        other.type = LaunchItemType::Application;
        other.categoryId = -1;
        other.runAsAdmin = false;
        other.runInTerminal = false;
        other.priority = 0;
        other.isEnabled = true;
        other.isVisible = true;
        other.isPinned = false;
        other.useCount = 0;
        other.totalRunTime = 0;
        other.avgRunTime = 0;
        other.rating = 0.0;
        other.fileSize = 0;
    }
    return *this;
}

bool LaunchItem::isValid() const
{
    // 基本验证
    if (name.isEmpty() || path.isEmpty()) {
        return false;
    }
    
    // 根据类型进行特定验证
    switch (type) {
    case LaunchItemType::Application:
    case LaunchItemType::Document:
    case LaunchItemType::Script:
        return fileExists();
        
    case LaunchItemType::Folder:
        return QDir(path).exists();
        
    case LaunchItemType::Url:
        return QUrl(path).isValid();
        
    case LaunchItemType::Command:
        return !path.isEmpty(); // 命令行只需要非空
        
    default:
        return false;
    }
}

bool LaunchItem::fileExists() const
{
    if (type == LaunchItemType::Url) {
        return QUrl(path).isValid();
    }
    
    if (type == LaunchItemType::Folder) {
        return QDir(path).exists();
    }
    
    return QFileInfo::exists(path);
}

QString LaunchItem::getDisplayName() const
{
    if (!name.isEmpty()) {
        return name;
    }
    
    return getFileName();
}

QString LaunchItem::getFileName() const
{
    if (type == LaunchItemType::Url) {
        QUrl url(path);
        return url.host();
    }
    
    QFileInfo fileInfo(path);
    return fileInfo.fileName();
}

QString LaunchItem::getFileExtension() const
{
    if (type == LaunchItemType::Url) {
        return "url";
    }
    
    QFileInfo fileInfo(path);
    return fileInfo.suffix().toLower();
}

QString LaunchItem::getFileDirectory() const
{
    if (type == LaunchItemType::Url) {
        return QString();
    }
    
    QFileInfo fileInfo(path);
    return fileInfo.absolutePath();
}

QIcon LaunchItem::getIcon() const
{
    // 如果有自定义图标路径，优先使用
    if (!iconPath.isEmpty() && QFileInfo::exists(iconPath)) {
        return QIcon(iconPath);
    }
    
    // 根据类型返回默认图标
    switch (type) {
    case LaunchItemType::Application:
        return QIcon(":/icons/application.png");
    case LaunchItemType::Document:
        return QIcon(":/icons/document.png");
    case LaunchItemType::Folder:
        return QIcon(":/icons/folder.png");
    case LaunchItemType::Url:
        return QIcon(":/icons/url.png");
    case LaunchItemType::Command:
        return QIcon(":/icons/command.png");
    case LaunchItemType::Script:
        return QIcon(":/icons/command.png");
    default:
        return QIcon(":/icons/application.png");
    }
}

QString LaunchItem::getTypeString() const
{
    return launchItemTypeToString(type);
}

QString LaunchItem::getTypeDisplayName() const
{
    switch (type) {
    case LaunchItemType::Application:
        return QObject::tr("应用程序");
    case LaunchItemType::Document:
        return QObject::tr("文档");
    case LaunchItemType::Folder:
        return QObject::tr("文件夹");
    case LaunchItemType::Url:
        return QObject::tr("网址");
    case LaunchItemType::Command:
        return QObject::tr("命令");
    case LaunchItemType::Script:
        return QObject::tr("脚本");
    default:
        return QObject::tr("未知");
    }
}

void LaunchItem::updateFileInfo()
{
    if (type == LaunchItemType::Url || type == LaunchItemType::Command) {
        return; // URL和命令不需要文件信息
    }
    
    QFileInfo fileInfo(path);
    if (!fileInfo.exists()) {
        qCWarning(lcLaunchItem) << "File does not exist:" << path;
        return;
    }
    
    // 更新基本文件信息
    fileSize = fileInfo.size();
    fileModified = fileInfo.lastModified();
    
    // 如果工作目录为空，设置为文件所在目录
    if (workingDirectory.isEmpty()) {
        workingDirectory = fileInfo.absolutePath();
    }
    
    // 尝试提取图标
    if (iconPath.isEmpty()) {
        iconPath = extractFileIcon(path);
    }
    
    // 更新时间戳
    updatedAt = QDateTime::currentDateTime();
}

void LaunchItem::recordUsage(int runTime)
{
    useCount++;
    lastUsed = QDateTime::currentDateTime();

    // 如果是首次使用，记录首次使用时间
    if (firstUsed.isNull()) {
        firstUsed = lastUsed;
    }

    // 更新运行时间统计
    if (runTime > 0) {
        totalRunTime += runTime;
        avgRunTime = totalRunTime / useCount;
    }

    // 更新时间戳
    updatedAt = QDateTime::currentDateTime();

    qCDebug(lcLaunchItem) << "Recorded usage for" << name << "count:" << useCount << "runtime:" << runTime;
}

double LaunchItem::calculateRelevanceScore(const QString &query) const
{
    if (query.isEmpty()) {
        return 0.0;
    }

    double score = 0.0;

    // 名称匹配分数（权重最高）
    double nameScore = calculateNameScore(query);
    score += nameScore * 0.4;

    // 路径匹配分数
    double pathScore = calculatePathScore(query);
    score += pathScore * 0.2;

    // 标签匹配分数
    double tagScore = calculateTagScore(query);
    score += tagScore * 0.2;

    // 使用频率分数
    double usageScore = calculateUsageScore();
    score += usageScore * 0.1;

    // 优先级分数
    double priorityScore = std::min(priority / 100.0, 1.0);
    score += priorityScore * 0.1;

    return std::min(score, 1.0);
}

bool LaunchItem::matchesQuery(const QString &query, bool fuzzy) const
{
    if (query.isEmpty()) {
        return true;
    }

    QString lowerQuery = query.toLower();

    // 检查名称匹配
    if (name.toLower().contains(lowerQuery)) {
        return true;
    }

    // 检查路径匹配
    if (path.toLower().contains(lowerQuery)) {
        return true;
    }

    // 检查描述匹配
    if (description.toLower().contains(lowerQuery)) {
        return true;
    }

    // 检查标签匹配
    for (const QString &tag : tags) {
        if (tag.toLower().contains(lowerQuery)) {
            return true;
        }
    }

    // 检查关键词匹配
    for (const QString &keyword : keywords) {
        if (keyword.toLower().contains(lowerQuery)) {
            return true;
        }
    }

    // 模糊匹配
    if (fuzzy) {
        // 简单的模糊匹配：检查查询字符是否按顺序出现在名称中
        QString lowerName = name.toLower();
        int queryIndex = 0;
        for (int i = 0; i < lowerName.length() && queryIndex < lowerQuery.length(); ++i) {
            if (lowerName[i] == lowerQuery[queryIndex]) {
                queryIndex++;
            }
        }
        return queryIndex == lowerQuery.length();
    }

    return false;
}

QVariantMap LaunchItem::toJson() const
{
    QVariantMap json;

    // 基本属性
    json["id"] = id;
    json["name"] = name;
    json["path"] = path;
    json["description"] = description;
    json["iconPath"] = iconPath;
    json["type"] = static_cast<int>(type);
    json["categoryId"] = categoryId;

    // 启动参数
    json["arguments"] = arguments;
    json["workingDirectory"] = workingDirectory;
    json["runAsUser"] = runAsUser;
    json["runAsAdmin"] = runAsAdmin;
    json["runInTerminal"] = runInTerminal;

    // 显示属性
    json["tags"] = tags;
    json["keywords"] = keywords;
    json["shortcut"] = shortcut;
    json["priority"] = priority;
    json["isEnabled"] = isEnabled;
    json["isVisible"] = isVisible;
    json["isPinned"] = isPinned;

    // 统计信息
    json["useCount"] = useCount;
    json["lastUsed"] = lastUsed.toString(Qt::ISODate);
    json["firstUsed"] = firstUsed.toString(Qt::ISODate);
    json["totalRunTime"] = totalRunTime;
    json["avgRunTime"] = avgRunTime;
    json["rating"] = rating;

    // 文件信息
    json["fileSize"] = static_cast<qint64>(fileSize);
    json["fileModified"] = fileModified.toString(Qt::ISODate);
    json["fileVersion"] = fileVersion;
    json["fileDescription"] = fileDescription;
    json["fileCompany"] = fileCompany;
    json["fileCopyright"] = fileCopyright;

    // 系统信息
    json["createdAt"] = createdAt.toString(Qt::ISODate);
    json["updatedAt"] = updatedAt.toString(Qt::ISODate);
    json["createdBy"] = createdBy;
    json["source"] = source;

    // 扩展属性
    json["metadata"] = metadata;
    json["settings"] = settings;

    return json;
}

LaunchItem LaunchItem::fromJson(const QVariantMap &json)
{
    LaunchItem item;

    // 基本属性
    item.id = json.value("id", -1).toInt();
    item.name = json.value("name").toString();
    item.path = json.value("path").toString();
    item.description = json.value("description").toString();
    item.iconPath = json.value("iconPath").toString();
    item.type = static_cast<LaunchItemType>(json.value("type", 0).toInt());
    item.categoryId = json.value("categoryId", -1).toInt();

    // 启动参数
    item.arguments = json.value("arguments").toString();
    item.workingDirectory = json.value("workingDirectory").toString();
    item.runAsUser = json.value("runAsUser").toString();
    item.runAsAdmin = json.value("runAsAdmin", false).toBool();
    item.runInTerminal = json.value("runInTerminal", false).toBool();

    // 显示属性
    item.tags = json.value("tags").toStringList();
    item.keywords = json.value("keywords").toStringList();
    item.shortcut = json.value("shortcut").toString();
    item.priority = json.value("priority", 0).toInt();
    item.isEnabled = json.value("isEnabled", true).toBool();
    item.isVisible = json.value("isVisible", true).toBool();
    item.isPinned = json.value("isPinned", false).toBool();

    // 统计信息
    item.useCount = json.value("useCount", 0).toInt();
    item.lastUsed = QDateTime::fromString(json.value("lastUsed").toString(), Qt::ISODate);
    item.firstUsed = QDateTime::fromString(json.value("firstUsed").toString(), Qt::ISODate);
    item.totalRunTime = json.value("totalRunTime", 0).toInt();
    item.avgRunTime = json.value("avgRunTime", 0).toInt();
    item.rating = json.value("rating", 0.0).toDouble();

    // 文件信息
    item.fileSize = json.value("fileSize", 0).toLongLong();
    item.fileModified = QDateTime::fromString(json.value("fileModified").toString(), Qt::ISODate);
    item.fileVersion = json.value("fileVersion").toString();
    item.fileDescription = json.value("fileDescription").toString();
    item.fileCompany = json.value("fileCompany").toString();
    item.fileCopyright = json.value("fileCopyright").toString();

    // 系统信息
    item.createdAt = QDateTime::fromString(json.value("createdAt").toString(), Qt::ISODate);
    item.updatedAt = QDateTime::fromString(json.value("updatedAt").toString(), Qt::ISODate);
    item.createdBy = json.value("createdBy").toString();
    item.source = json.value("source").toString();

    // 扩展属性
    item.metadata = json.value("metadata").toMap();
    item.settings = json.value("settings").toMap();

    return item;
}

bool LaunchItem::operator==(const LaunchItem &other) const
{
    return id == other.id &&
           name == other.name &&
           path == other.path &&
           type == other.type;
}

bool LaunchItem::operator!=(const LaunchItem &other) const
{
    return !(*this == other);
}

bool LaunchItem::operator<(const LaunchItem &other) const
{
    // 首先按优先级排序
    if (priority != other.priority) {
        return priority > other.priority; // 优先级高的在前
    }

    // 然后按置顶状态排序
    if (isPinned != other.isPinned) {
        return isPinned; // 置顶的在前
    }

    // 然后按使用次数排序
    if (useCount != other.useCount) {
        return useCount > other.useCount; // 使用次数多的在前
    }

    // 最后按名称排序
    return name.toLower() < other.name.toLower();
}

uint LaunchItem::hash() const
{
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(QString::number(id).toUtf8());
    hash.addData(name.toUtf8());
    hash.addData(path.toUtf8());
    hash.addData(QString::number(static_cast<int>(type)).toUtf8());

    QByteArray result = hash.result();
    return qFromBigEndian<uint>(result.constData());
}

void LaunchItem::initializeDefaults()
{
    QDateTime now = QDateTime::currentDateTime();
    createdAt = now;
    updatedAt = now;
    source = "manual"; // 默认为手动添加

    // 设置默认创建者
    createdBy = qgetenv("USER");
    if (createdBy.isEmpty()) {
        createdBy = qgetenv("USERNAME"); // Windows
    }
    if (createdBy.isEmpty()) {
        createdBy = "unknown";
    }
}

QString LaunchItem::normalizePath(const QString &path) const
{
    if (path.isEmpty()) {
        return path;
    }

    // URL不需要规范化
    if (path.startsWith("http://") || path.startsWith("https://") || path.startsWith("ftp://")) {
        return path;
    }

    // 规范化文件路径
    QFileInfo fileInfo(path);
    return fileInfo.absoluteFilePath();
}

QString LaunchItem::extractFileIcon(const QString &filePath) const
{
    // 这里可以实现从可执行文件中提取图标的逻辑
    // 暂时返回空字符串，使用默认图标
    Q_UNUSED(filePath)
    return QString();
}

double LaunchItem::calculateNameScore(const QString &query) const
{
    QString lowerName = name.toLower();
    QString lowerQuery = query.toLower();

    // 完全匹配
    if (lowerName == lowerQuery) {
        return 1.0;
    }

    // 开头匹配
    if (lowerName.startsWith(lowerQuery)) {
        return 0.9;
    }

    // 包含匹配
    if (lowerName.contains(lowerQuery)) {
        return 0.7;
    }

    // 模糊匹配
    int matches = 0;
    int queryIndex = 0;
    for (int i = 0; i < lowerName.length() && queryIndex < lowerQuery.length(); ++i) {
        if (lowerName[i] == lowerQuery[queryIndex]) {
            matches++;
            queryIndex++;
        }
    }

    if (matches == lowerQuery.length()) {
        return 0.5 * (static_cast<double>(matches) / lowerName.length());
    }

    return 0.0;
}

double LaunchItem::calculatePathScore(const QString &query) const
{
    QString lowerPath = path.toLower();
    QString lowerQuery = query.toLower();

    if (lowerPath.contains(lowerQuery)) {
        // 路径匹配的分数较低
        return 0.3;
    }

    return 0.0;
}

double LaunchItem::calculateTagScore(const QString &query) const
{
    QString lowerQuery = query.toLower();

    for (const QString &tag : tags) {
        if (tag.toLower().contains(lowerQuery)) {
            return 0.6;
        }
    }

    for (const QString &keyword : keywords) {
        if (keyword.toLower().contains(lowerQuery)) {
            return 0.6;
        }
    }

    return 0.0;
}

double LaunchItem::calculateUsageScore() const
{
    if (useCount == 0) {
        return 0.0;
    }

    // 基于使用次数的对数分数
    double score = std::log(useCount + 1) / std::log(100); // 假设100次使用为满分

    // 考虑最近使用时间
    if (!lastUsed.isNull()) {
        qint64 daysSinceLastUse = lastUsed.daysTo(QDateTime::currentDateTime());
        double recencyFactor = std::exp(-daysSinceLastUse / 30.0); // 30天衰减
        score *= recencyFactor;
    }

    return std::min(score, 1.0);
}

// 全局函数实现
QString launchItemTypeToString(LaunchItemType type)
{
    switch (type) {
    case LaunchItemType::Application:
        return "application";
    case LaunchItemType::Document:
        return "document";
    case LaunchItemType::Folder:
        return "folder";
    case LaunchItemType::Url:
        return "url";
    case LaunchItemType::Command:
        return "command";
    case LaunchItemType::Script:
        return "script";
    default:
        return "unknown";
    }
}

LaunchItemType launchItemTypeFromString(const QString &typeString)
{
    QString lower = typeString.toLower();

    if (lower == "application") return LaunchItemType::Application;
    if (lower == "document") return LaunchItemType::Document;
    if (lower == "folder") return LaunchItemType::Folder;
    if (lower == "url") return LaunchItemType::Url;
    if (lower == "command") return LaunchItemType::Command;
    if (lower == "script") return LaunchItemType::Script;

    return LaunchItemType::Application; // 默认类型
}

uint qHash(const LaunchItem &item, uint seed)
{
    return item.hash() ^ seed;
}
