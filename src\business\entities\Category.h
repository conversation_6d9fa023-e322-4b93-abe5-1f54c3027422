#pragma once

#include <QString>
#include <QStringList>
#include <QDateTime>
#include <QIcon>
#include <QColor>
#include <QVariantMap>

/**
 * @brief 分类实体类
 * 
 * 表示启动项的分类，支持层级结构和自定义属性
 */
class Category
{
public:
    /**
     * @brief 默认构造函数
     */
    Category();
    
    /**
     * @brief 构造函数
     * @param name 分类名称
     * @param parentId 父分类ID
     */
    Category(const QString &name, int parentId = -1);
    
    /**
     * @brief 拷贝构造函数
     */
    Category(const Category &other);
    
    /**
     * @brief 赋值操作符
     */
    Category& operator=(const Category &other);
    
    /**
     * @brief 移动构造函数
     */
    Category(Category &&other) noexcept;
    
    /**
     * @brief 移动赋值操作符
     */
    Category& operator=(Category &&other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~Category() = default;

    // 基本属性
    int id = -1;                           // 唯一标识符
    QString name;                          // 分类名称
    QString description;                   // 描述信息
    QString iconPath;                      // 图标路径
    QColor color;                          // 分类颜色
    int parentId = -1;                     // 父分类ID（-1表示根分类）
    
    // 显示属性
    int sortOrder = 0;                     // 排序顺序
    bool isVisible = true;                 // 是否可见
    bool isExpanded = true;                // 是否展开（用于树形显示）
    bool isSystem = false;                 // 是否系统分类（不可删除）
    bool isDefault = false;                // 是否默认分类
    
    // 统计信息
    int itemCount = 0;                     // 包含的启动项数量
    int totalItemCount = 0;                // 包含子分类的总启动项数量
    QDateTime lastUsed;                    // 最后使用时间
    int useCount = 0;                      // 使用次数
    
    // 系统信息
    QDateTime createdAt;                   // 创建时间
    QDateTime updatedAt;                   // 更新时间
    QString createdBy;                     // 创建者
    
    // 扩展属性
    QVariantMap metadata;                  // 元数据
    QVariantMap settings;                  // 设置信息
    QStringList tags;                      // 标签

    /**
     * @brief 检查分类是否有效
     * @return 是否有效
     */
    bool isValid() const;
    
    /**
     * @brief 检查是否为根分类
     * @return 是否为根分类
     */
    bool isRoot() const;
    
    /**
     * @brief 检查是否有子分类
     * @return 是否有子分类
     */
    bool hasChildren() const;
    
    /**
     * @brief 获取显示名称
     * @return 显示名称
     */
    QString getDisplayName() const;
    
    /**
     * @brief 获取完整路径（包含父分类）
     * @param separator 分隔符
     * @return 完整路径
     */
    QString getFullPath(const QString &separator = " > ") const;
    
    /**
     * @brief 获取层级深度
     * @return 层级深度（根分类为0）
     */
    int getDepth() const;
    
    /**
     * @brief 获取图标
     * @return 图标对象
     */
    QIcon getIcon() const;
    
    /**
     * @brief 获取默认颜色
     * @return 默认颜色
     */
    static QColor getDefaultColor();
    
    /**
     * @brief 获取系统分类颜色
     * @return 系统分类颜色
     */
    static QColor getSystemColor();
    
    /**
     * @brief 更新统计信息
     * @param itemCount 启动项数量
     * @param totalItemCount 总启动项数量
     */
    void updateStatistics(int itemCount, int totalItemCount = -1);
    
    /**
     * @brief 记录使用
     */
    void recordUsage();
    
    /**
     * @brief 匹配搜索查询
     * @param query 查询字符串
     * @param fuzzy 是否模糊匹配
     * @return 是否匹配
     */
    bool matchesQuery(const QString &query, bool fuzzy = true) const;
    
    /**
     * @brief 计算相关性分数
     * @param query 查询字符串
     * @return 相关性分数（0-1）
     */
    double calculateRelevanceScore(const QString &query) const;
    
    /**
     * @brief 转换为JSON对象
     * @return JSON对象
     */
    QVariantMap toJson() const;
    
    /**
     * @brief 从JSON对象创建
     * @param json JSON对象
     * @return 分类对象
     */
    static Category fromJson(const QVariantMap &json);
    
    /**
     * @brief 比较操作符
     */
    bool operator==(const Category &other) const;
    bool operator!=(const Category &other) const;
    bool operator<(const Category &other) const;
    
    /**
     * @brief 获取哈希值
     * @return 哈希值
     */
    uint hash() const;

    // 静态方法 - 预定义分类
    
    /**
     * @brief 创建默认分类
     * @return 默认分类
     */
    static Category createDefaultCategory();
    
    /**
     * @brief 创建系统分类列表
     * @return 系统分类列表
     */
    static QList<Category> createSystemCategories();
    
    /**
     * @brief 获取分类图标路径
     * @param categoryName 分类名称
     * @return 图标路径
     */
    static QString getCategoryIconPath(const QString &categoryName);
    
    /**
     * @brief 获取分类颜色
     * @param categoryName 分类名称
     * @return 分类颜色
     */
    static QColor getCategoryColor(const QString &categoryName);

private:
    /**
     * @brief 初始化默认值
     */
    void initializeDefaults();
    
    /**
     * @brief 计算名称匹配分数
     * @param query 查询字符串
     * @return 匹配分数
     */
    double calculateNameScore(const QString &query) const;
    
    /**
     * @brief 计算描述匹配分数
     * @param query 查询字符串
     * @return 匹配分数
     */
    double calculateDescriptionScore(const QString &query) const;
    
    /**
     * @brief 计算标签匹配分数
     * @param query 查询字符串
     * @return 匹配分数
     */
    double calculateTagScore(const QString &query) const;
    
    /**
     * @brief 计算使用频率分数
     * @return 使用频率分数
     */
    double calculateUsageScore() const;

    // 静态数据
    static QHash<QString, QString> s_categoryIcons;
    static QHash<QString, QColor> s_categoryColors;
    static bool s_staticDataInitialized;
    
    /**
     * @brief 初始化静态数据
     */
    static void initializeStaticData();
};

/**
 * @brief 哈希函数（用于QHash）
 */
uint qHash(const Category &category, uint seed = 0);
