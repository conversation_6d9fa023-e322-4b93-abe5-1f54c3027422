#pragma once

#include <QObject>
#include <QList>
#include <QDateTime>
#include <QVariantMap>
#include <memory>

#include "business/entities/LaunchItem.h"

// 前置声明
class DatabaseManager;

/**
 * @brief 启动项查询条件
 */
struct LaunchItemQuery {
    QString nameFilter;                    // 名称过滤
    QString pathFilter;                    // 路径过滤
    QList<LaunchItemType> typeFilter;      // 类型过滤
    QList<int> categoryFilter;             // 分类过滤
    bool enabledOnly = true;               // 仅启用的项目
    bool visibleOnly = true;               // 仅可见的项目
    bool pinnedOnly = false;               // 仅置顶的项目
    QString sortBy = "name";               // 排序字段
    bool sortAscending = true;             // 排序方向
    int limit = -1;                        // 限制数量
    int offset = 0;                        // 偏移量
    QDateTime modifiedSince;               // 修改时间过滤
    QStringList tags;                      // 标签过滤
    double minRating = 0.0;                // 最小评分
    int minUseCount = 0;                   // 最小使用次数
};

/**
 * @brief 启动项统计信息
 */
struct LaunchItemStats {
    int totalItems = 0;                    // 总项目数
    int enabledItems = 0;                  // 启用项目数
    int pinnedItems = 0;                   // 置顶项目数
    QHash<LaunchItemType, int> typeCount;  // 按类型统计
    QHash<int, int> categoryCount;         // 按分类统计
    QDateTime lastModified;                // 最后修改时间
    double avgRating = 0.0;                // 平均评分
    int totalUseCount = 0;                 // 总使用次数
};

/**
 * @brief 启动项仓储类
 * 
 * 负责启动项的数据访问操作，包括增删改查、统计分析等
 */
class LaunchItemRepository : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param databaseManager 数据库管理器
     * @param parent 父对象
     */
    explicit LaunchItemRepository(std::shared_ptr<DatabaseManager> databaseManager, 
                                  QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~LaunchItemRepository();

    // 基本CRUD操作
    
    /**
     * @brief 添加启动项
     * @param item 启动项
     * @return 是否成功
     */
    bool addItem(LaunchItem &item);
    
    /**
     * @brief 批量添加启动项
     * @param items 启动项列表
     * @return 成功添加的数量
     */
    int addItems(QList<LaunchItem> &items);
    
    /**
     * @brief 更新启动项
     * @param item 启动项
     * @return 是否成功
     */
    bool updateItem(const LaunchItem &item);
    
    /**
     * @brief 批量更新启动项
     * @param items 启动项列表
     * @return 成功更新的数量
     */
    int updateItems(const QList<LaunchItem> &items);
    
    /**
     * @brief 删除启动项
     * @param id 启动项ID
     * @return 是否成功
     */
    bool removeItem(int id);
    
    /**
     * @brief 批量删除启动项
     * @param ids 启动项ID列表
     * @return 成功删除的数量
     */
    int removeItems(const QList<int> &ids);
    
    /**
     * @brief 根据ID获取启动项
     * @param id 启动项ID
     * @return 启动项对象，如果不存在则返回无效对象
     */
    LaunchItem getItem(int id) const;
    
    /**
     * @brief 根据路径获取启动项
     * @param path 文件路径
     * @return 启动项对象，如果不存在则返回无效对象
     */
    LaunchItem getItemByPath(const QString &path) const;

    // 查询操作
    
    /**
     * @brief 获取所有启动项
     * @return 启动项列表
     */
    QList<LaunchItem> getAllItems() const;
    
    /**
     * @brief 根据条件查询启动项
     * @param query 查询条件
     * @return 启动项列表
     */
    QList<LaunchItem> findItems(const LaunchItemQuery &query) const;
    
    /**
     * @brief 根据分类获取启动项
     * @param categoryId 分类ID
     * @param includeSubcategories 是否包含子分类
     * @return 启动项列表
     */
    QList<LaunchItem> getItemsByCategory(int categoryId, bool includeSubcategories = false) const;
    
    /**
     * @brief 根据类型获取启动项
     * @param type 启动项类型
     * @return 启动项列表
     */
    QList<LaunchItem> getItemsByType(LaunchItemType type) const;
    
    /**
     * @brief 搜索启动项
     * @param searchText 搜索文本
     * @param fuzzy 是否模糊搜索
     * @param limit 限制数量
     * @return 启动项列表
     */
    QList<LaunchItem> searchItems(const QString &searchText, bool fuzzy = true, int limit = 50) const;
    
    /**
     * @brief 获取最近使用的启动项
     * @param limit 限制数量
     * @return 启动项列表
     */
    QList<LaunchItem> getRecentlyUsedItems(int limit = 10) const;
    
    /**
     * @brief 获取最常使用的启动项
     * @param limit 限制数量
     * @return 启动项列表
     */
    QList<LaunchItem> getMostUsedItems(int limit = 10) const;
    
    /**
     * @brief 获取置顶的启动项
     * @return 启动项列表
     */
    QList<LaunchItem> getPinnedItems() const;
    
    /**
     * @brief 获取评分最高的启动项
     * @param limit 限制数量
     * @return 启动项列表
     */
    QList<LaunchItem> getTopRatedItems(int limit = 10) const;

    // 统计操作
    
    /**
     * @brief 获取启动项统计信息
     * @return 统计信息
     */
    LaunchItemStats getStatistics() const;
    
    /**
     * @brief 获取启动项数量
     * @return 总数量
     */
    int getItemCount() const;
    
    /**
     * @brief 根据分类获取启动项数量
     * @param categoryId 分类ID
     * @return 数量
     */
    int getItemCountByCategory(int categoryId) const;
    
    /**
     * @brief 根据类型获取启动项数量
     * @param type 启动项类型
     * @return 数量
     */
    int getItemCountByType(LaunchItemType type) const;

    // 维护操作
    
    /**
     * @brief 验证启动项
     * @return 无效启动项的ID列表
     */
    QList<int> validateItems() const;
    
    /**
     * @brief 清理无效启动项
     * @return 清理的数量
     */
    int cleanupInvalidItems();
    
    /**
     * @brief 更新文件信息
     * @param ids 启动项ID列表，空表示更新所有
     * @return 更新的数量
     */
    int updateFileInfo(const QList<int> &ids = {});
    
    /**
     * @brief 重建搜索索引
     * @return 是否成功
     */
    bool rebuildSearchIndex();

    // 导入导出
    
    /**
     * @brief 导出启动项
     * @param filePath 文件路径
     * @param format 格式 (json/xml/csv)
     * @param ids 启动项ID列表，空表示导出所有
     * @return 是否成功
     */
    bool exportItems(const QString &filePath, const QString &format, const QList<int> &ids = {}) const;
    
    /**
     * @brief 导入启动项
     * @param filePath 文件路径
     * @param format 格式 (json/xml/csv)
     * @param mergeMode 合并模式 (replace/merge/skip)
     * @return 导入的数量
     */
    int importItems(const QString &filePath, const QString &format, const QString &mergeMode = "merge");

    // 事务操作
    
    /**
     * @brief 在事务中执行操作
     * @param operation 操作函数
     * @return 是否成功
     */
    bool executeInTransaction(std::function<bool()> operation);

signals:
    /**
     * @brief 启动项添加信号
     * @param item 启动项
     */
    void itemAdded(const LaunchItem &item);
    
    /**
     * @brief 启动项更新信号
     * @param item 启动项
     */
    void itemUpdated(const LaunchItem &item);
    
    /**
     * @brief 启动项删除信号
     * @param id 启动项ID
     */
    void itemRemoved(int id);
    
    /**
     * @brief 批量操作完成信号
     * @param operation 操作类型
     * @param count 影响的数量
     */
    void batchOperationCompleted(const QString &operation, int count);

private:
    /**
     * @brief 从查询结果创建启动项
     * @param query SQL查询结果
     * @return 启动项对象
     */
    LaunchItem createItemFromQuery(const QSqlQuery &query) const;
    
    /**
     * @brief 将启动项绑定到查询
     * @param query SQL查询
     * @param item 启动项
     */
    void bindItemToQuery(QSqlQuery &query, const LaunchItem &item) const;
    
    /**
     * @brief 构建查询SQL
     * @param query 查询条件
     * @return SQL语句和参数
     */
    QPair<QString, QVariantList> buildQuerySql(const LaunchItemQuery &query) const;
    
    /**
     * @brief 验证单个启动项
     * @param item 启动项
     * @return 是否有效
     */
    bool validateItem(const LaunchItem &item) const;

private:
    std::shared_ptr<DatabaseManager> m_databaseManager;  // 数据库管理器
    mutable QMutex m_mutex;                              // 线程安全锁
};
