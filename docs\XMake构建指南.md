# KK QuickLaunch XMake 构建指南

## 🚀 快速开始

### 1. 安装 XMake

#### Windows
```bash
# 使用 PowerShell
Invoke-Expression (Invoke-Webrequest 'https://xmake.io/psget.text' -UseBasicParsing).Content

# 或者下载安装包
# https://github.com/xmake-io/xmake/releases
```

#### Linux/macOS
```bash
# 使用安装脚本
curl -fsSL https://xmake.io/shget.text | bash

# 或者使用包管理器
# Ubuntu/Debian: apt install xmake
# macOS: brew install xmake
```

### 2. 安装 Qt6

#### Windows
```bash
# 下载 Qt6 在线安装器
# https://www.qt.io/download-qt-installer

# 或使用 vcpkg
vcpkg install qt6-base qt6-multimedia qt6-speech
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt install qt6-base-dev qt6-multimedia-dev qt6-speech-dev

# Fedora/CentOS
sudo dnf install qt6-qtbase-devel qt6-qtmultimedia-devel qt6-qtspeech-devel
```

#### macOS
```bash
# 使用 Homebrew
brew install qt6
```

### 3. 构建项目

#### 快速构建
```bash
# Windows
build_xmake.bat

# Linux/macOS
chmod +x build_xmake.sh
./build_xmake.sh
```

#### 手动构建
```bash
# 配置项目
xmake config -m release

# 构建
xmake build

# 运行
xmake run
```

## 📋 构建选项

### 构建模式

```bash
# Debug 模式（开发调试）
xmake config -m debug
xmake build

# Release 模式（发布版本）
xmake config -m release
xmake build
```

### 平台配置

```bash
# Windows x64
xmake config -p windows -a x64

# Linux x64
xmake config -p linux -a x86_64

# macOS x64
xmake config -p macosx -a x86_64

# macOS ARM64 (Apple Silicon)
xmake config -p macosx -a arm64
```

### 目标构建

```bash
# 构建主程序
xmake build kkquicklaunch

# 构建测试
xmake build kkquicklaunch_tests

# 构建所有目标
xmake build -a
```

## 🧪 测试

### 运行单元测试
```bash
# 构建并运行测试
xmake build kkquicklaunch_tests
xmake run kkquicklaunch_tests

# 或使用脚本
./build_xmake.sh test
```

### 测试覆盖率
```bash
# 启用覆盖率（需要 gcov/llvm-cov）
xmake config -m debug --coverage=true
xmake build kkquicklaunch_tests
xmake run kkquicklaunch_tests

# 生成覆盖率报告
xmake run --coverage
```

## 📦 打包和安装

### 创建发布包
```bash
# 创建 ZIP 包
xmake package -f zip

# 创建 tar.gz 包
xmake package -f tar.gz

# 使用脚本打包
./build_xmake.sh package
```

### 安装到系统
```bash
# 安装到默认位置
xmake install

# 安装到指定位置
xmake install -o /opt/kkquicklaunch

# 使用脚本安装
./build_xmake.sh install
```

## 🛠️ 开发工作流

### 日常开发
```bash
# 开发模式（debug + 运行）
xmake dev

# 或者
xmake config -m debug
xmake build
xmake run
```

### 代码检查
```bash
# 静态分析（如果配置了）
xmake check

# 格式检查
xmake format --check

# 代码检查
xmake lint
```

### 清理构建
```bash
# 清理构建文件
xmake clean

# 清理所有文件（包括配置）
xmake clean-all

# 或使用脚本
./build_xmake.sh clean
```

## 🔧 高级配置

### 自定义编译选项
```bash
# 启用额外警告
xmake config --cxxflags="-Wall -Wextra"

# 启用地址消毒器（调试内存问题）
xmake config -m debug --cxxflags="-fsanitize=address"

# 启用线程消毒器（调试并发问题）
xmake config -m debug --cxxflags="-fsanitize=thread"
```

### 交叉编译
```bash
# 为 ARM64 Linux 交叉编译
xmake config -p linux -a arm64 --toolchain=aarch64-linux-gnu

# 为 Windows 交叉编译（在 Linux 上）
xmake config -p windows -a x64 --toolchain=mingw
```

### 依赖管理
```bash
# 更新依赖
xmake require --update

# 清理依赖缓存
xmake require --clean

# 显示依赖信息
xmake require --info qt6widgets
```

## 📊 项目信息

### 查看项目状态
```bash
# 显示项目信息
xmake show

# 显示目标信息
xmake show -t kkquicklaunch

# 显示配置信息
xmake show -c
```

### 性能分析
```bash
# 构建时间分析
xmake build --profile

# 依赖分析
xmake graph

# 缓存统计
xmake cache --stats
```

## 🐛 故障排除

### 常见问题

#### Qt6 找不到
```bash
# 设置 Qt6 路径
export Qt6_DIR=/path/to/qt6
xmake config --qt=/path/to/qt6

# 或在 Windows 上
set Qt6_DIR=C:\Qt\6.5.0\msvc2019_64
xmake config --qt=C:\Qt\6.5.0\msvc2019_64
```

#### 编译错误
```bash
# 清理并重新配置
xmake clean-all
xmake config -m debug --verbose
xmake build --verbose
```

#### 链接错误
```bash
# 检查依赖
xmake show -t kkquicklaunch

# 重新安装依赖
xmake require --reinstall
```

### 调试构建过程
```bash
# 详细输出
xmake build --verbose

# 调试模式
xmake build --debug

# 诊断信息
xmake diagnose
```

## 📚 更多资源

- [XMake 官方文档](https://xmake.io/#/zh-cn/)
- [Qt6 文档](https://doc.qt.io/qt-6/)
- [项目 GitHub](https://github.com/your-repo/kkquicklaunch)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建 Pull Request

### 开发环境设置
```bash
# 克隆项目
git clone https://github.com/your-repo/kkquicklaunch.git
cd kkquicklaunch

# 安装开发依赖
xmake require --install

# 配置开发模式
xmake config -m debug

# 构建并运行
xmake build && xmake run
```
