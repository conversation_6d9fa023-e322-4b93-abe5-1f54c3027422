#pragma once

#include <QObject>
#include <QWidget>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QSoundEffect>
#include <QMediaPlayer>
#include <QAudioOutput>
#include <memory>

// 前置声明
class MainWindow;
class MiniWindow;
class TaskbarWindow;
class VoiceManager;
class ConfigManager;

/**
 * @brief 显示模式枚举
 */
enum class DisplayMode {
    Mini,           // 迷你模式 - 小窗口，基本功能
    Taskbar,        // 任务栏模式 - 集成到任务栏
    Full,           // 完整模式 - 完整功能窗口
    Voice,          // 语音模式 - 语音交互
    Hidden          // 隐藏模式 - 仅托盘
};

/**
 * @brief 动画类型
 */
enum class AnimationType {
    FadeIn,         // 淡入
    FadeOut,        // 淡出
    SlideIn,        // 滑入
    SlideOut,       // 滑出
    Scale,          // 缩放
    Bounce          // 弹跳
};

/**
 * @brief 声音效果类型
 */
enum class SoundEffect {
    Launch,         // 启动音效
    Search,         // 搜索音效
    Select,         // 选择音效
    Error,          // 错误音效
    Success,        // 成功音效
    Notification,   // 通知音效
    VoiceStart,     // 语音开始
    VoiceEnd        // 语音结束
};

/**
 * @brief 显示模式管理器
 * 
 * 负责管理不同的显示模式和用户界面状态，包括：
 * - 多种显示模式切换
 * - 窗口动画效果
 * - 声音效果管理
 * - 语音交互支持
 * - 自适应布局
 */
class DisplayModeManager : public QObject
{
    Q_OBJECT
    
    Q_PROPERTY(DisplayMode currentMode READ currentMode WRITE setCurrentMode NOTIFY currentModeChanged)
    Q_PROPERTY(bool animationsEnabled READ animationsEnabled WRITE setAnimationsEnabled NOTIFY animationsEnabledChanged)
    Q_PROPERTY(bool soundEnabled READ soundEnabled WRITE setSoundEnabled NOTIFY soundEnabledChanged)
    Q_PROPERTY(double opacity READ opacity WRITE setOpacity NOTIFY opacityChanged)

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit DisplayModeManager(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~DisplayModeManager();

    /**
     * @brief 初始化显示模式管理器
     * @return 是否成功
     */
    bool initialize();

    // 属性访问器
    DisplayMode currentMode() const;
    void setCurrentMode(DisplayMode mode);
    
    bool animationsEnabled() const;
    void setAnimationsEnabled(bool enabled);
    
    bool soundEnabled() const;
    void setSoundEnabled(bool enabled);
    
    double opacity() const;
    void setOpacity(double opacity);

    // 模式管理
    
    /**
     * @brief 切换到指定模式
     * @param mode 目标模式
     * @param animated 是否使用动画
     */
    Q_INVOKABLE void switchToMode(DisplayMode mode, bool animated = true);
    
    /**
     * @brief 切换到下一个模式
     */
    Q_INVOKABLE void switchToNextMode();
    
    /**
     * @brief 切换到上一个模式
     */
    Q_INVOKABLE void switchToPreviousMode();
    
    /**
     * @brief 临时显示模式
     * @param mode 临时模式
     * @param duration 显示时长（毫秒）
     */
    Q_INVOKABLE void showTemporaryMode(DisplayMode mode, int duration = 3000);
    
    /**
     * @brief 获取当前窗口
     * @return 当前活动窗口
     */
    QWidget* getCurrentWindow() const;
    
    /**
     * @brief 获取所有可用模式
     * @return 模式列表
     */
    QList<DisplayMode> getAvailableModes() const;

    // 动画管理
    
    /**
     * @brief 播放动画
     * @param widget 目标控件
     * @param type 动画类型
     * @param duration 动画时长
     */
    void playAnimation(QWidget *widget, AnimationType type, int duration = 300);
    
    /**
     * @brief 停止所有动画
     */
    void stopAllAnimations();
    
    /**
     * @brief 设置动画速度
     * @param speed 速度倍数 (0.1 - 3.0)
     */
    void setAnimationSpeed(double speed);

    // 声音管理
    
    /**
     * @brief 播放声音效果
     * @param effect 声音效果类型
     * @param volume 音量 (0.0 - 1.0)
     */
    void playSoundEffect(SoundEffect effect, double volume = 1.0);
    
    /**
     * @brief 设置主音量
     * @param volume 音量 (0.0 - 1.0)
     */
    void setMasterVolume(double volume);
    
    /**
     * @brief 加载声音主题
     * @param themeName 主题名称
     */
    void loadSoundTheme(const QString &themeName);

    // 语音功能
    
    /**
     * @brief 启用语音模式
     * @param enabled 是否启用
     */
    void setVoiceEnabled(bool enabled);
    
    /**
     * @brief 检查语音是否可用
     * @return 是否可用
     */
    bool isVoiceAvailable() const;
    
    /**
     * @brief 开始语音识别
     */
    Q_INVOKABLE void startVoiceRecognition();
    
    /**
     * @brief 停止语音识别
     */
    Q_INVOKABLE void stopVoiceRecognition();
    
    /**
     * @brief 语音播报文本
     * @param text 要播报的文本
     */
    Q_INVOKABLE void speakText(const QString &text);

    // 窗口管理
    
    /**
     * @brief 显示窗口
     * @param animated 是否使用动画
     */
    void showWindow(bool animated = true);
    
    /**
     * @brief 隐藏窗口
     * @param animated 是否使用动画
     */
    void hideWindow(bool animated = true);
    
    /**
     * @brief 切换窗口可见性
     */
    void toggleWindowVisibility();
    
    /**
     * @brief 设置窗口位置
     * @param mode 模式
     */
    void setWindowPosition(DisplayMode mode);
    
    /**
     * @brief 调整窗口大小
     * @param mode 模式
     */
    void resizeWindow(DisplayMode mode);

signals:
    /**
     * @brief 当前模式变更信号
     * @param mode 新模式
     * @param oldMode 旧模式
     */
    void currentModeChanged(DisplayMode mode, DisplayMode oldMode);
    
    /**
     * @brief 动画启用状态变更信号
     * @param enabled 是否启用
     */
    void animationsEnabledChanged(bool enabled);
    
    /**
     * @brief 声音启用状态变更信号
     * @param enabled 是否启用
     */
    void soundEnabledChanged(bool enabled);
    
    /**
     * @brief 透明度变更信号
     * @param opacity 透明度
     */
    void opacityChanged(double opacity);
    
    /**
     * @brief 窗口显示状态变更信号
     * @param visible 是否可见
     */
    void windowVisibilityChanged(bool visible);
    
    /**
     * @brief 语音识别结果信号
     * @param text 识别的文本
     * @param confidence 置信度
     */
    void voiceRecognitionResult(const QString &text, double confidence);
    
    /**
     * @brief 语音识别状态变更信号
     * @param active 是否活跃
     */
    void voiceRecognitionStateChanged(bool active);

private slots:
    /**
     * @brief 动画完成处理
     */
    void onAnimationFinished();
    
    /**
     * @brief 临时模式超时处理
     */
    void onTemporaryModeTimeout();
    
    /**
     * @brief 配置变更处理
     * @param key 配置键
     * @param value 配置值
     */
    void onConfigChanged(const QString &key, const QVariant &value);
    
    /**
     * @brief 语音识别结果处理
     * @param text 识别文本
     */
    void onVoiceRecognitionResult(const QString &text);

private:
    /**
     * @brief 创建窗口
     * @param mode 模式
     * @return 窗口指针
     */
    QWidget* createWindow(DisplayMode mode);
    
    /**
     * @brief 初始化声音效果
     */
    void initializeSoundEffects();
    
    /**
     * @brief 初始化语音管理器
     */
    void initializeVoiceManager();
    
    /**
     * @brief 加载配置
     */
    void loadConfiguration();
    
    /**
     * @brief 保存配置
     */
    void saveConfiguration();
    
    /**
     * @brief 获取模式名称
     * @param mode 模式
     * @return 模式名称
     */
    QString getModeDisplayName(DisplayMode mode) const;
    
    /**
     * @brief 获取声音文件路径
     * @param effect 声音效果
     * @return 文件路径
     */
    QString getSoundFilePath(SoundEffect effect) const;

private:
    // 服务引用
    std::shared_ptr<ConfigManager> m_configManager;
    std::unique_ptr<VoiceManager> m_voiceManager;
    
    // 窗口管理
    std::unique_ptr<MainWindow> m_mainWindow;
    std::unique_ptr<MiniWindow> m_miniWindow;
    std::unique_ptr<TaskbarWindow> m_taskbarWindow;
    QWidget *m_currentWindow = nullptr;
    
    // 状态管理
    DisplayMode m_currentMode = DisplayMode::Full;
    DisplayMode m_previousMode = DisplayMode::Full;
    bool m_animationsEnabled = true;
    bool m_soundEnabled = true;
    bool m_voiceEnabled = false;
    double m_opacity = 1.0;
    double m_animationSpeed = 1.0;
    double m_masterVolume = 0.8;
    
    // 动画管理
    QList<QPropertyAnimation*> m_activeAnimations;
    QTimer *m_temporaryModeTimer;
    
    // 声音管理
    std::unique_ptr<QMediaPlayer> m_mediaPlayer;
    std::unique_ptr<QAudioOutput> m_audioOutput;
    QHash<SoundEffect, QString> m_soundFiles;
    QString m_currentSoundTheme = "default";
    
    // 常量
    static const int DEFAULT_ANIMATION_DURATION = 300;
    static const int TEMPORARY_MODE_DURATION = 3000;
    static const double DEFAULT_OPACITY = 0.95;
};
