#include "MiniWindow.h"
#include "presentation/viewmodels/LaunchItemViewModel.h"
#include "presentation/managers/DisplayModeManager.h"
#include "business/managers/ConfigManager.h"
#include <QApplication>
#include <QScreen>
#include <QKeyEvent>
#include <QMouseEvent>
#include <QPainter>
#include <QStyleOption>
#include <QLoggingCategory>

Q_LOGGING_CATEGORY(lcMiniWindow, "miniwindow")

MiniWindow::MiniWindow(QWidget *parent)
    : QWidget(parent)
    , m_searchDelayTimer(new QTimer(this))
    , m_autoHideTimer(new QTimer(this))
    , m_fadeAnimation(new QPropertyAnimation(this, "windowOpacity", this))
{
    // 设置窗口属性
    setupWindowProperties();
    
    // 设置定时器
    m_searchDelayTimer->setSingleShot(true);
    m_searchDelayTimer->setInterval(SEARCH_DELAY);
    connect(m_searchDelayTimer, &QTimer::timeout, this, &MiniWindow::onSearchDelayTimeout);
    
    m_autoHideTimer->setSingleShot(true);
    m_autoHideTimer->setInterval(AUTO_HIDE_DELAY);
    connect(m_autoHideTimer, &QTimer::timeout, this, &MiniWindow::onAutoHideTimeout);
    
    // 初始化UI
    setupUI();
    setupConnections();
    setupStyles();
    
    // 创建阴影效果
    createShadowEffect();
    
    // 加载设置
    loadSettings();
    
    qCDebug(lcMiniWindow) << "MiniWindow created";
}

MiniWindow::~MiniWindow()
{
    saveSettings();
    qCDebug(lcMiniWindow) << "MiniWindow destroyed";
}

bool MiniWindow::autoHide() const
{
    return m_autoHide;
}

void MiniWindow::setAutoHide(bool enabled)
{
    if (m_autoHide == enabled) {
        return;
    }
    
    m_autoHide = enabled;
    emit autoHideChanged(enabled);
    
    if (!enabled) {
        m_autoHideTimer->stop();
    }
    
    qCDebug(lcMiniWindow) << "Auto hide" << (enabled ? "enabled" : "disabled");
}

int MiniWindow::maxResults() const
{
    return m_maxResults;
}

void MiniWindow::setMaxResults(int count)
{
    count = qBound(1, count, 20);
    
    if (m_maxResults == count) {
        return;
    }
    
    m_maxResults = count;
    emit maxResultsChanged(count);
    
    updateResultsList();
    
    qCDebug(lcMiniWindow) << "Max results set to" << count;
}

bool MiniWindow::alwaysOnTop() const
{
    return m_alwaysOnTop;
}

void MiniWindow::setAlwaysOnTop(bool enabled)
{
    if (m_alwaysOnTop == enabled) {
        return;
    }
    
    m_alwaysOnTop = enabled;
    emit alwaysOnTopChanged(enabled);
    
    // 更新窗口标志
    Qt::WindowFlags flags = windowFlags();
    if (enabled) {
        flags |= Qt::WindowStaysOnTopHint;
    } else {
        flags &= ~Qt::WindowStaysOnTopHint;
    }
    setWindowFlags(flags);
    
    qCDebug(lcMiniWindow) << "Always on top" << (enabled ? "enabled" : "disabled");
}

void MiniWindow::showAndActivate()
{
    show();
    raise();
    activateWindow();
    focusSearchBox();
    
    // 启动淡入动画
    startFadeInAnimation();
    
    // 重置自动隐藏定时器
    resetAutoHideTimer();
    
    emit visibilityChanged(true);
    qCDebug(lcMiniWindow) << "Window shown and activated";
}

void MiniWindow::hideWindow()
{
    // 启动淡出动画
    startFadeOutAnimation();
    
    emit visibilityChanged(false);
    qCDebug(lcMiniWindow) << "Window hidden";
}

void MiniWindow::toggleVisibility()
{
    if (isVisible() && !isMinimized()) {
        hideWindow();
    } else {
        showAndActivate();
    }
}

void MiniWindow::focusSearchBox()
{
    if (m_searchEdit) {
        m_searchEdit->setFocus();
        m_searchEdit->selectAll();
    }
}

void MiniWindow::clearSearch()
{
    if (m_searchEdit) {
        m_searchEdit->clear();
    }
    
    m_selectedIndex = -1;
    updateResultsList();
}

void MiniWindow::launchSelectedItem()
{
    if (m_selectedIndex >= 0 && m_selectedIndex < m_searchResults.size()) {
        const LaunchItem &item = m_searchResults[m_selectedIndex];
        
        // 这里应该通过LaunchManager启动项目
        // 暂时发送信号
        emit itemLaunched(item.id, true);
        
        // 如果启动成功，隐藏窗口
        hideWindow();
        
        qCDebug(lcMiniWindow) << "Launched item:" << item.name;
    }
}

void MiniWindow::setSearchText(const QString &text)
{
    if (m_searchEdit) {
        m_searchEdit->setText(text);
    }
}

QString MiniWindow::getSearchText() const
{
    return m_searchEdit ? m_searchEdit->text() : QString();
}

void MiniWindow::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    
    // 设置窗口位置
    setWindowPosition();
    
    emit visibilityChanged(true);
}

void MiniWindow::hideEvent(QHideEvent *event)
{
    QWidget::hideEvent(event);
    
    // 停止定时器
    m_autoHideTimer->stop();
    
    emit visibilityChanged(false);
}

void MiniWindow::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
    case Qt::Key_Escape:
        hideWindow();
        break;
        
    case Qt::Key_Return:
    case Qt::Key_Enter:
        launchSelectedItem();
        break;
        
    case Qt::Key_Up:
        if (m_selectedIndex > 0) {
            m_selectedIndex--;
            updateSelection(m_selectedIndex);
        }
        break;
        
    case Qt::Key_Down:
        if (m_selectedIndex < m_searchResults.size() - 1) {
            m_selectedIndex++;
            updateSelection(m_selectedIndex);
        }
        break;
        
    case Qt::Key_Tab:
        // 切换到完整模式
        emit switchToFullModeRequested();
        break;
        
    default:
        QWidget::keyPressEvent(event);
        break;
    }
    
    // 重置自动隐藏定时器
    resetAutoHideTimer();
}

void MiniWindow::focusOutEvent(QFocusEvent *event)
{
    QWidget::focusOutEvent(event);
    
    // 如果启用自动隐藏且失去焦点，启动定时器
    if (m_autoHide && !hasFocus()) {
        resetAutoHideTimer();
    }
}

void MiniWindow::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 绘制圆角背景
    QStyleOption opt;
    opt.initFrom(this);
    style()->drawPrimitive(QStyle::PE_Widget, &opt, &painter, this);
}

void MiniWindow::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = true;
        m_dragStartPosition = event->globalPosition().toPoint() - frameGeometry().topLeft();
    }
    
    QWidget::mousePressEvent(event);
}

void MiniWindow::mouseMoveEvent(QMouseEvent *event)
{
    if (m_dragging && (event->buttons() & Qt::LeftButton)) {
        move(event->globalPosition().toPoint() - m_dragStartPosition);
    }
    
    QWidget::mouseMoveEvent(event);
}

void MiniWindow::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = false;
    }
    
    QWidget::mouseReleaseEvent(event);
}

void MiniWindow::enterEvent(QEnterEvent *event)
{
    QWidget::enterEvent(event);
    
    // 鼠标进入时停止自动隐藏定时器
    m_autoHideTimer->stop();
}

void MiniWindow::leaveEvent(QEvent *event)
{
    QWidget::leaveEvent(event);
    
    // 鼠标离开时重启自动隐藏定时器
    resetAutoHideTimer();
}

void MiniWindow::onSearchTextChanged(const QString &text)
{
    m_currentSearchText = text;
    
    // 重启搜索延迟定时器
    m_searchDelayTimer->stop();
    m_searchDelayTimer->start();
    
    // 重置自动隐藏定时器
    resetAutoHideTimer();
    
    emit searchTextChanged(text);
}

void MiniWindow::onSearchDelayTimeout()
{
    // 执行搜索
    if (m_viewModel) {
        m_viewModel->setSearchText(m_currentSearchText);
    }
    
    qCDebug(lcMiniWindow) << "Search executed:" << m_currentSearchText;
}

void MiniWindow::onItemActivated(int index)
{
    if (index >= 0 && index < m_searchResults.size()) {
        m_selectedIndex = index;
        launchSelectedItem();
    }
}

void MiniWindow::onItemSelectionChanged(int index)
{
    m_selectedIndex = index;
    updateSelection(index);
}

void MiniWindow::onAutoHideTimeout()
{
    if (m_autoHide && isVisible()) {
        hideWindow();
        qCDebug(lcMiniWindow) << "Auto hide triggered";
    }
}

void MiniWindow::onExpandButtonClicked()
{
    emit switchToFullModeRequested();
}

void MiniWindow::onSettingsButtonClicked()
{
    // TODO: 显示设置菜单
    qCDebug(lcMiniWindow) << "Settings button clicked";
}

void MiniWindow::onConfigChanged(const QString &key, const QVariant &value)
{
    if (key == "mini.auto_hide") {
        setAutoHide(value.toBool());
    } else if (key == "mini.max_results") {
        setMaxResults(value.toInt());
    } else if (key == "mini.always_on_top") {
        setAlwaysOnTop(value.toBool());
    } else if (key == "ui.theme") {
        applyTheme(value.toString());
    }
    
    qCDebug(lcMiniWindow) << "Config changed:" << key << "=" << value;
}

void MiniWindow::setupUI()
{
    setFixedSize(WINDOW_WIDTH, WINDOW_HEIGHT);

    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(8);

    // 创建各个区域
    createSearchArea();
    createResultsList();
    createToolBar();

    qCDebug(lcMiniWindow) << "UI setup completed";
}

void MiniWindow::createSearchArea()
{
    // 创建搜索布局
    m_searchLayout = new QHBoxLayout();

    // 创建搜索框
    m_searchEdit = new QLineEdit(this);
    m_searchEdit->setPlaceholderText("搜索应用程序...");
    m_searchEdit->setClearButtonEnabled(true);
    m_searchEdit->setFixedHeight(32);

    // 添加到布局
    m_searchLayout->addWidget(m_searchEdit);
    m_mainLayout->addLayout(m_searchLayout);
}

void MiniWindow::createResultsList()
{
    m_resultsList = new QListWidget(this);
    m_resultsList->setAlternatingRowColors(true);
    m_resultsList->setSelectionMode(QAbstractItemView::SingleSelection);
    m_resultsList->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_resultsList->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    // 设置项目高度
    m_resultsList->setUniformItemSizes(true);

    m_mainLayout->addWidget(m_resultsList);
}

void MiniWindow::createToolBar()
{
    // 创建工具栏布局
    m_toolLayout = new QHBoxLayout();

    // 创建状态标签
    m_statusLabel = new QLabel("就绪", this);
    m_statusLabel->setStyleSheet("color: #666; font-size: 11px;");

    // 创建展开按钮
    m_expandButton = new QPushButton("⤢", this);
    m_expandButton->setFixedSize(24, 24);
    m_expandButton->setToolTip("切换到完整模式");

    // 创建设置按钮
    m_settingsButton = new QPushButton("⚙", this);
    m_settingsButton->setFixedSize(24, 24);
    m_settingsButton->setToolTip("设置");

    // 添加到布局
    m_toolLayout->addWidget(m_statusLabel);
    m_toolLayout->addStretch();
    m_toolLayout->addWidget(m_expandButton);
    m_toolLayout->addWidget(m_settingsButton);

    m_mainLayout->addLayout(m_toolLayout);
}

void MiniWindow::setupConnections()
{
    // 搜索框连接
    connect(m_searchEdit, &QLineEdit::textChanged, this, &MiniWindow::onSearchTextChanged);

    // 结果列表连接
    connect(m_resultsList, &QListWidget::itemActivated, this, [this](QListWidgetItem *item) {
        int index = m_resultsList->row(item);
        onItemActivated(index);
    });

    connect(m_resultsList, &QListWidget::currentRowChanged, this, &MiniWindow::onItemSelectionChanged);

    // 按钮连接
    connect(m_expandButton, &QPushButton::clicked, this, &MiniWindow::onExpandButtonClicked);
    connect(m_settingsButton, &QPushButton::clicked, this, &MiniWindow::onSettingsButtonClicked);

    // 配置管理器连接
    if (m_configManager) {
        connect(m_configManager.get(), &ConfigManager::valueChanged,
                this, &MiniWindow::onConfigChanged);
    }
}

void MiniWindow::setupStyles()
{
    // 应用默认主题
    applyTheme("default");
}

void MiniWindow::applyTheme(const QString &themeName)
{
    QString styleSheet;

    if (themeName == "dark") {
        styleSheet = R"(
            MiniWindow {
                background-color: #2b2b2b;
                border: 1px solid #555;
                border-radius: 8px;
            }
            QLineEdit {
                background-color: #3c3c3c;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 6px;
                color: #ffffff;
                font-size: 13px;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
            QListWidget {
                background-color: #3c3c3c;
                border: 1px solid #555;
                border-radius: 4px;
                color: #ffffff;
                outline: none;
            }
            QListWidget::item {
                padding: 6px;
                border-bottom: 1px solid #444;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
            }
            QListWidget::item:hover {
                background-color: #404040;
            }
            QPushButton {
                background-color: #4a4a4a;
                border: 1px solid #666;
                border-radius: 3px;
                color: #ffffff;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a5a5a;
            }
            QPushButton:pressed {
                background-color: #3a3a3a;
            }
        )";
    } else {
        styleSheet = R"(
            MiniWindow {
                background-color: #ffffff;
                border: 1px solid #ccc;
                border-radius: 8px;
            }
            QLineEdit {
                background-color: #ffffff;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 6px;
                font-size: 13px;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
            QListWidget {
                background-color: #ffffff;
                border: 1px solid #ccc;
                border-radius: 4px;
                outline: none;
            }
            QListWidget::item {
                padding: 6px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #f0f0f0;
            }
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        )";
    }

    setStyleSheet(styleSheet);
    qCDebug(lcMiniWindow) << "Theme applied:" << themeName;
}

void MiniWindow::setupWindowProperties()
{
    // 设置窗口标志
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_ShowWithoutActivating, false);

    // 设置焦点策略
    setFocusPolicy(Qt::StrongFocus);
}

void MiniWindow::setWindowPosition()
{
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();

    int x = (screenGeometry.width() - width()) / 2;
    int y = screenGeometry.height() / 3;

    move(x, y);
}

void MiniWindow::updateResultsList()
{
    m_resultsList->clear();

    // 这里应该从ViewModel获取搜索结果
    // 暂时使用模拟数据
    for (int i = 0; i < qMin(m_maxResults, m_searchResults.size()); ++i) {
        const LaunchItem &item = m_searchResults[i];

        QListWidgetItem *listItem = new QListWidgetItem(item.name);
        listItem->setToolTip(item.path);
        listItem->setData(Qt::UserRole, item.id);

        m_resultsList->addItem(listItem);
    }

    // 选择第一项
    if (m_resultsList->count() > 0) {
        m_selectedIndex = 0;
        m_resultsList->setCurrentRow(0);
    } else {
        m_selectedIndex = -1;
    }
}

void MiniWindow::updateSelection(int index)
{
    if (index >= 0 && index < m_resultsList->count()) {
        m_resultsList->setCurrentRow(index);
        m_selectedIndex = index;
    }
}

void MiniWindow::startFadeInAnimation()
{
    m_fadeAnimation->setDuration(ANIMATION_DURATION);
    m_fadeAnimation->setStartValue(0.0);
    m_fadeAnimation->setEndValue(1.0);
    m_fadeAnimation->start();
}

void MiniWindow::startFadeOutAnimation()
{
    m_fadeAnimation->setDuration(ANIMATION_DURATION);
    m_fadeAnimation->setStartValue(windowOpacity());
    m_fadeAnimation->setEndValue(0.0);

    connect(m_fadeAnimation, &QPropertyAnimation::finished, this, [this]() {
        hide();
        disconnect(m_fadeAnimation, &QPropertyAnimation::finished, this, nullptr);
    });

    m_fadeAnimation->start();
}

void MiniWindow::resetAutoHideTimer()
{
    if (m_autoHide && isVisible()) {
        m_autoHideTimer->start();
    }
}

void MiniWindow::loadSettings()
{
    if (m_configManager) {
        setAutoHide(m_configManager->getBool("mini.auto_hide", true));
        setMaxResults(m_configManager->getInt("mini.max_results", 8));
        setAlwaysOnTop(m_configManager->getBool("mini.always_on_top", true));
    }
}

void MiniWindow::saveSettings()
{
    if (m_configManager) {
        m_configManager->setValue("mini.auto_hide", m_autoHide);
        m_configManager->setValue("mini.max_results", m_maxResults);
        m_configManager->setValue("mini.always_on_top", m_alwaysOnTop);
        m_configManager->sync();
    }
}

void MiniWindow::createShadowEffect()
{
    m_shadowEffect = new QGraphicsDropShadowEffect(this);
    m_shadowEffect->setBlurRadius(SHADOW_BLUR_RADIUS);
    m_shadowEffect->setColor(QColor(0, 0, 0, 80));
    m_shadowEffect->setOffset(0, 2);

    setGraphicsEffect(m_shadowEffect);
}
