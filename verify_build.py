#!/usr/bin/env python3
"""
KK QuickLaunch 项目构建验证脚本
验证项目的完整性和构建能力
"""

import os
import sys
import subprocess
from pathlib import Path
import json
from datetime import datetime

class BuildVerifier:
    def __init__(self):
        self.project_root = Path('.')
        self.errors = []
        self.warnings = []
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'project_name': 'KK QuickLaunch',
            'version': '1.0.0',
            'checks': {}
        }
    
    def log_error(self, message):
        """记录错误"""
        self.errors.append(message)
        print(f"❌ ERROR: {message}")
    
    def log_warning(self, message):
        """记录警告"""
        self.warnings.append(message)
        print(f"⚠️  WARNING: {message}")
    
    def log_success(self, message):
        """记录成功"""
        print(f"✅ {message}")
    
    def log_info(self, message):
        """记录信息"""
        print(f"ℹ️  {message}")
    
    def check_file_exists(self, file_path, required=True):
        """检查文件是否存在"""
        full_path = self.project_root / file_path
        exists = full_path.exists()
        
        if exists:
            self.log_success(f"Found: {file_path}")
        elif required:
            self.log_error(f"Missing required file: {file_path}")
        else:
            self.log_warning(f"Missing optional file: {file_path}")
        
        return exists
    
    def check_directory_structure(self):
        """检查目录结构"""
        self.log_info("Checking directory structure...")
        
        required_dirs = [
            'src',
            'src/application',
            'src/business',
            'src/business/entities',
            'src/business/managers',
            'src/data',
            'src/data/repositories',
            'src/infrastructure',
            'src/infrastructure/database',
            'src/infrastructure/utils',
            'src/presentation',
            'src/presentation/views',
            'src/presentation/viewmodels',
            'tests',
            'tests/unit',
            'docs',
            'resources'
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if full_path.exists() and full_path.is_dir():
                self.log_success(f"Directory exists: {dir_path}")
            else:
                missing_dirs.append(dir_path)
                self.log_error(f"Missing directory: {dir_path}")
        
        self.results['checks']['directory_structure'] = {
            'status': 'pass' if not missing_dirs else 'fail',
            'missing_directories': missing_dirs
        }
        
        return len(missing_dirs) == 0
    
    def check_core_files(self):
        """检查核心文件"""
        self.log_info("Checking core files...")
        
        core_files = [
            # 构建文件
            ('CMakeLists.txt', True),
            ('build.sh', False),
            ('build.bat', False),
            
            # 应用程序入口
            ('src/main.cpp', True),
            
            # 应用程序层
            ('src/application/Application.h', True),
            ('src/application/Application.cpp', True),
            ('src/application/ServiceContainer.h', True),
            ('src/application/ServiceContainer.cpp', True),
            
            # 基础设施层
            ('src/infrastructure/utils/Logger.h', True),
            ('src/infrastructure/utils/Logger.cpp', True),
            ('src/infrastructure/database/DatabaseManager.h', True),
            ('src/infrastructure/database/DatabaseManager.cpp', True),
            
            # 业务实体
            ('src/business/entities/LaunchItem.h', True),
            ('src/business/entities/LaunchItem.cpp', True),
            
            # 数据访问层
            ('src/data/repositories/LaunchItemRepository.h', True),
            ('src/data/repositories/LaunchItemRepository.cpp', True),
            
            # 业务逻辑层
            ('src/business/managers/LaunchManager.h', True),
            ('src/business/managers/LaunchManager.cpp', True),
            ('src/business/managers/ConfigManager.h', True),
            ('src/business/managers/ConfigManager.cpp', True),
            
            # 表示层
            ('src/presentation/views/MainWindow.h', True),
            ('src/presentation/views/MainWindow.cpp', True),
            ('src/presentation/views/TrayIcon.h', True),
            ('src/presentation/views/TrayIcon.cpp', True),
            ('src/presentation/viewmodels/BaseViewModel.h', True),
            ('src/presentation/viewmodels/BaseViewModel.cpp', True),
            ('src/presentation/viewmodels/LaunchItemViewModel.h', True),
            
            # 测试文件
            ('tests/CMakeLists.txt', True),
            ('tests/unit/test_ServiceContainer.cpp', True),
            ('tests/unit/test_LaunchManager.cpp', True),
            
            # 文档
            ('README.md', True),
            ('docs/项目架构总结.md', False),
            ('docs/项目完成总结.md', False),
        ]
        
        missing_files = []
        for file_path, required in core_files:
            if not self.check_file_exists(file_path, required):
                if required:
                    missing_files.append(file_path)
        
        self.results['checks']['core_files'] = {
            'status': 'pass' if not missing_files else 'fail',
            'missing_files': missing_files
        }
        
        return len(missing_files) == 0
    
    def check_cmake_configuration(self):
        """检查CMake配置"""
        self.log_info("Checking CMake configuration...")
        
        cmake_file = self.project_root / 'CMakeLists.txt'
        if not cmake_file.exists():
            self.log_error("CMakeLists.txt not found")
            return False
        
        try:
            content = cmake_file.read_text(encoding='utf-8')
            
            # 检查必要的CMake配置
            required_configs = [
                'cmake_minimum_required',
                'project(',
                'find_package(Qt6',
                'add_executable',
                'target_link_libraries'
            ]
            
            missing_configs = []
            for config in required_configs:
                if config not in content:
                    missing_configs.append(config)
                    self.log_error(f"Missing CMake configuration: {config}")
                else:
                    self.log_success(f"Found CMake configuration: {config}")
            
            self.results['checks']['cmake_configuration'] = {
                'status': 'pass' if not missing_configs else 'fail',
                'missing_configurations': missing_configs
            }
            
            return len(missing_configs) == 0
            
        except Exception as e:
            self.log_error(f"Error reading CMakeLists.txt: {e}")
            return False
    
    def check_code_quality(self):
        """检查代码质量"""
        self.log_info("Checking code quality...")
        
        # 统计代码行数
        cpp_files = list(self.project_root.rglob('*.cpp'))
        h_files = list(self.project_root.rglob('*.h'))
        
        total_lines = 0
        total_files = len(cpp_files) + len(h_files)
        
        for file_path in cpp_files + h_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = len(f.readlines())
                    total_lines += lines
            except:
                continue
        
        self.log_info(f"Code statistics: {total_files} files, {total_lines} lines")
        
        # 检查基本的代码质量指标
        quality_issues = []
        
        # 检查是否有空的源文件
        for file_path in cpp_files:
            try:
                if file_path.stat().st_size < 100:  # 小于100字节认为可能是空文件
                    quality_issues.append(f"Possibly empty source file: {file_path}")
            except:
                continue
        
        self.results['checks']['code_quality'] = {
            'status': 'pass' if not quality_issues else 'warning',
            'total_files': total_files,
            'total_lines': total_lines,
            'issues': quality_issues
        }
        
        for issue in quality_issues:
            self.log_warning(issue)
        
        return True
    
    def check_dependencies(self):
        """检查依赖项"""
        self.log_info("Checking dependencies...")
        
        # 检查Qt6是否可用（简单检查）
        try:
            result = subprocess.run(['qmake', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log_success("Qt development tools found")
                qt_available = True
            else:
                self.log_warning("Qt development tools not found in PATH")
                qt_available = False
        except:
            self.log_warning("Cannot check Qt availability")
            qt_available = False
        
        # 检查CMake是否可用
        try:
            result = subprocess.run(['cmake', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log_success("CMake found")
                cmake_available = True
            else:
                self.log_warning("CMake not found in PATH")
                cmake_available = False
        except:
            self.log_warning("Cannot check CMake availability")
            cmake_available = False
        
        self.results['checks']['dependencies'] = {
            'status': 'pass' if qt_available and cmake_available else 'warning',
            'qt_available': qt_available,
            'cmake_available': cmake_available
        }
        
        return True
    
    def generate_report(self):
        """生成验证报告"""
        self.log_info("Generating verification report...")
        
        # 计算总体状态
        failed_checks = sum(1 for check in self.results['checks'].values() 
                          if check['status'] == 'fail')
        warning_checks = sum(1 for check in self.results['checks'].values() 
                           if check['status'] == 'warning')
        
        if failed_checks > 0:
            overall_status = 'fail'
        elif warning_checks > 0:
            overall_status = 'warning'
        else:
            overall_status = 'pass'
        
        self.results['overall_status'] = overall_status
        self.results['summary'] = {
            'total_errors': len(self.errors),
            'total_warnings': len(self.warnings),
            'failed_checks': failed_checks,
            'warning_checks': warning_checks
        }
        
        # 保存报告
        report_file = self.project_root / 'build_verification_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        self.log_info(f"Report saved to: {report_file}")
        
        return overall_status
    
    def run_verification(self):
        """运行完整验证"""
        print("🚀 KK QuickLaunch Build Verification")
        print("=" * 50)
        
        # 执行各项检查
        checks = [
            self.check_directory_structure,
            self.check_core_files,
            self.check_cmake_configuration,
            self.check_code_quality,
            self.check_dependencies
        ]
        
        for check in checks:
            try:
                check()
                print()
            except Exception as e:
                self.log_error(f"Check failed with exception: {e}")
                print()
        
        # 生成报告
        overall_status = self.generate_report()
        
        # 输出总结
        print("📊 Verification Summary")
        print("-" * 30)
        print(f"Overall Status: {overall_status.upper()}")
        print(f"Errors: {len(self.errors)}")
        print(f"Warnings: {len(self.warnings)}")
        
        if self.errors:
            print("\n❌ Errors:")
            for error in self.errors:
                print(f"  - {error}")
        
        if self.warnings:
            print("\n⚠️  Warnings:")
            for warning in self.warnings:
                print(f"  - {warning}")
        
        if overall_status == 'pass':
            print("\n🎉 Project verification passed! Ready for build.")
            return 0
        elif overall_status == 'warning':
            print("\n⚠️  Project verification passed with warnings.")
            return 0
        else:
            print("\n❌ Project verification failed. Please fix the errors.")
            return 1

def main():
    verifier = BuildVerifier()
    return verifier.run_verification()

if __name__ == '__main__':
    sys.exit(main())
