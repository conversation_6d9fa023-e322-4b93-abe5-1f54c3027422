syntax = "v1"

// ============================================================================
// 配置相关类型定义
// ============================================================================

// 配置同步请求
type SyncConfigReq {
    DeviceId      string `json:"device_id" validate:"required"`           // 设备ID
    ConfigVersion string `json:"config_version,optional"`                 // 当前配置版本
    LastSyncTime  string `json:"last_sync_time,optional"`                 // 上次同步时间
    Changes       struct {
        LaunchItems struct {
            Added   []LaunchItem `json:"added,optional"`   // 新增的启动项
            Updated []LaunchItem `json:"updated,optional"` // 更新的启动项
            Deleted []int64      `json:"deleted,optional"` // 删除的启动项ID
        } `json:"launch_items,optional"`
        Categories struct {
            Added   []Category `json:"added,optional"`   // 新增的分类
            Updated []Category `json:"updated,optional"` // 更新的分类
            Deleted []int64    `json:"deleted,optional"` // 删除的分类ID
        } `json:"categories,optional"`
        Settings struct {
            AppSettings    []ConfigItem `json:"app_settings,optional"`    // 应用设置变更
            HotkeySettings []HotkeyItem `json:"hotkey_settings,optional"` // 快捷键设置变更
        } `json:"settings,optional"`
        FileAssociations struct {
            Added   []FileAssociation `json:"added,optional"`   // 新增的文件关联
            Updated []FileAssociation `json:"updated,optional"` // 更新的文件关联
            Deleted []int64           `json:"deleted,optional"` // 删除的文件关联ID
        } `json:"file_associations,optional"`
    } `json:"changes,optional"` // 配置变更
    Metadata struct {
        ClientVersion string `json:"client_version,optional"` // 客户端版本
        Platform      string `json:"platform,optional"`      // 操作系统平台
        Timestamp     string `json:"timestamp"`               // 同步时间戳
        Checksum      string `json:"checksum,optional"`       // 数据校验和
    } `json:"metadata"`
}

// 配置同步响应
type SyncConfigResp {
    BaseResp
    Data struct {
        ConfigVersion string `json:"config_version"`         // 新的配置版本
        LastModified  string `json:"last_modified"`          // 最后修改时间
        SyncResult    string `json:"sync_result"`            // 同步结果: success/conflict/partial
        Changes       struct {
            LaunchItems struct {
                Added   []LaunchItem `json:"added,optional"`   // 服务器端新增的启动项
                Updated []LaunchItem `json:"updated,optional"` // 服务器端更新的启动项
                Deleted []int64      `json:"deleted,optional"` // 服务器端删除的启动项ID
            } `json:"launch_items,optional"`
            Categories struct {
                Added   []Category `json:"added,optional"`   // 服务器端新增的分类
                Updated []Category `json:"updated,optional"` // 服务器端更新的分类
                Deleted []int64    `json:"deleted,optional"` // 服务器端删除的分类ID
            } `json:"categories,optional"`
            Settings struct {
                AppSettings    []ConfigItem `json:"app_settings,optional"`    // 应用设置变更
                HotkeySettings []HotkeyItem `json:"hotkey_settings,optional"` // 快捷键设置变更
            } `json:"settings,optional"`
            FileAssociations struct {
                Added   []FileAssociation `json:"added,optional"`   // 新增的文件关联
                Updated []FileAssociation `json:"updated,optional"` // 更新的文件关联
                Deleted []int64           `json:"deleted,optional"` // 删除的文件关联ID
            } `json:"file_associations,optional"`
        } `json:"changes,optional"` // 服务器端变更
        Conflicts []ConfigConflict `json:"conflicts,optional"` // 冲突列表
        NextSyncTime string `json:"next_sync_time,optional"`   // 下次同步时间
    } `json:"data"`
}

// 获取配置请求
type GetConfigReq {
    DeviceId      string `form:"device_id" validate:"required"`           // 设备ID
    LastSyncTime  string `form:"last_sync_time,optional"`                 // 上次同步时间
    ConfigVersion string `form:"config_version,optional"`                 // 配置版本
    IncludeDeleted bool  `form:"include_deleted,optional"`                // 是否包含已删除项
    Incremental   bool   `form:"incremental,optional"`                    // 是否增量同步
}

// 获取配置响应
type GetConfigResp {
    BaseResp
    Data struct {
        ConfigVersion string `json:"config_version"`         // 配置版本
        LastModified  string `json:"last_modified"`          // 最后修改时间
        IsIncremental bool   `json:"is_incremental"`         // 是否增量数据
        FullConfig    struct {
            LaunchItems      []LaunchItem      `json:"launch_items,optional"`      // 所有启动项
            Categories       []Category        `json:"categories,optional"`        // 所有分类
            AppSettings      []ConfigItem      `json:"app_settings,optional"`      // 应用设置
            HotkeySettings   []HotkeyItem      `json:"hotkey_settings,optional"`   // 快捷键设置
            FileAssociations []FileAssociation `json:"file_associations,optional"` // 文件关联
        } `json:"full_config,optional"` // 完整配置(首次同步)
        Changes struct {
            LaunchItems struct {
                Added   []LaunchItem `json:"added,optional"`   // 新增项
                Updated []LaunchItem `json:"updated,optional"` // 更新项
                Deleted []int64      `json:"deleted,optional"` // 删除项ID
            } `json:"launch_items,optional"`
            Categories struct {
                Added   []Category `json:"added,optional"`   // 新增分类
                Updated []Category `json:"updated,optional"` // 更新分类
                Deleted []int64    `json:"deleted,optional"` // 删除分类ID
            } `json:"categories,optional"`
            Settings struct {
                AppSettings    []ConfigItem `json:"app_settings,optional"`    // 应用设置变更
                HotkeySettings []HotkeyItem `json:"hotkey_settings,optional"` // 快捷键设置变更
            } `json:"settings,optional"`
            FileAssociations struct {
                Added   []FileAssociation `json:"added,optional"`   // 新增文件关联
                Updated []FileAssociation `json:"updated,optional"` // 更新文件关联
                Deleted []int64           `json:"deleted,optional"` // 删除文件关联ID
            } `json:"file_associations,optional"`
        } `json:"changes,optional"` // 增量变更
        Statistics struct {
            TotalItems       int64 `json:"total_items"`        // 总启动项数
            TotalCategories  int64 `json:"total_categories"`   // 总分类数
            TotalSettings    int64 `json:"total_settings"`     // 总设置项数
            LastSyncDeviceId string `json:"last_sync_device_id"` // 最后同步设备ID
            SyncCount        int64 `json:"sync_count"`         // 同步次数
        } `json:"statistics"`
    } `json:"data"`
}

// 配置冲突
type ConfigConflict {
    Id          string `json:"id"`           // 冲突ID
    Type        string `json:"type"`         // 冲突类型: launch_item/category/setting
    ItemId      int64  `json:"item_id"`      // 冲突项ID
    Field       string `json:"field"`        // 冲突字段
    LocalValue  interface{} `json:"local_value"`  // 本地值
    ServerValue interface{} `json:"server_value"` // 服务器值
    LocalTime   string `json:"local_time"`   // 本地修改时间
    ServerTime  string `json:"server_time"`  // 服务器修改时间
    Severity    string `json:"severity"`     // 冲突严重程度: low/medium/high
    Description string `json:"description"`  // 冲突描述
    Suggestions []string `json:"suggestions,optional"` // 解决建议
}

// 解决配置冲突请求
type ResolveConflictReq {
    ConflictId   string `json:"conflict_id" validate:"required"`          // 冲突ID
    Resolution   string `json:"resolution" validate:"required,oneof=local server merge custom"` // 解决策略
    CustomValue  interface{} `json:"custom_value,optional"`               // 自定义值(custom策略)
    ApplyToAll   bool   `json:"apply_to_all,optional"`                    // 是否应用到所有同类冲突
    MergeStrategy string `json:"merge_strategy,optional"`                 // 合并策略(merge策略)
}

// 配置历史版本
type ConfigVersion {
    Version     string `json:"version"`      // 版本号
    Timestamp   string `json:"timestamp"`    // 创建时间
    DeviceId    string `json:"device_id"`    // 操作设备ID
    DeviceName  string `json:"device_name"`  // 设备名称
    ChangesSummary string `json:"changes_summary"` // 变更摘要
    Size        int64  `json:"size"`         // 配置大小(字节)
    ItemCount   struct {
        LaunchItems      int `json:"launch_items"`       // 启动项数量
        Categories       int `json:"categories"`         // 分类数量
        Settings         int `json:"settings"`           // 设置项数量
        FileAssociations int `json:"file_associations"`  // 文件关联数量
    } `json:"item_count"`
    IsCurrent   bool   `json:"is_current"`   // 是否当前版本
    CanRestore  bool   `json:"can_restore"`  // 是否可恢复
}

// 获取配置历史请求
type GetConfigHistoryReq {
    Limit  int `form:"limit,optional" validate:"min=1,max=50"`           // 限制数量
    Offset int `form:"offset,optional" validate:"min=0"`                 // 偏移量
}

// 获取配置历史响应
type GetConfigHistoryResp {
    BaseResp
    Data struct {
        Versions []ConfigVersion `json:"versions"`        // 版本列表
        Total    int64           `json:"total"`           // 总版本数
        Current  string          `json:"current"`         // 当前版本
        Oldest   string          `json:"oldest"`          // 最旧版本
        Retention struct {
            MaxVersions int `json:"max_versions"`         // 最大保留版本数
            MaxAge      int `json:"max_age"`              // 最大保留天数
        } `json:"retention"`
    } `json:"data"`
}

// 恢复配置版本请求
type RestoreConfigReq {
    Version   string `path:"version" validate:"required"`                // 要恢复的版本号
    DeviceId  string `json:"device_id" validate:"required"`              // 设备ID
    Selective bool   `json:"selective,optional"`                         // 是否选择性恢复
    Items     struct {
        LaunchItems      bool `json:"launch_items,optional"`      // 是否恢复启动项
        Categories       bool `json:"categories,optional"`        // 是否恢复分类
        Settings         bool `json:"settings,optional"`          // 是否恢复设置
        FileAssociations bool `json:"file_associations,optional"` // 是否恢复文件关联
    } `json:"items,optional"` // 选择性恢复项目
    CreateBackup bool `json:"create_backup,optional"`                    // 是否创建当前配置备份
}

// 快捷键设置项
type HotkeyItem {
    Id              int64  `json:"id"`               // 快捷键ID
    ActionName      string `json:"action_name"`      // 动作名称
    DisplayName     string `json:"display_name"`     // 显示名称
    KeySequence     string `json:"key_sequence"`     // 快捷键序列
    Description     string `json:"description"`      // 描述
    IsGlobal        bool   `json:"is_global"`        // 是否全局快捷键
    IsEnabled       bool   `json:"is_enabled"`       // 是否启用
    ConflictResolution string `json:"conflict_resolution"` // 冲突解决策略
    UseCount        int64  `json:"use_count"`        // 使用次数
    LastUsed        string `json:"last_used,optional"` // 最后使用时间
    CreatedAt       string `json:"created_at"`       // 创建时间
    UpdatedAt       string `json:"updated_at"`       // 更新时间
}

// 文件关联
type FileAssociation {
    Id            int64   `json:"id"`             // 关联ID
    FileExtension string  `json:"file_extension"` // 文件扩展名
    MimeType      string  `json:"mime_type"`      // MIME类型
    LaunchItemId  int64   `json:"launch_item_id"` // 关联的启动项ID
    LaunchItemName string `json:"launch_item_name,optional"` // 启动项名称
    AssociationType string `json:"association_type"` // 关联类型: system/user/auto
    ConfidenceScore float64 `json:"confidence_score"` // 置信度分数
    UseCount        int64  `json:"use_count"`      // 使用次数
    LastUsed        string `json:"last_used,optional"` // 最后使用时间
    IsDefault       bool   `json:"is_default"`     // 是否默认关联
    CreatedAt       string `json:"created_at"`     // 创建时间
    UpdatedAt       string `json:"updated_at"`     // 更新时间
}

// 获取应用设置请求
type GetAppSettingsReq {
    Category string `form:"category,optional"`                           // 设置分类过滤
    Search   string `form:"search,optional"`                             // 搜索关键词
    UserConfigurable *bool `form:"user_configurable,optional"`          // 是否用户可配置
}

// 获取应用设置响应
type GetAppSettingsResp {
    BaseResp
    Data []ConfigItem `json:"data"`                                      // 设置项列表
    Categories []struct {
        Name        string `json:"name"`         // 分类名称
        DisplayName string `json:"display_name"` // 显示名称
        Count       int    `json:"count"`        // 设置项数量
        Icon        string `json:"icon,optional"` // 图标
    } `json:"categories"` // 设置分类
}

// 更新应用设置请求
type UpdateAppSettingsReq {
    Settings []struct {
        Key   string `json:"key" validate:"required"`   // 设置键
        Value string `json:"value" validate:"required"` // 设置值
    } `json:"settings" validate:"required,min=1,dive"`
    DeviceId string `json:"device_id" validate:"required"`              // 设备ID
}

// 获取快捷键设置请求
type GetHotkeySettingsReq {
    IsGlobal  *bool  `form:"is_global,optional"`                        // 全局快捷键过滤
    IsEnabled *bool  `form:"is_enabled,optional"`                       // 启用状态过滤
    Search    string `form:"search,optional"`                           // 搜索关键词
}

// 获取快捷键设置响应
type GetHotkeySettingsResp {
    BaseResp
    Data []HotkeyItem `json:"data"`                                     // 快捷键列表
    Conflicts []struct {
        KeySequence string   `json:"key_sequence"` // 冲突的快捷键
        Actions     []string `json:"actions"`      // 冲突的动作
        Severity    string   `json:"severity"`     // 冲突严重程度
    } `json:"conflicts,optional"` // 快捷键冲突
}

// 更新快捷键设置请求
type UpdateHotkeySettingsReq {
    Hotkeys []struct {
        ActionName  string `json:"action_name" validate:"required"`  // 动作名称
        KeySequence string `json:"key_sequence" validate:"required"` // 快捷键序列
        IsEnabled   bool   `json:"is_enabled,optional"`              // 是否启用
    } `json:"hotkeys" validate:"required,min=1,dive"`
    DeviceId string `json:"device_id" validate:"required"`           // 设备ID
    ResolveConflicts bool `json:"resolve_conflicts,optional"`        // 是否自动解决冲突
}

// 配置导入请求
type ImportConfigReq {
    Format      string `json:"format" validate:"required,oneof=json xml backup"` // 导入格式
    Data        string `json:"data" validate:"required"`                         // 导入数据
    DeviceId    string `json:"device_id" validate:"required"`                    // 设备ID
    MergeMode   string `json:"merge_mode,optional" validate:"oneof=replace merge skip"` // 合并模式
    Selective   bool   `json:"selective,optional"`                               // 是否选择性导入
    Items       struct {
        LaunchItems      bool `json:"launch_items,optional"`      // 是否导入启动项
        Categories       bool `json:"categories,optional"`        // 是否导入分类
        Settings         bool `json:"settings,optional"`          // 是否导入设置
        FileAssociations bool `json:"file_associations,optional"` // 是否导入文件关联
    } `json:"items,optional"` // 选择性导入项目
    ValidateOnly bool `json:"validate_only,optional"`                           // 仅验证不导入
    CreateBackup bool `json:"create_backup,optional"`                           // 是否创建备份
}

// 配置导入响应
type ImportConfigResp {
    BaseResp
    Data struct {
        ImportId    string `json:"import_id"`     // 导入任务ID
        Status      string `json:"status"`        // 导入状态: success/partial/failed
        Summary     struct {
            LaunchItems      int `json:"launch_items"`       // 导入的启动项数
            Categories       int `json:"categories"`         // 导入的分类数
            Settings         int `json:"settings"`           // 导入的设置数
            FileAssociations int `json:"file_associations"`  // 导入的文件关联数
        } `json:"summary"`
        Errors   []string `json:"errors,optional"`   // 错误信息
        Warnings []string `json:"warnings,optional"` // 警告信息
        Conflicts []ConfigConflict `json:"conflicts,optional"` // 冲突列表
        BackupVersion string `json:"backup_version,optional"` // 备份版本号
    } `json:"data"`
}

// 配置导出请求
type ExportConfigReq {
    Format    string `json:"format" validate:"required,oneof=json xml backup"` // 导出格式
    DeviceId  string `json:"device_id" validate:"required"`                    // 设备ID
    Selective bool   `json:"selective,optional"`                               // 是否选择性导出
    Items     struct {
        LaunchItems      bool `json:"launch_items,optional"`      // 是否导出启动项
        Categories       bool `json:"categories,optional"`        // 是否导出分类
        Settings         bool `json:"settings,optional"`          // 是否导出设置
        FileAssociations bool `json:"file_associations,optional"` // 是否导出文件关联
    } `json:"items,optional"` // 选择性导出项目
    IncludeMetadata bool   `json:"include_metadata,optional"`                  // 是否包含元数据
    Compress        bool   `json:"compress,optional"`                          // 是否压缩
    Password        string `json:"password,optional"`                          // 加密密码
}

// 配置导出响应
type ExportConfigResp {
    BaseResp
    Data struct {
        Data        string `json:"data"`         // 导出数据
        FileName    string `json:"file_name"`    // 文件名
        FileSize    int64  `json:"file_size"`    // 文件大小
        Format      string `json:"format"`       // 文件格式
        Checksum    string `json:"checksum"`     // 校验和
        ExportTime  string `json:"export_time"`  // 导出时间
        IsEncrypted bool   `json:"is_encrypted"` // 是否加密
        IsCompressed bool  `json:"is_compressed"` // 是否压缩
    } `json:"data"`
}
