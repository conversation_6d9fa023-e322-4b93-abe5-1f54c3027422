syntax = "v1"

// ============================================================================
// 分类相关类型定义
// ============================================================================

// 分类基础信息
type Category {
    Id          int64  `json:"id"`           // 分类ID
    Name        string `json:"name"`         // 分类名称(唯一标识)
    DisplayName string `json:"display_name"` // 显示名称(支持多语言)
    IconName    string `json:"icon_name"`    // 图标名称
    Color       string `json:"color"`        // 分类颜色(十六进制)
    Description string `json:"description"`  // 分类描述
    SortOrder   int    `json:"sort_order"`   // 排序顺序
    IsSystem    bool   `json:"is_system"`    // 是否系统分类
    IsVisible   bool   `json:"is_visible"`   // 是否可见
    ParentId    int64  `json:"parent_id,optional"` // 父分类ID(支持层级)
    ParentName  string `json:"parent_name,optional"` // 父分类名称
    CreatedAt   string `json:"created_at"`   // 创建时间
    UpdatedAt   string `json:"updated_at"`   // 更新时间
}

// 分类统计信息
type CategoryStats {
    Id              int64   `json:"id"`               // 分类ID
    Name            string  `json:"name"`             // 分类名称
    DisplayName     string  `json:"display_name"`     // 显示名称
    ItemCount       int64   `json:"item_count"`       // 启动项数量
    EnabledCount    int64   `json:"enabled_count"`    // 启用的启动项数量
    TotalUsage      int64   `json:"total_usage"`      // 总使用次数
    AvgUsage        float64 `json:"avg_usage"`        // 平均使用次数
    AvgResponseTime float64 `json:"avg_response_time"` // 平均响应时间
    LastUsed        string  `json:"last_used,optional"` // 最后使用时间
    PercentageOfTotal float64 `json:"percentage_of_total"` // 占总数的百分比
    UsageRank       int     `json:"usage_rank"`       // 使用排名
    GrowthRate      float64 `json:"growth_rate"`      // 增长率
}

// 分类树节点
type CategoryTreeNode {
    Category
    Children    []CategoryTreeNode `json:"children,optional"`    // 子分类
    ItemCount   int64              `json:"item_count"`           // 启动项数量
    TotalCount  int64              `json:"total_count"`          // 包含子分类的总数量
    Level       int                `json:"level"`                // 层级深度
    Path        string             `json:"path"`                 // 分类路径
    IsExpanded  bool               `json:"is_expanded,optional"` // 是否展开(前端状态)
}

// 获取分类列表请求
type GetCategoriesReq {
    ParentId     int64  `form:"parent_id,optional"`     // 父分类ID，0表示根分类
    IsVisible    *bool  `form:"is_visible,optional"`    // 可见性过滤
    IsSystem     *bool  `form:"is_system,optional"`     // 系统分类过滤
    IncludeStats bool   `form:"include_stats,optional"` // 是否包含统计信息
    TreeFormat   bool   `form:"tree_format,optional"`   // 是否返回树形结构
    MaxDepth     int    `form:"max_depth,optional"`     // 最大深度限制
    SortBy       string `form:"sort_by,optional"`       // 排序字段: sort_order/name/item_count/usage
    SortOrder    string `form:"sort_order,optional"`    // 排序方向: asc/desc
}

// 获取分类列表响应
type GetCategoriesResp {
    BaseResp
    Data []Category `json:"data,optional"`      // 平铺格式的分类列表
    Tree []CategoryTreeNode `json:"tree,optional"` // 树形格式的分类列表
    Stats struct {
        Total     int64 `json:"total"`      // 总分类数
        Visible   int64 `json:"visible"`    // 可见分类数
        System    int64 `json:"system"`     // 系统分类数
        Custom    int64 `json:"custom"`     // 自定义分类数
        MaxDepth  int   `json:"max_depth"`  // 最大层级深度
    } `json:"stats"`
}

// 创建分类请求
type CreateCategoryReq {
    Name        string `json:"name" validate:"required,min=1,max=50"`         // 分类名称
    DisplayName string `json:"display_name" validate:"required,min=1,max=100"` // 显示名称
    IconName    string `json:"icon_name,optional" validate:"max=50"`          // 图标名称
    Color       string `json:"color,optional" validate:"hexcolor"`            // 分类颜色
    Description string `json:"description,optional" validate:"max=500"`       // 分类描述
    SortOrder   int    `json:"sort_order,optional" validate:"min=0"`          // 排序顺序
    IsVisible   bool   `json:"is_visible,optional"`                           // 是否可见
    ParentId    int64  `json:"parent_id,optional" validate:"min=0"`           // 父分类ID
}

// 更新分类请求
type UpdateCategoryReq {
    Id          int64  `path:"id" validate:"required,min=1"`                  // 分类ID
    Name        string `json:"name,optional" validate:"min=1,max=50"`         // 分类名称
    DisplayName string `json:"display_name,optional" validate:"min=1,max=100"` // 显示名称
    IconName    string `json:"icon_name,optional" validate:"max=50"`          // 图标名称
    Color       string `json:"color,optional" validate:"hexcolor"`            // 分类颜色
    Description string `json:"description,optional" validate:"max=500"`       // 分类描述
    SortOrder   int    `json:"sort_order,optional" validate:"min=0"`          // 排序顺序
    IsVisible   bool   `json:"is_visible,optional"`                           // 是否可见
    ParentId    int64  `json:"parent_id,optional" validate:"min=0"`           // 父分类ID
}

// 删除分类请求
type DeleteCategoryReq {
    Id               int64 `path:"id" validate:"required,min=1"`     // 分类ID
    MoveToCategory   int64 `form:"move_to_category,optional"`        // 将启动项移动到的分类ID
    ForceDelete      bool  `form:"force_delete,optional"`            // 强制删除(删除所有启动项)
}

// 分类响应
type CategoryResp {
    BaseResp
    Data  Category      `json:"data"`            // 分类数据
    Stats CategoryStats `json:"stats,optional"`  // 统计信息
}

// 获取分类统计请求
type GetCategoryStatsReq {
    TimeRange    string `form:"time_range,optional"`    // 时间范围: 1d/7d/30d/90d
    IncludeEmpty bool   `form:"include_empty,optional"` // 是否包含空分类
    GroupBy      string `form:"group_by,optional"`      // 分组方式: day/week/month
}

// 获取分类统计响应
type GetCategoryStatsResp {
    BaseResp
    Data []CategoryStats `json:"data"`           // 分类统计列表
    Summary struct {
        TotalCategories   int64   `json:"total_categories"`    // 总分类数
        TotalItems        int64   `json:"total_items"`         // 总启动项数
        TotalUsage        int64   `json:"total_usage"`         // 总使用次数
        AvgItemsPerCategory float64 `json:"avg_items_per_category"` // 平均每分类启动项数
        MostUsedCategory  string  `json:"most_used_category"`  // 最常用分类
        LeastUsedCategory string  `json:"least_used_category"` // 最少用分类
    } `json:"summary"`
    Timeline []struct {
        Date       string `json:"date"`        // 日期
        Categories []struct {
            CategoryId int64 `json:"category_id"` // 分类ID
            Name       string `json:"name"`       // 分类名称
            Usage      int64  `json:"usage"`      // 使用次数
        } `json:"categories"`
    } `json:"timeline,optional"` // 时间线数据
}

// 分类排序请求
type SortCategoriesReq {
    Categories []struct {
        Id        int64 `json:"id" validate:"required,min=1"`        // 分类ID
        SortOrder int   `json:"sort_order" validate:"min=0"`         // 新的排序顺序
        ParentId  int64 `json:"parent_id,optional" validate:"min=0"` // 父分类ID(支持移动)
    } `json:"categories" validate:"required,min=1,dive"`
}

// 分类移动请求
type MoveCategoryReq {
    Id         int64 `path:"id" validate:"required,min=1"`           // 要移动的分类ID
    ParentId   int64 `json:"parent_id" validate:"min=0"`             // 新的父分类ID，0表示移动到根级
    SortOrder  int   `json:"sort_order,optional" validate:"min=0"`   // 新的排序顺序
}

// 分类合并请求
type MergeCategoriesReq {
    SourceIds []int64 `json:"source_ids" validate:"required,min=1,dive,min=1"` // 源分类ID列表
    TargetId  int64   `json:"target_id" validate:"required,min=1"`             // 目标分类ID
    DeleteSource bool `json:"delete_source,optional"`                          // 是否删除源分类
}

// 分类复制请求
type CopyCategoryReq {
    Id           int64  `path:"id" validate:"required,min=1"`                   // 源分类ID
    NewName      string `json:"new_name" validate:"required,min=1,max=50"`      // 新分类名称
    ParentId     int64  `json:"parent_id,optional" validate:"min=0"`            // 父分类ID
    CopyItems    bool   `json:"copy_items,optional"`                            // 是否复制启动项
    CopyChildren bool   `json:"copy_children,optional"`                         // 是否复制子分类
}

// 分类导入请求
type ImportCategoriesReq {
    Format       string `json:"format" validate:"required,oneof=json csv xml"`  // 导入格式
    Data         string `json:"data" validate:"required"`                       // 导入数据
    MergeMode    string `json:"merge_mode,optional" validate:"oneof=skip overwrite merge"` // 合并模式
    ValidateOnly bool   `json:"validate_only,optional"`                         // 仅验证不导入
}

// 分类导出请求
type ExportCategoriesReq {
    Format        string  `json:"format" validate:"required,oneof=json csv xml"` // 导出格式
    Ids           []int64 `json:"ids,optional"`                                  // 指定分类ID列表
    IncludeItems  bool    `json:"include_items,optional"`                        // 是否包含启动项
    IncludeStats  bool    `json:"include_stats,optional"`                        // 是否包含统计信息
    TreeFormat    bool    `json:"tree_format,optional"`                          // 是否导出为树形结构
}

// 分类使用趋势请求
type GetCategoryTrendsReq {
    CategoryId int64  `path:"id,optional"`                    // 分类ID，为空则获取所有分类
    TimeRange  string `form:"time_range,optional"`            // 时间范围: 1d/7d/30d/90d
    GroupBy    string `form:"group_by,optional"`              // 分组方式: hour/day/week/month
    MetricType string `form:"metric_type,optional"`           // 指标类型: usage/items/response_time
}

// 分类使用趋势响应
type GetCategoryTrendsResp {
    BaseResp
    Data []struct {
        CategoryId   int64             `json:"category_id"`   // 分类ID
        CategoryName string            `json:"category_name"` // 分类名称
        Timeline     []TimeSeriesPoint `json:"timeline"`      // 时间线数据
        Trend        string            `json:"trend"`         // 趋势: up/down/stable
        ChangeRate   float64           `json:"change_rate"`   // 变化率
    } `json:"data"`
    Summary struct {
        TotalUsage     int64   `json:"total_usage"`      // 总使用次数
        GrowthRate     float64 `json:"growth_rate"`      // 整体增长率
        MostGrowing    string  `json:"most_growing"`     // 增长最快的分类
        MostDeclining  string  `json:"most_declining"`   // 下降最快的分类
    } `json:"summary"`
}

// 分类推荐请求
type GetCategoryRecommendationsReq {
    Context struct {
        FileType         string   `json:"file_type,optional"`         // 文件类型
        ClipboardContent string   `json:"clipboard_content,optional"` // 剪贴板内容
        ActiveWindow     string   `json:"active_window,optional"`     // 活动窗口
        RecentFiles      []string `json:"recent_files,optional"`      // 最近文件
        TimeOfDay        string   `json:"time_of_day,optional"`       // 时间段
        WorkingDirectory string   `json:"working_directory,optional"` // 工作目录
    } `json:"context,optional"` // 上下文信息
    MaxResults int `json:"max_results,optional" validate:"min=1,max=20"` // 最大结果数
}

// 分类推荐响应
type GetCategoryRecommendationsResp {
    BaseResp
    Data []struct {
        Category    Category `json:"category"`     // 推荐的分类
        Score       float64  `json:"score"`        // 推荐分数
        Confidence  float64  `json:"confidence"`   // 置信度
        Reasons     []string `json:"reasons"`      // 推荐理由
        ItemCount   int64    `json:"item_count"`   // 分类中的启动项数量
        RecentUsage int64    `json:"recent_usage"` // 最近使用次数
    } `json:"data"`
}
