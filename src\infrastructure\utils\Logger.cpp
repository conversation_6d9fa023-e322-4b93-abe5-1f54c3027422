#include "Logger.h"
#include <QStandardPaths>
#include <QDir>
#include <QThread>
#include <QCoreApplication>
#include <QDebug>
#include <iostream>

// 静态成员初始化
Logger* Logger::s_instance = nullptr;

Logger::Logger(QObject *parent)
    : QObject(parent)
    , m_maxFileSize(10 * 1024 * 1024)  // 10MB
    , m_maxFileCount(5)
    , m_consoleOutputEnabled(true)
    , m_fileOutputEnabled(true)
    , m_initialized(false)
    , m_rotationTimer(new QTimer(this))
    , m_writeTimer(new QTimer(this))
{
    // 设置定时器
    m_rotationTimer->setInterval(ROTATION_CHECK_INTERVAL);
    m_rotationTimer->setSingleShot(false);
    connect(m_rotationTimer, &QTimer::timeout, this, &Logger::checkLogRotation);

    m_writeTimer->setInterval(WRITE_INTERVAL);
    m_writeTimer->setSingleShot(false);
    connect(m_writeTimer, &QTimer::timeout, this, &Logger::writeLogAsync);
}

Logger::~Logger()
{
    shutdown();
}

Logger& Logger::instance()
{
    static QMutex mutex;
    QMutexLocker locker(&mutex);
    
    if (!s_instance) {
        s_instance = new Logger();
    }
    
    return *s_instance;
}

bool Logger::initialize(const QString &logFilePath, qint64 maxFileSize, int maxFileCount)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_initialized) {
        return true;
    }
    
    // 设置参数
    m_maxFileSize = maxFileSize;
    m_maxFileCount = maxFileCount;
    
    // 设置日志文件路径
    if (logFilePath.isEmpty()) {
        QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
        QDir().mkpath(appDataPath + "/logs");
        m_logFilePath = appDataPath + "/logs/app.log";
    } else {
        m_logFilePath = logFilePath;
    }
    
    // 创建日志文件目录
    QFileInfo fileInfo(m_logFilePath);
    QDir().mkpath(fileInfo.absolutePath());
    
    // 打开日志文件
    m_logFile = std::make_unique<QFile>(m_logFilePath);
    if (!m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
        std::cerr << "Failed to open log file: " << m_logFilePath.toStdString() << std::endl;
        return false;
    }
    
    m_logStream = std::make_unique<QTextStream>(m_logFile.get());
    m_logStream->setEncoding(QStringConverter::Utf8);
    
    // 安装消息处理器
    qInstallMessageHandler(Logger::messageHandler);
    
    // 启动定时器
    m_rotationTimer->start();
    m_writeTimer->start();
    
    m_initialized = true;
    
    // 写入启动日志
    LogEntry startupEntry;
    startupEntry.timestamp = QDateTime::currentDateTime();
    startupEntry.type = QtInfoMsg;
    startupEntry.category = "logger";
    startupEntry.message = QString("Logger initialized, file: %1").arg(m_logFilePath);
    startupEntry.threadId = reinterpret_cast<qint64>(QThread::currentThread());
    
    handleMessage(QtInfoMsg, QMessageLogContext(), startupEntry.message);
    
    return true;
}

void Logger::setLogFile(const QString &filePath)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_logFilePath == filePath) {
        return;
    }
    
    // 关闭当前文件
    if (m_logStream) {
        m_logStream->flush();
        m_logStream.reset();
    }
    
    if (m_logFile) {
        m_logFile->close();
        m_logFile.reset();
    }
    
    // 设置新文件路径
    m_logFilePath = filePath;
    
    if (m_initialized && m_fileOutputEnabled) {
        // 创建目录
        QFileInfo fileInfo(m_logFilePath);
        QDir().mkpath(fileInfo.absolutePath());
        
        // 打开新文件
        m_logFile = std::make_unique<QFile>(m_logFilePath);
        if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
            m_logStream = std::make_unique<QTextStream>(m_logFile.get());
            m_logStream->setEncoding(QStringConverter::Utf8);
        }
    }
}

void Logger::setMaxFileSize(qint64 maxSize)
{
    QMutexLocker locker(&m_mutex);
    m_maxFileSize = maxSize;
}

void Logger::setMaxFileCount(int maxCount)
{
    QMutexLocker locker(&m_mutex);
    m_maxFileCount = maxCount;
}

void Logger::setConsoleOutputEnabled(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_consoleOutputEnabled = enabled;
}

void Logger::setFileOutputEnabled(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_fileOutputEnabled = enabled;
}

void Logger::setCategoryFilter(const QString &category, QtMsgType type)
{
    QMutexLocker locker(&m_mutex);
    m_categoryFilters[category] = type;
}

void Logger::removeCategoryFilter(const QString &category)
{
    QMutexLocker locker(&m_mutex);
    m_categoryFilters.remove(category);
}

void Logger::clearFilters()
{
    QMutexLocker locker(&m_mutex);
    m_categoryFilters.clear();
}

QList<LogEntry> Logger::getRecentEntries(int count) const
{
    QMutexLocker locker(&m_mutex);
    
    if (count >= m_memoryLog.size()) {
        return m_memoryLog;
    }
    
    return m_memoryLog.mid(m_memoryLog.size() - count);
}

QList<LogEntry> Logger::getEntriesByType(QtMsgType type, int count) const
{
    QMutexLocker locker(&m_mutex);
    
    QList<LogEntry> result;
    
    for (auto it = m_memoryLog.rbegin(); it != m_memoryLog.rend() && result.size() < count; ++it) {
        if (it->type == type) {
            result.prepend(*it);
        }
    }
    
    return result;
}

QList<LogEntry> Logger::getEntriesByCategory(const QString &category, int count) const
{
    QMutexLocker locker(&m_mutex);
    
    QList<LogEntry> result;
    
    for (auto it = m_memoryLog.rbegin(); it != m_memoryLog.rend() && result.size() < count; ++it) {
        if (it->category == category) {
            result.prepend(*it);
        }
    }
    
    return result;
}

void Logger::clearMemoryLog()
{
    QMutexLocker locker(&m_mutex);
    m_memoryLog.clear();
}

void Logger::flush()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_logStream) {
        m_logStream->flush();
    }
    
    if (m_logFile) {
        m_logFile->flush();
    }
}

void Logger::shutdown()
{
    if (!m_initialized) {
        return;
    }
    
    // 停止定时器
    m_rotationTimer->stop();
    m_writeTimer->stop();
    
    // 写入剩余的日志
    writeLogAsync();
    
    // 恢复默认消息处理器
    qInstallMessageHandler(nullptr);
    
    QMutexLocker locker(&m_mutex);
    
    // 关闭文件
    if (m_logStream) {
        m_logStream->flush();
        m_logStream.reset();
    }
    
    if (m_logFile) {
        m_logFile->close();
        m_logFile.reset();
    }
    
    m_initialized = false;
}

void Logger::messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    if (s_instance) {
        s_instance->handleMessage(type, context, msg);
    }
}

void Logger::handleMessage(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    // 创建日志条目
    LogEntry entry;
    entry.timestamp = QDateTime::currentDateTime();
    entry.type = type;
    entry.category = context.category ? QString(context.category) : "default";
    entry.message = msg;
    entry.file = context.file ? QString(context.file) : QString();
    entry.line = context.line;
    entry.function = context.function ? QString(context.function) : QString();
    entry.threadId = reinterpret_cast<qint64>(QThread::currentThread());
    
    // 检查过滤器
    if (shouldFilter(entry.category, type)) {
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    // 添加到内存日志
    m_memoryLog.append(entry);
    if (m_memoryLog.size() > MAX_MEMORY_LOG_SIZE) {
        m_memoryLog.removeFirst();
    }
    
    // 添加到待写入队列
    m_pendingWrites.append(entry);
    
    // 控制台输出
    if (m_consoleOutputEnabled) {
        writeToConsole(entry);
    }
    
    // 发送信号
    emit logEntryAdded(entry);
}

void Logger::checkLogRotation()
{
    QMutexLocker locker(&m_mutex);

    if (!m_logFile || !m_fileOutputEnabled) {
        return;
    }

    // 检查文件大小
    if (m_logFile->size() >= m_maxFileSize) {
        rotateLogFile();
    }
}

void Logger::writeLogAsync()
{
    QMutexLocker locker(&m_mutex);

    if (!m_fileOutputEnabled || !m_logStream || m_pendingWrites.isEmpty()) {
        return;
    }

    // 批量写入待处理的日志
    for (const LogEntry &entry : m_pendingWrites) {
        writeToFile(entry);
    }

    m_pendingWrites.clear();

    // 刷新到磁盘
    m_logStream->flush();
    m_logFile->flush();
}

void Logger::writeToFile(const LogEntry &entry)
{
    if (!m_logStream) {
        return;
    }

    QString formattedEntry = formatLogEntry(entry);
    *m_logStream << formattedEntry << Qt::endl;
}

void Logger::writeToConsole(const LogEntry &entry)
{
    QString formattedEntry = formatLogEntry(entry);

    // 根据日志级别选择输出流
    if (entry.type == QtCriticalMsg || entry.type == QtFatalMsg) {
        std::cerr << formattedEntry.toStdString() << std::endl;
    } else {
        std::cout << formattedEntry.toStdString() << std::endl;
    }
}

QString Logger::formatLogEntry(const LogEntry &entry) const
{
    // 格式: [时间戳] [级别] [分类] [线程ID] 消息 (文件:行号)
    QString formatted = QString("[%1] [%2] [%3] [%4] %5")
                       .arg(entry.timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz"))
                       .arg(getLogTypeString(entry.type))
                       .arg(entry.category)
                       .arg(entry.threadId, 0, 16)  // 十六进制显示线程ID
                       .arg(entry.message);

    // 添加文件和行号信息（仅在调试模式下）
    if (!entry.file.isEmpty() && entry.line > 0) {
        QFileInfo fileInfo(entry.file);
        formatted += QString(" (%1:%2)").arg(fileInfo.fileName()).arg(entry.line);
    }

    return formatted;
}

void Logger::rotateLogFile()
{
    if (!m_logFile || !m_logStream) {
        return;
    }

    // 刷新并关闭当前文件
    m_logStream->flush();
    m_logFile->flush();
    m_logStream.reset();
    m_logFile->close();

    // 轮转文件名
    QFileInfo fileInfo(m_logFilePath);
    QString baseName = fileInfo.completeBaseName();
    QString suffix = fileInfo.suffix();
    QString dir = fileInfo.absolutePath();

    // 移动现有的轮转文件
    for (int i = m_maxFileCount - 1; i > 0; --i) {
        QString oldFile = QString("%1/%2.%3.%4").arg(dir, baseName).arg(i).arg(suffix);
        QString newFile = QString("%1/%2.%3.%4").arg(dir, baseName).arg(i + 1).arg(suffix);

        if (QFile::exists(oldFile)) {
            QFile::remove(newFile);  // 删除目标文件（如果存在）
            QFile::rename(oldFile, newFile);
        }
    }

    // 移动当前文件到 .1
    QString rotatedFile = QString("%1/%2.1.%3").arg(dir, baseName, suffix);
    QFile::remove(rotatedFile);  // 删除目标文件（如果存在）
    QFile::rename(m_logFilePath, rotatedFile);

    // 创建新的日志文件
    m_logFile = std::make_unique<QFile>(m_logFilePath);
    if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
        m_logStream = std::make_unique<QTextStream>(m_logFile.get());
        m_logStream->setEncoding(QStringConverter::Utf8);

        // 写入轮转日志
        LogEntry rotationEntry;
        rotationEntry.timestamp = QDateTime::currentDateTime();
        rotationEntry.type = QtInfoMsg;
        rotationEntry.category = "logger";
        rotationEntry.message = QString("Log file rotated, new file: %1").arg(m_logFilePath);
        rotationEntry.threadId = reinterpret_cast<qint64>(QThread::currentThread());

        writeToFile(rotationEntry);
        m_logStream->flush();

        emit logFileRotated(m_logFilePath);
    }
}

QString Logger::getLogTypeString(QtMsgType type) const
{
    switch (type) {
    case QtDebugMsg:
        return "DEBUG";
    case QtInfoMsg:
        return "INFO ";
    case QtWarningMsg:
        return "WARN ";
    case QtCriticalMsg:
        return "ERROR";
    case QtFatalMsg:
        return "FATAL";
    default:
        return "UNKNW";
    }
}

bool Logger::shouldFilter(const QString &category, QtMsgType type) const
{
    auto it = m_categoryFilters.find(category);
    if (it != m_categoryFilters.end()) {
        return type < it.value();
    }

    // 默认不过滤
    return false;
}
