#pragma once

#include <QWidget>
#include <QLineEdit>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QListWidget>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QSystemTrayIcon>
#include <QScreen>
#include <memory>

#include "business/entities/LaunchItem.h"

// 前置声明
class LaunchItemViewModel;
class DisplayModeManager;
class ConfigManager;

/**
 * @brief 任务栏位置枚举
 */
enum class TaskbarPosition {
    Top,        // 顶部
    Bottom,     // 底部
    Left,       // 左侧
    Right,      // 右侧
    Auto        // 自动检测
};

/**
 * @brief 任务栏窗口
 * 
 * 集成到系统任务栏的启动界面，包括：
 * - 任务栏位置自适应
 * - 紧凑的横向布局
 * - 快速搜索和启动
 * - 自动收缩展开
 * - 系统集成优化
 */
class TaskbarWindow : public QWidget
{
    Q_OBJECT
    
    Q_PROPERTY(TaskbarPosition position READ position WRITE setPosition NOTIFY positionChanged)
    Q_PROPERTY(bool autoCollapse READ autoCollapse WRITE setAutoCollapse NOTIFY autoCollapseChanged)
    Q_PROPERTY(bool collapsed READ collapsed WRITE setCollapsed NOTIFY collapsedChanged)
    Q_PROPERTY(int maxVisibleItems READ maxVisibleItems WRITE setMaxVisibleItems NOTIFY maxVisibleItemsChanged)

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit TaskbarWindow(QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~TaskbarWindow();

    // 属性访问器
    TaskbarPosition position() const;
    void setPosition(TaskbarPosition pos);
    
    bool autoCollapse() const;
    void setAutoCollapse(bool enabled);
    
    bool collapsed() const;
    void setCollapsed(bool collapsed);
    
    int maxVisibleItems() const;
    void setMaxVisibleItems(int count);

    // 公共方法
    
    /**
     * @brief 显示窗口
     */
    Q_INVOKABLE void showWindow();
    
    /**
     * @brief 隐藏窗口
     */
    Q_INVOKABLE void hideWindow();
    
    /**
     * @brief 展开窗口
     */
    Q_INVOKABLE void expandWindow();
    
    /**
     * @brief 收缩窗口
     */
    Q_INVOKABLE void collapseWindow();
    
    /**
     * @brief 切换展开/收缩状态
     */
    Q_INVOKABLE void toggleCollapsed();
    
    /**
     * @brief 聚焦搜索框
     */
    Q_INVOKABLE void focusSearchBox();
    
    /**
     * @brief 检测任务栏位置
     * @return 任务栏位置
     */
    Q_INVOKABLE TaskbarPosition detectTaskbarPosition();
    
    /**
     * @brief 更新窗口位置和大小
     */
    Q_INVOKABLE void updateWindowGeometry();
    
    /**
     * @brief 启动选中项目
     */
    Q_INVOKABLE void launchSelectedItem();

signals:
    /**
     * @brief 任务栏位置变更信号
     * @param position 新位置
     */
    void positionChanged(TaskbarPosition position);
    
    /**
     * @brief 自动收缩设置变更信号
     * @param enabled 是否启用
     */
    void autoCollapseChanged(bool enabled);
    
    /**
     * @brief 收缩状态变更信号
     * @param collapsed 是否收缩
     */
    void collapsedChanged(bool collapsed);
    
    /**
     * @brief 最大可见项目数变更信号
     * @param count 项目数
     */
    void maxVisibleItemsChanged(int count);
    
    /**
     * @brief 项目启动信号
     * @param itemId 项目ID
     * @param success 是否成功
     */
    void itemLaunched(int itemId, bool success);
    
    /**
     * @brief 请求切换模式信号
     * @param targetMode 目标模式
     */
    void switchModeRequested(int targetMode);

protected:
    // 事件处理
    void showEvent(QShowEvent *event) override;
    void hideEvent(QHideEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void enterEvent(QEnterEvent *event) override;
    void leaveEvent(QEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    /**
     * @brief 搜索文本变更处理
     * @param text 搜索文本
     */
    void onSearchTextChanged(const QString &text);
    
    /**
     * @brief 搜索延迟超时处理
     */
    void onSearchDelayTimeout();
    
    /**
     * @brief 项目激活处理
     * @param index 项目索引
     */
    void onItemActivated(int index);
    
    /**
     * @brief 展开/收缩按钮点击处理
     */
    void onToggleButtonClicked();
    
    /**
     * @brief 设置按钮点击处理
     */
    void onSettingsButtonClicked();
    
    /**
     * @brief 自动收缩超时处理
     */
    void onAutoCollapseTimeout();
    
    /**
     * @brief 屏幕变更处理
     */
    void onScreenChanged();
    
    /**
     * @brief 配置变更处理
     * @param key 配置键
     * @param value 配置值
     */
    void onConfigChanged(const QString &key, const QVariant &value);

private:
    /**
     * @brief 初始化UI
     */
    void setupUI();
    
    /**
     * @brief 创建水平布局
     */
    void createHorizontalLayout();
    
    /**
     * @brief 创建垂直布局
     */
    void createVerticalLayout();
    
    /**
     * @brief 创建搜索区域
     */
    void createSearchArea();
    
    /**
     * @brief 创建快速启动区域
     */
    void createQuickLaunchArea();
    
    /**
     * @brief 创建控制按钮
     */
    void createControlButtons();
    
    /**
     * @brief 设置连接
     */
    void setupConnections();
    
    /**
     * @brief 设置样式
     */
    void setupStyles();
    
    /**
     * @brief 应用任务栏样式
     */
    void applyTaskbarStyle();
    
    /**
     * @brief 设置窗口属性
     */
    void setupWindowProperties();
    
    /**
     * @brief 计算窗口位置
     * @return 窗口位置
     */
    QPoint calculateWindowPosition();
    
    /**
     * @brief 计算窗口大小
     * @return 窗口大小
     */
    QSize calculateWindowSize();
    
    /**
     * @brief 更新布局方向
     */
    void updateLayoutOrientation();
    
    /**
     * @brief 更新快速启动项目
     */
    void updateQuickLaunchItems();
    
    /**
     * @brief 启动展开动画
     */
    void startExpandAnimation();
    
    /**
     * @brief 启动收缩动画
     */
    void startCollapseAnimation();
    
    /**
     * @brief 重置自动收缩定时器
     */
    void resetAutoCollapseTimer();
    
    /**
     * @brief 获取任务栏区域
     * @return 任务栏矩形区域
     */
    QRect getTaskbarRect();
    
    /**
     * @brief 获取工作区域
     * @return 工作区域矩形
     */
    QRect getWorkArea();
    
    /**
     * @brief 加载设置
     */
    void loadSettings();
    
    /**
     * @brief 保存设置
     */
    void saveSettings();

private:
    // 服务引用
    std::shared_ptr<LaunchItemViewModel> m_viewModel;
    std::shared_ptr<DisplayModeManager> m_displayManager;
    std::shared_ptr<ConfigManager> m_configManager;
    
    // UI组件
    QHBoxLayout *m_horizontalLayout;
    QVBoxLayout *m_verticalLayout;
    QHBoxLayout *m_searchLayout;
    QHBoxLayout *m_quickLaunchLayout;
    QHBoxLayout *m_controlLayout;
    
    QLineEdit *m_searchEdit;
    QListWidget *m_quickLaunchList;
    QPushButton *m_toggleButton;
    QPushButton *m_settingsButton;
    QLabel *m_statusLabel;
    
    // 动画
    QPropertyAnimation *m_expandAnimation;
    QPropertyAnimation *m_collapseAnimation;
    QGraphicsOpacityEffect *m_opacityEffect;
    
    // 定时器
    QTimer *m_searchDelayTimer;
    QTimer *m_autoCollapseTimer;
    
    // 状态管理
    TaskbarPosition m_position = TaskbarPosition::Bottom;
    bool m_autoCollapse = true;
    bool m_collapsed = false;
    int m_maxVisibleItems = 6;
    int m_selectedIndex = -1;
    
    // 几何信息
    QSize m_expandedSize;
    QSize m_collapsedSize;
    QPoint m_windowPosition;
    
    // 搜索状态
    QString m_currentSearchText;
    QList<LaunchItem> m_quickLaunchItems;
    
    // 常量
    static const int COLLAPSED_WIDTH = 60;
    static const int COLLAPSED_HEIGHT = 40;
    static const int EXPANDED_WIDTH = 400;
    static const int EXPANDED_HEIGHT = 60;
    static const int SEARCH_DELAY = 200;
    static const int AUTO_COLLAPSE_DELAY = 2000;
    static const int ANIMATION_DURATION = 250;
    static const int TASKBAR_MARGIN = 4;
};
