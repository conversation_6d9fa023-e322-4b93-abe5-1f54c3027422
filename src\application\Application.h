#pragma once

#include <QObject>
#include <QTimer>
#include <QSystemTrayIcon>
#include <QLocalServer>
#include <memory>

// 前置声明
class ServiceContainer;
class ConfigManager;
class DatabaseManager;
class PluginManager;
class HotkeyManager;
class MainWindow;
class TrayIcon;
class LaunchManager;
class SearchEngine;
class RecommendationSystem;

/**
 * @brief 应用程序主类
 * 
 * 负责应用程序的整体生命周期管理，包括：
 * - 服务容器初始化
 * - 数据库初始化
 * - 插件系统初始化
 * - 全局热键注册
 * - 系统托盘设置
 * - 主窗口管理
 */
class Application : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit Application(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~Application();

    /**
     * @brief 初始化应用程序
     * @return 初始化是否成功
     */
    bool initialize();
    
    /**
     * @brief 获取服务容器
     * @return 服务容器指针
     */
    ServiceContainer* serviceContainer() const;
    
    /**
     * @brief 获取配置管理器
     * @return 配置管理器指针
     */
    ConfigManager* configManager() const;
    
    /**
     * @brief 获取应用程序单例
     * @return 应用程序实例
     */
    static Application* instance();
    
    /**
     * @brief 显示主窗口
     */
    void showMainWindow();
    
    /**
     * @brief 隐藏主窗口
     */
    void hideMainWindow();
    
    /**
     * @brief 切换主窗口显示状态
     */
    void toggleMainWindow();
    
    /**
     * @brief 退出应用程序
     */
    void quit();

public slots:
    /**
     * @brief 处理系统关机信号
     */
    void onSystemShutdown();
    
    /**
     * @brief 处理会话变更信号
     */
    void onSessionChanged();
    
    /**
     * @brief 处理单实例激活信号
     */
    void onSingleInstanceActivated();

signals:
    /**
     * @brief 应用程序即将退出信号
     */
    void aboutToQuit();
    
    /**
     * @brief 主窗口显示状态变更信号
     * @param visible 是否可见
     */
    void mainWindowVisibilityChanged(bool visible);

private slots:
    /**
     * @brief 处理托盘图标激活
     * @param reason 激活原因
     */
    void onTrayIconActivated(QSystemTrayIcon::ActivationReason reason);
    
    /**
     * @brief 处理全局热键激活
     * @param hotkeyId 热键ID
     */
    void onGlobalHotkeyActivated(const QString &hotkeyId);
    
    /**
     * @brief 处理新的客户端连接（单实例）
     */
    void onNewConnection();
    
    /**
     * @brief 处理配置变更
     * @param key 配置键
     * @param value 配置值
     */
    void onConfigChanged(const QString &key, const QVariant &value);
    
    /**
     * @brief 执行后台任务
     */
    void onBackgroundTask();

private:
    /**
     * @brief 设置日志系统
     * @return 是否成功
     */
    bool setupLogging();
    
    /**
     * @brief 设置数据库
     * @return 是否成功
     */
    bool setupDatabase();
    
    /**
     * @brief 设置服务容器
     * @return 是否成功
     */
    bool setupServices();
    
    /**
     * @brief 设置全局热键
     * @return 是否成功
     */
    bool setupHotkeys();
    
    /**
     * @brief 设置系统托盘
     * @return 是否成功
     */
    bool setupTrayIcon();
    
    /**
     * @brief 加载插件
     * @return 是否成功
     */
    bool loadPlugins();
    
    /**
     * @brief 启动后台任务
     */
    void startBackgroundTasks();
    
    /**
     * @brief 设置单实例服务器
     * @return 是否成功
     */
    bool setupSingleInstanceServer();
    
    /**
     * @brief 注册系统事件处理
     */
    void registerSystemEventHandlers();
    
    /**
     * @brief 清理资源
     */
    void cleanup();
    
    /**
     * @brief 保存应用程序状态
     */
    void saveApplicationState();
    
    /**
     * @brief 恢复应用程序状态
     */
    void restoreApplicationState();

private:
    // 核心组件
    std::unique_ptr<ServiceContainer> m_serviceContainer;
    std::unique_ptr<ConfigManager> m_configManager;
    std::unique_ptr<DatabaseManager> m_databaseManager;
    std::unique_ptr<PluginManager> m_pluginManager;
    std::unique_ptr<HotkeyManager> m_hotkeyManager;
    
    // UI组件
    std::unique_ptr<MainWindow> m_mainWindow;
    std::unique_ptr<TrayIcon> m_trayIcon;
    
    // 业务组件
    std::shared_ptr<LaunchManager> m_launchManager;
    std::shared_ptr<SearchEngine> m_searchEngine;
    std::shared_ptr<RecommendationSystem> m_recommendationSystem;

    // 新增管理器
    std::shared_ptr<DisplayModeManager> m_displayModeManager;
    std::shared_ptr<VoiceManager> m_voiceManager;
    std::shared_ptr<SoundEffectManager> m_soundEffectManager;
    
    // 系统组件
    std::unique_ptr<QLocalServer> m_singleInstanceServer;
    std::unique_ptr<QTimer> m_backgroundTaskTimer;
    
    // 状态变量
    bool m_initialized = false;
    bool m_shuttingDown = false;
    QString m_applicationDataPath;
    
    // 静态实例
    static Application* s_instance;
    
    // 常量定义
    static const QString SINGLE_INSTANCE_SERVER_NAME;
    static const int BACKGROUND_TASK_INTERVAL = 60000; // 1分钟
};
