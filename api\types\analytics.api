syntax = "v1"

// ============================================================================
// 分析统计相关类型定义
// ============================================================================

// 用户统计请求
type GetUserStatsReq {
    TimeRange   string `form:"time_range,optional"`                       // 时间范围: 1d/7d/30d/90d
    Granularity string `form:"granularity,optional"`                      // 粒度: hour/day/week/month
    MetricTypes []string `form:"metric_types,optional"`                    // 指标类型过滤
    IncludeComparison bool `form:"include_comparison,optional"`            // 是否包含对比数据
}

// 用户统计响应
type GetUserStatsResp {
    BaseResp
    Data struct {
        Summary struct {
            TotalLaunches       int64   `json:"total_launches"`        // 总启动次数
            UniqueApps          int64   `json:"unique_apps"`           // 使用的不同应用数
            AvgLaunchesPerDay   float64 `json:"avg_launches_per_day"`  // 日均启动次数
            MostActiveHour      int     `json:"most_active_hour"`      // 最活跃时间
            MostActiveDay       int     `json:"most_active_day"`       // 最活跃星期
            TotalActiveTime     int64   `json:"total_active_time"`     // 总活跃时间(分钟)
            AvgSessionLength    float64 `json:"avg_session_length"`    // 平均会话长度(分钟)
            ProductivityScore   float64 `json:"productivity_score"`    // 生产力指数(0-100)
            EfficiencyTrend     string  `json:"efficiency_trend"`      // 效率趋势: up/down/stable
        } `json:"summary"`
        Timeline []TimeSeriesPoint `json:"timeline"`                  // 时间线数据
        TopApps []struct {
            LaunchItemId   int64   `json:"launch_item_id"`   // 启动项ID
            LaunchItemName string  `json:"launch_item_name"` // 启动项名称
            Category       string  `json:"category"`         // 分类
            Launches       int64   `json:"launches"`         // 启动次数
            Percentage     float64 `json:"percentage"`       // 占比
            AvgSessionTime int64   `json:"avg_session_time"` // 平均使用时长(分钟)
            Trend          string  `json:"trend"`            // 趋势: up/down/stable
        } `json:"top_apps"` // 最常用应用
        TopCategories []struct {
            CategoryId   int64   `json:"category_id"`   // 分类ID
            CategoryName string  `json:"category_name"` // 分类名称
            Launches     int64   `json:"launches"`      // 启动次数
            Percentage   float64 `json:"percentage"`    // 占比
            AppCount     int     `json:"app_count"`     // 应用数量
            Trend        string  `json:"trend"`         // 趋势
        } `json:"top_categories"` // 最常用分类
        HourlyDistribution []struct {
            Hour       int     `json:"hour"`        // 小时(0-23)
            Launches   int64   `json:"launches"`    // 启动次数
            Percentage float64 `json:"percentage"`  // 占比
            Intensity  string  `json:"intensity"`   // 强度: low/medium/high
        } `json:"hourly_distribution"` // 小时分布
        WeeklyDistribution []struct {
            DayOfWeek  int     `json:"day_of_week"` // 星期几(1-7)
            DayName    string  `json:"day_name"`    // 星期名称
            Launches   int64   `json:"launches"`    // 启动次数
            Percentage float64 `json:"percentage"`  // 占比
            IsWorkday  bool    `json:"is_workday"`  // 是否工作日
        } `json:"weekly_distribution"` // 星期分布
        Comparison struct {
            PreviousPeriod struct {
                TotalLaunches     int64   `json:"total_launches"`      // 上期总启动次数
                UniqueApps        int64   `json:"unique_apps"`         // 上期不同应用数
                AvgLaunchesPerDay float64 `json:"avg_launches_per_day"` // 上期日均启动次数
                ProductivityScore float64 `json:"productivity_score"`  // 上期生产力指数
            } `json:"previous_period"`
            Changes struct {
                LaunchesChange      float64 `json:"launches_change"`       // 启动次数变化率
                AppsChange          float64 `json:"apps_change"`           // 应用数变化率
                ProductivityChange  float64 `json:"productivity_change"`   // 生产力变化率
                EfficiencyChange    float64 `json:"efficiency_change"`     // 效率变化率
            } `json:"changes"`
        } `json:"comparison,optional"` // 对比数据
    } `json:"data"`
}

// 使用趋势请求
type GetUsageTrendsReq {
    TimeRange   string   `form:"time_range,optional"`                      // 时间范围: 7d/30d/90d/1y
    GroupBy     string   `form:"group_by,optional"`                        // 分组方式: day/week/month
    MetricType  string   `form:"metric_type,optional"`                     // 指标类型: launches/apps/categories/time
    CategoryIds []int64  `form:"category_ids,optional"`                    // 分类ID过滤
    AppIds      []string `form:"app_ids,optional"`                         // 应用ID过滤
    Smooth      bool     `form:"smooth,optional"`                          // 是否平滑处理
}

// 使用趋势响应
type GetUsageTrendsResp {
    BaseResp
    Data struct {
        Trends []struct {
            Name        string            `json:"name"`         // 趋势名称
            Type        string            `json:"type"`         // 类型: app/category/overall
            Timeline    []TimeSeriesPoint `json:"timeline"`     // 时间线数据
            Trend       string            `json:"trend"`        // 趋势方向: up/down/stable
            ChangeRate  float64           `json:"change_rate"`  // 变化率
            Correlation float64           `json:"correlation"`  // 相关性
            Seasonality struct {
                HasPattern bool     `json:"has_pattern"`  // 是否有季节性模式
                Period     int      `json:"period"`       // 周期(天)
                Strength   float64  `json:"strength"`     // 强度
            } `json:"seasonality"` // 季节性分析
        } `json:"trends"`
        Insights []struct {
            Type        string `json:"type"`         // 洞察类型: peak/decline/pattern/anomaly
            Title       string `json:"title"`        // 标题
            Description string `json:"description"`  // 描述
            Confidence  float64 `json:"confidence"`  // 置信度
            ActionItems []string `json:"action_items,optional"` // 行动建议
        } `json:"insights"` // 趋势洞察
        Forecast []struct {
            Date           string  `json:"date"`            // 预测日期
            PredictedValue float64 `json:"predicted_value"` // 预测值
            ConfidenceInterval struct {
                Lower float64 `json:"lower"` // 置信区间下限
                Upper float64 `json:"upper"` // 置信区间上限
            } `json:"confidence_interval"`
        } `json:"forecast,optional"` // 趋势预测
    } `json:"data"`
}

// 推荐效果统计请求
type GetRecommendationPerformanceReq {
    TimeRange   string `form:"time_range,optional"`                         // 时间范围: 1d/7d/30d/90d
    Algorithm   string `form:"algorithm,optional"`                          // 算法过滤
    Category    string `form:"category,optional"`                           // 分类过滤
    MetricType  string `form:"metric_type,optional"`                        // 指标类型: ctr/conversion/rating/satisfaction
    GroupBy     string `form:"group_by,optional"`                           // 分组方式: algorithm/category/time
}

// 推荐效果统计响应
type GetRecommendationPerformanceResp {
    BaseResp
    Data struct {
        Overall struct {
            TotalRecommendations int64   `json:"total_recommendations"` // 总推荐次数
            ClickThroughRate     float64 `json:"click_through_rate"`    // 点击率
            InstallationRate     float64 `json:"installation_rate"`     // 安装率
            AvgRating            float64 `json:"avg_rating"`            // 平均评分
            UserSatisfaction     float64 `json:"user_satisfaction"`     // 用户满意度
            RevenueImpact        float64 `json:"revenue_impact"`        // 收入影响
            CostPerAcquisition   float64 `json:"cost_per_acquisition"`  // 获客成本
        } `json:"overall"` // 总体统计
        ByAlgorithm []struct {
            Algorithm        string  `json:"algorithm"`         // 算法名称
            Recommendations  int64   `json:"recommendations"`   // 推荐次数
            CTR              float64 `json:"ctr"`               // 点击率
            ConversionRate   float64 `json:"conversion_rate"`   // 转化率
            AvgScore         float64 `json:"avg_score"`         // 平均分数
            AvgRating        float64 `json:"avg_rating"`        // 平均评分
            Performance      string  `json:"performance"`       // 性能评级: excellent/good/fair/poor
            Improvement      float64 `json:"improvement"`       // 改进幅度
        } `json:"by_algorithm"` // 按算法统计
        ByCategory []struct {
            Category        string   `json:"category"`         // 分类名称
            Recommendations int64    `json:"recommendations"`  // 推荐次数
            CTR             float64  `json:"ctr"`              // 点击率
            ConversionRate  float64  `json:"conversion_rate"`  // 转化率
            PopularApps     []string `json:"popular_apps"`     // 热门应用
            TrendDirection  string   `json:"trend_direction"`  // 趋势方向: up/down/stable
            MarketShare     float64  `json:"market_share"`     // 市场份额
        } `json:"by_category"` // 按分类统计
        Timeline []struct {
            Date            string  `json:"date"`             // 日期
            Recommendations int64   `json:"recommendations"`  // 推荐次数
            Clicks          int64   `json:"clicks"`           // 点击次数
            Installs        int64   `json:"installs"`         // 安装次数
            CTR             float64 `json:"ctr"`              // 点击率
            ConversionRate  float64 `json:"conversion_rate"`  // 转化率
            Revenue         float64 `json:"revenue"`          // 收入
        } `json:"timeline"` // 时间线数据
        TopPerforming []struct {
            AppId           string  `json:"app_id"`           // 应用ID
            AppName         string  `json:"app_name"`         // 应用名称
            Category        string  `json:"category"`         // 分类
            Recommendations int64   `json:"recommendations"`  // 推荐次数
            CTR             float64 `json:"ctr"`              // 点击率
            ConversionRate  float64 `json:"conversion_rate"`  // 转化率
            AvgRating       float64 `json:"avg_rating"`       // 平均评分
            RevenueShare    float64 `json:"revenue_share"`    // 收入占比
            GrowthRate      float64 `json:"growth_rate"`      // 增长率
        } `json:"top_performing"` // 表现最好的应用
        Optimization struct {
            Recommendations []struct {
                Type        string `json:"type"`         // 优化类型: algorithm/targeting/content
                Title       string `json:"title"`        // 标题
                Description string `json:"description"`  // 描述
                Impact      string `json:"impact"`       // 预期影响: high/medium/low
                Effort      string `json:"effort"`       // 实施难度: high/medium/low
                Priority    int    `json:"priority"`     // 优先级
            } `json:"recommendations"` // 优化建议
            ABTests []struct {
                Name        string  `json:"name"`         // 测试名称
                Status      string  `json:"status"`       // 状态: running/completed/planned
                Hypothesis  string  `json:"hypothesis"`   // 假设
                Results     string  `json:"results"`      // 结果
                Confidence  float64 `json:"confidence"`   // 置信度
                Impact      float64 `json:"impact"`       // 影响
            } `json:"ab_tests"` // A/B测试
        } `json:"optimization"` // 优化建议
    } `json:"data"`
}

// 搜索统计请求
type GetSearchStatsReq {
    TimeRange   string `form:"time_range,optional"`                         // 时间范围: 1h/1d/7d/30d
    GroupBy     string `form:"group_by,optional"`                           // 分组方式: hour/day/query/result_type
    MetricType  string `form:"metric_type,optional"`                        // 指标类型: count/response_time/success_rate/ctr
    QueryType   string `form:"query_type,optional"`                         // 查询类型过滤
    MinResults  int    `form:"min_results,optional"`                        // 最小结果数过滤
}

// 搜索统计响应
type GetSearchStatsResp {
    BaseResp
    Data struct {
        Summary struct {
            TotalSearches       int64   `json:"total_searches"`        // 总搜索次数
            UniqueQueries       int64   `json:"unique_queries"`        // 唯一查询数
            AvgSearchesPerDay   float64 `json:"avg_searches_per_day"`  // 日均搜索次数
            SuccessRate         float64 `json:"success_rate"`          // 成功率
            AvgResponseTime     float64 `json:"avg_response_time"`     // 平均响应时间(毫秒)
            AvgResultsPerQuery  float64 `json:"avg_results_per_query"` // 平均每查询结果数
            ZeroResultRate      float64 `json:"zero_result_rate"`      // 零结果率
            ClickThroughRate    float64 `json:"click_through_rate"`    // 点击率
            QueryRefinementRate float64 `json:"query_refinement_rate"` // 查询优化率
            UserSatisfaction    float64 `json:"user_satisfaction"`     // 用户满意度
        } `json:"summary"`
        Timeline []TimeSeriesPoint `json:"timeline"`                  // 时间线数据
        TopQueries []struct {
            Query           string  `json:"query"`            // 查询关键词
            Count           int64   `json:"count"`            // 搜索次数
            Percentage      float64 `json:"percentage"`       // 占比
            SuccessRate     float64 `json:"success_rate"`     // 成功率
            AvgResults      float64 `json:"avg_results"`      // 平均结果数
            AvgResponseTime float64 `json:"avg_response_time"` // 平均响应时间
            CTR             float64 `json:"ctr"`              // 点击率
            Trend           string  `json:"trend"`            // 趋势
        } `json:"top_queries"` // 热门查询
        FailedQueries []struct {
            Query       string   `json:"query"`        // 查询关键词
            Count       int64    `json:"count"`        // 失败次数
            LastFailed  string   `json:"last_failed"`  // 最后失败时间
            Reasons     []string `json:"reasons"`      // 失败原因
            Suggestions []string `json:"suggestions,optional"` // 建议查询
            FixPriority string   `json:"fix_priority"` // 修复优先级: high/medium/low
        } `json:"failed_queries"` // 失败查询
        ResponseTimeDistribution []struct {
            Range      string  `json:"range"`       // 时间范围: <100ms, 100-300ms等
            Count      int64   `json:"count"`       // 数量
            Percentage float64 `json:"percentage"`  // 占比
            UserImpact string  `json:"user_impact"` // 用户影响: good/acceptable/poor
        } `json:"response_time_distribution"` // 响应时间分布
        QueryTypes []struct {
            Type        string  `json:"type"`         // 查询类型: exact/fuzzy/wildcard/phrase
            Count       int64   `json:"count"`        // 数量
            Percentage  float64 `json:"percentage"`   // 占比
            SuccessRate float64 `json:"success_rate"` // 成功率
            AvgResults  float64 `json:"avg_results"`  // 平均结果数
        } `json:"query_types"` // 查询类型分布
        SearchPaths []struct {
            Path        string  `json:"path"`         // 搜索路径: direct/suggestion/refinement
            Count       int64   `json:"count"`        // 数量
            Percentage  float64 `json:"percentage"`   // 占比
            SuccessRate float64 `json:"success_rate"` // 成功率
            Conversion  float64 `json:"conversion"`   // 转化率
        } `json:"search_paths"` // 搜索路径分析
    } `json:"data"`
}

// 系统健康状态请求
type GetSystemHealthReq {
    Components []string `form:"components,optional"`                        // 组件过滤
    Detailed   bool     `form:"detailed,optional"`                          // 是否详细信息
}

// 系统健康状态响应
type GetSystemHealthResp {
    BaseResp
    Data struct {
        Overall struct {
            Status      string  `json:"status"`       // 整体状态: healthy/degraded/unhealthy
            Score       float64 `json:"score"`        // 健康分数(0-100)
            LastChecked string  `json:"last_checked"` // 最后检查时间
            Uptime      int64   `json:"uptime"`       // 运行时间(秒)
            Issues      int     `json:"issues"`       // 问题数量
        } `json:"overall"`
        Components []struct {
            Name        string  `json:"name"`         // 组件名称
            Status      string  `json:"status"`       // 状态: healthy/degraded/unhealthy/unknown
            Score       float64 `json:"score"`        // 健康分数
            Message     string  `json:"message"`      // 状态消息
            LastChecked string  `json:"last_checked"` // 最后检查时间
            ResponseTime int    `json:"response_time"` // 响应时间(毫秒)
            Metrics     struct {
                CPU         float64 `json:"cpu"`          // CPU使用率
                Memory      float64 `json:"memory"`       // 内存使用率
                Disk        float64 `json:"disk"`         // 磁盘使用率
                Network     float64 `json:"network"`      // 网络使用率
                Connections int     `json:"connections"`  // 连接数
                QueueSize   int     `json:"queue_size"`   // 队列大小
                ErrorRate   float64 `json:"error_rate"`   // 错误率
                Throughput  float64 `json:"throughput"`   // 吞吐量
            } `json:"metrics,optional"` // 详细指标
            Dependencies []string `json:"dependencies,optional"` // 依赖组件
            Alerts       []struct {
                Level   string `json:"level"`    // 告警级别: info/warning/error/critical
                Message string `json:"message"`  // 告警消息
                Time    string `json:"time"`     // 告警时间
                Count   int    `json:"count"`    // 告警次数
            } `json:"alerts,optional"` // 告警信息
        } `json:"components"` // 组件状态
        Performance struct {
            RequestsPerSecond float64 `json:"requests_per_second"` // 每秒请求数
            AvgResponseTime   float64 `json:"avg_response_time"`   // 平均响应时间
            ErrorRate         float64 `json:"error_rate"`          // 错误率
            ActiveUsers       int64   `json:"active_users"`        // 活跃用户数
            ActiveSessions    int64   `json:"active_sessions"`     // 活跃会话数
            DatabaseConnections int   `json:"database_connections"` // 数据库连接数
            CacheHitRate      float64 `json:"cache_hit_rate"`      // 缓存命中率
            QueueLength       int     `json:"queue_length"`        // 队列长度
        } `json:"performance"` // 性能指标
        Resources struct {
            CPU struct {
                Usage     float64 `json:"usage"`      // 使用率
                Available float64 `json:"available"`  // 可用率
                Cores     int     `json:"cores"`      // 核心数
            } `json:"cpu"`
            Memory struct {
                Usage     float64 `json:"usage"`      // 使用率
                Available int64   `json:"available"`  // 可用内存(字节)
                Total     int64   `json:"total"`      // 总内存(字节)
            } `json:"memory"`
            Disk struct {
                Usage     float64 `json:"usage"`      // 使用率
                Available int64   `json:"available"`  // 可用空间(字节)
                Total     int64   `json:"total"`      // 总空间(字节)
                IOPS      float64 `json:"iops"`       // IOPS
            } `json:"disk"`
            Network struct {
                InboundBandwidth  float64 `json:"inbound_bandwidth"`  // 入站带宽(Mbps)
                OutboundBandwidth float64 `json:"outbound_bandwidth"` // 出站带宽(Mbps)
                Latency           float64 `json:"latency"`             // 延迟(毫秒)
                PacketLoss        float64 `json:"packet_loss"`         // 丢包率
            } `json:"network"`
        } `json:"resources"` // 资源使用情况
        Recommendations []struct {
            Type        string `json:"type"`         // 建议类型: performance/security/maintenance
            Priority    string `json:"priority"`     // 优先级: high/medium/low
            Title       string `json:"title"`        // 标题
            Description string `json:"description"`  // 描述
            Action      string `json:"action"`       // 建议操作
            Impact      string `json:"impact"`       // 预期影响
        } `json:"recommendations,optional"` // 优化建议
    } `json:"data"`
}
