#pragma once

#include <QSystemTrayIcon>
#include <QMenu>
#include <QAction>
#include <QTimer>
#include <QPropertyAnimation>
#include <memory>

// 前置声明
class LaunchManager;
class ConfigManager;
class QLabel;
class QWidgetAction;

/**
 * @brief 系统托盘图标类
 * 
 * 提供系统托盘功能，包括：
 * - 托盘图标和菜单
 * - 快速启动功能
 * - 通知显示
 * - 状态指示
 */
class TrayIcon : public QSystemTrayIcon
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit TrayIcon(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~TrayIcon();

    /**
     * @brief 初始化托盘图标
     * @return 是否成功
     */
    bool initialize();
    
    /**
     * @brief 更新托盘图标
     * @param iconPath 图标路径
     */
    void updateIcon(const QString &iconPath = QString());
    
    /**
     * @brief 更新工具提示
     * @param tooltip 提示文本
     */
    void updateTooltip(const QString &tooltip = QString());
    
    /**
     * @brief 显示通知消息
     * @param title 标题
     * @param message 消息内容
     * @param icon 图标类型
     * @param timeout 超时时间（毫秒）
     */
    void showNotification(const QString &title, const QString &message, 
                         QSystemTrayIcon::MessageIcon icon = QSystemTrayIcon::Information,
                         int timeout = 3000);
    
    /**
     * @brief 显示气泡提示
     * @param message 消息内容
     * @param timeout 超时时间（毫秒）
     */
    void showBalloonTip(const QString &message, int timeout = 2000);
    
    /**
     * @brief 设置闪烁状态
     * @param enabled 是否启用闪烁
     * @param interval 闪烁间隔（毫秒）
     */
    void setBlinking(bool enabled, int interval = 500);
    
    /**
     * @brief 更新菜单
     */
    void updateMenu();
    
    /**
     * @brief 添加快速启动项
     * @param itemId 启动项ID
     * @param name 显示名称
     * @param iconPath 图标路径
     */
    void addQuickLaunchItem(int itemId, const QString &name, const QString &iconPath = QString());
    
    /**
     * @brief 移除快速启动项
     * @param itemId 启动项ID
     */
    void removeQuickLaunchItem(int itemId);
    
    /**
     * @brief 清空快速启动项
     */
    void clearQuickLaunchItems();
    
    /**
     * @brief 设置状态指示
     * @param status 状态文本
     * @param color 状态颜色
     */
    void setStatusIndicator(const QString &status, const QColor &color = QColor());

public slots:
    /**
     * @brief 显示主窗口
     */
    void showMainWindow();
    
    /**
     * @brief 隐藏主窗口
     */
    void hideMainWindow();
    
    /**
     * @brief 切换主窗口显示状态
     */
    void toggleMainWindow();
    
    /**
     * @brief 显示设置对话框
     */
    void showSettings();
    
    /**
     * @brief 显示统计窗口
     */
    void showStatistics();
    
    /**
     * @brief 显示关于对话框
     */
    void showAbout();
    
    /**
     * @brief 退出应用程序
     */
    void quitApplication();

signals:
    /**
     * @brief 显示主窗口请求信号
     */
    void showMainWindowRequested();
    
    /**
     * @brief 隐藏主窗口请求信号
     */
    void hideMainWindowRequested();
    
    /**
     * @brief 切换主窗口请求信号
     */
    void toggleMainWindowRequested();
    
    /**
     * @brief 显示设置请求信号
     */
    void showSettingsRequested();
    
    /**
     * @brief 显示统计请求信号
     */
    void showStatisticsRequested();
    
    /**
     * @brief 退出应用程序请求信号
     */
    void quitRequested();
    
    /**
     * @brief 快速启动项点击信号
     * @param itemId 启动项ID
     */
    void quickLaunchItemClicked(int itemId);

private slots:
    /**
     * @brief 托盘图标激活处理
     * @param reason 激活原因
     */
    void onActivated(QSystemTrayIcon::ActivationReason reason);
    
    /**
     * @brief 消息点击处理
     */
    void onMessageClicked();
    
    /**
     * @brief 闪烁定时器超时
     */
    void onBlinkTimeout();
    
    /**
     * @brief 快速启动项触发
     */
    void onQuickLaunchTriggered();
    
    /**
     * @brief 配置变更处理
     * @param key 配置键
     * @param value 配置值
     */
    void onConfigChanged(const QString &key, const QVariant &value);
    
    /**
     * @brief 启动项变更处理
     */
    void onLaunchItemsChanged();

private:
    /**
     * @brief 创建上下文菜单
     */
    void createContextMenu();
    
    /**
     * @brief 创建快速启动菜单
     */
    void createQuickLaunchMenu();
    
    /**
     * @brief 更新快速启动菜单
     */
    void updateQuickLaunchMenu();
    
    /**
     * @brief 加载图标
     * @param iconPath 图标路径
     * @return 图标对象
     */
    QIcon loadIcon(const QString &iconPath) const;
    
    /**
     * @brief 获取默认图标
     * @return 默认图标
     */
    QIcon getDefaultIcon() const;
    
    /**
     * @brief 获取状态图标
     * @param active 是否活动状态
     * @return 状态图标
     */
    QIcon getStatusIcon(bool active = true) const;
    
    /**
     * @brief 创建状态指示器
     * @return 状态指示器部件
     */
    QWidget* createStatusIndicator();
    
    /**
     * @brief 更新状态指示器
     */
    void updateStatusIndicator();
    
    /**
     * @brief 加载设置
     */
    void loadSettings();
    
    /**
     * @brief 保存设置
     */
    void saveSettings();

private:
    // 菜单和动作
    std::unique_ptr<QMenu> m_contextMenu;
    std::unique_ptr<QMenu> m_quickLaunchMenu;
    
    QAction *m_showAction;
    QAction *m_hideAction;
    QAction *m_settingsAction;
    QAction *m_statisticsAction;
    QAction *m_aboutAction;
    QAction *m_quitAction;
    QAction *m_separatorAction;
    
    // 快速启动项
    QHash<int, QAction*> m_quickLaunchActions;
    QAction *m_quickLaunchSeparator;
    QAction *m_manageQuickLaunchAction;
    
    // 状态指示器
    QWidgetAction *m_statusAction;
    QLabel *m_statusLabel;
    QString m_currentStatus;
    QColor m_statusColor;
    
    // 闪烁效果
    QTimer *m_blinkTimer;
    bool m_blinkState = false;
    QIcon m_normalIcon;
    QIcon m_blinkIcon;
    
    // 动画效果
    QPropertyAnimation *m_iconAnimation;
    
    // 服务引用
    std::shared_ptr<LaunchManager> m_launchManager;
    std::shared_ptr<ConfigManager> m_configManager;
    
    // 配置选项
    bool m_showNotifications = true;
    bool m_minimizeToTray = true;
    bool m_closeToTray = true;
    bool m_startMinimized = false;
    bool m_showQuickLaunch = true;
    int m_maxQuickLaunchItems = 10;
    QString m_iconTheme = "default";
    
    // 状态变量
    bool m_initialized = false;
    bool m_mainWindowVisible = false;
    
    // 常量
    static const int DEFAULT_NOTIFICATION_TIMEOUT = 3000;
    static const int DEFAULT_BLINK_INTERVAL = 500;
    static const int MAX_TOOLTIP_LENGTH = 100;
    static const int MAX_NOTIFICATION_LENGTH = 200;
};

/**
 * @brief 托盘图标工厂类
 * 
 * 用于创建和管理托盘图标实例
 */
class TrayIconFactory
{
public:
    /**
     * @brief 创建托盘图标
     * @param parent 父对象
     * @return 托盘图标实例
     */
    static std::unique_ptr<TrayIcon> create(QObject *parent = nullptr);
    
    /**
     * @brief 检查系统托盘是否可用
     * @return 是否可用
     */
    static bool isSystemTrayAvailable();
    
    /**
     * @brief 检查托盘图标是否支持
     * @return 是否支持
     */
    static bool isTrayIconSupported();
    
    /**
     * @brief 获取推荐的图标大小
     * @return 图标大小
     */
    static QSize getRecommendedIconSize();
};
