#include "DisplayModeManager.h"
#include "presentation/views/MainWindow.h"
#include "presentation/views/MiniWindow.h"
#include "presentation/views/TaskbarWindow.h"
#include "business/managers/VoiceManager.h"
#include "business/managers/ConfigManager.h"
#include <QApplication>
#include <QScreen>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QLoggingCategory>
#include <QTimer>

Q_LOGGING_CATEGORY(lcDisplayMode, "displaymode")

DisplayModeManager::DisplayModeManager(QObject *parent)
    : QObject(parent)
    , m_temporaryModeTimer(new QTimer(this))
{
    // 设置临时模式定时器
    m_temporaryModeTimer->setSingleShot(true);
    connect(m_temporaryModeTimer, &QTimer::timeout, this, &DisplayModeManager::onTemporaryModeTimeout);
    
    qCDebug(lcDisplayMode) << "DisplayModeManager created";
}

DisplayModeManager::~DisplayModeManager()
{
    stopAllAnimations();
    qCDebug(lcDisplayMode) << "DisplayModeManager destroyed";
}

bool DisplayModeManager::initialize()
{
    qCInfo(lcDisplayMode) << "Initializing DisplayModeManager";
    
    // 加载配置
    loadConfiguration();
    
    // 初始化语音管理器
    initializeVoiceManager();
    
    // 创建默认窗口
    m_mainWindow = std::make_unique<MainWindow>();
    m_currentWindow = m_mainWindow.get();
    
    // 连接配置变更信号
    if (m_configManager) {
        connect(m_configManager.get(), &ConfigManager::valueChanged,
                this, &DisplayModeManager::onConfigChanged);
    }
    
    qCInfo(lcDisplayMode) << "DisplayModeManager initialized successfully";
    return true;
}

DisplayMode DisplayModeManager::currentMode() const
{
    return m_currentMode;
}

void DisplayModeManager::setCurrentMode(DisplayMode mode)
{
    if (m_currentMode == mode) {
        return;
    }
    
    DisplayMode oldMode = m_currentMode;
    m_currentMode = mode;
    
    emit currentModeChanged(mode, oldMode);
    qCDebug(lcDisplayMode) << "Display mode changed from" << static_cast<int>(oldMode) 
                          << "to" << static_cast<int>(mode);
}

bool DisplayModeManager::animationsEnabled() const
{
    return m_animationsEnabled;
}

void DisplayModeManager::setAnimationsEnabled(bool enabled)
{
    if (m_animationsEnabled == enabled) {
        return;
    }
    
    m_animationsEnabled = enabled;
    emit animationsEnabledChanged(enabled);
    
    qCDebug(lcDisplayMode) << "Animations" << (enabled ? "enabled" : "disabled");
}

bool DisplayModeManager::soundEnabled() const
{
    return m_soundEnabled;
}

void DisplayModeManager::setSoundEnabled(bool enabled)
{
    if (m_soundEnabled == enabled) {
        return;
    }
    
    m_soundEnabled = enabled;
    emit soundEnabledChanged(enabled);
    
    qCDebug(lcDisplayMode) << "Sound" << (enabled ? "enabled" : "disabled");
}

double DisplayModeManager::opacity() const
{
    return m_opacity;
}

void DisplayModeManager::setOpacity(double opacity)
{
    opacity = qBound(0.0, opacity, 1.0);
    
    if (qFuzzyCompare(m_opacity, opacity)) {
        return;
    }
    
    m_opacity = opacity;
    
    // 应用到当前窗口
    if (m_currentWindow) {
        m_currentWindow->setWindowOpacity(opacity);
    }
    
    emit opacityChanged(opacity);
    qCDebug(lcDisplayMode) << "Opacity changed to" << opacity;
}

void DisplayModeManager::switchToMode(DisplayMode mode, bool animated)
{
    if (m_currentMode == mode) {
        return;
    }
    
    qCDebug(lcDisplayMode) << "Switching to mode" << static_cast<int>(mode) 
                          << "animated:" << animated;
    
    // 停止临时模式定时器
    m_temporaryModeTimer->stop();
    
    // 创建新窗口（如果需要）
    QWidget *newWindow = createWindow(mode);
    if (!newWindow) {
        qCWarning(lcDisplayMode) << "Failed to create window for mode" << static_cast<int>(mode);
        return;
    }
    
    // 保存之前的模式
    m_previousMode = m_currentMode;
    
    // 执行模式切换
    if (animated && m_animationsEnabled) {
        // 隐藏当前窗口
        if (m_currentWindow && m_currentWindow->isVisible()) {
            hideWindow(true);
        }
        
        // 延迟显示新窗口
        QTimer::singleShot(DEFAULT_ANIMATION_DURATION, this, [this, newWindow, mode]() {
            m_currentWindow = newWindow;
            setCurrentMode(mode);
            showWindow(true);
        });
    } else {
        // 立即切换
        if (m_currentWindow && m_currentWindow->isVisible()) {
            m_currentWindow->hide();
        }
        
        m_currentWindow = newWindow;
        setCurrentMode(mode);
        showWindow(false);
    }
}

void DisplayModeManager::switchToNextMode()
{
    QList<DisplayMode> modes = getAvailableModes();
    int currentIndex = modes.indexOf(m_currentMode);
    int nextIndex = (currentIndex + 1) % modes.size();
    
    switchToMode(modes[nextIndex]);
}

void DisplayModeManager::switchToPreviousMode()
{
    QList<DisplayMode> modes = getAvailableModes();
    int currentIndex = modes.indexOf(m_currentMode);
    int prevIndex = (currentIndex - 1 + modes.size()) % modes.size();
    
    switchToMode(modes[prevIndex]);
}

void DisplayModeManager::showTemporaryMode(DisplayMode mode, int duration)
{
    if (m_currentMode == mode) {
        return;
    }
    
    qCDebug(lcDisplayMode) << "Showing temporary mode" << static_cast<int>(mode) 
                          << "for" << duration << "ms";
    
    // 保存当前模式
    DisplayMode originalMode = m_currentMode;
    
    // 切换到临时模式
    switchToMode(mode, true);
    
    // 设置定时器恢复原模式
    m_temporaryModeTimer->setInterval(duration);
    m_temporaryModeTimer->start();
    
    // 保存原模式以便恢复
    m_previousMode = originalMode;
}

QWidget* DisplayModeManager::getCurrentWindow() const
{
    return m_currentWindow;
}

QList<DisplayMode> DisplayModeManager::getAvailableModes() const
{
    QList<DisplayMode> modes;
    modes << DisplayMode::Full << DisplayMode::Mini << DisplayMode::Taskbar;
    
    // 检查语音模式是否可用
    if (m_voiceEnabled && m_voiceManager && m_voiceManager->isRecognitionAvailable()) {
        modes << DisplayMode::Voice;
    }
    
    return modes;
}

void DisplayModeManager::playAnimation(QWidget *widget, AnimationType type, int duration)
{
    if (!widget || !m_animationsEnabled) {
        return;
    }
    
    QPropertyAnimation *animation = nullptr;
    
    switch (type) {
    case AnimationType::FadeIn:
        animation = new QPropertyAnimation(widget, "windowOpacity", this);
        animation->setStartValue(0.0);
        animation->setEndValue(m_opacity);
        break;
        
    case AnimationType::FadeOut:
        animation = new QPropertyAnimation(widget, "windowOpacity", this);
        animation->setStartValue(widget->windowOpacity());
        animation->setEndValue(0.0);
        break;
        
    case AnimationType::SlideIn:
        animation = new QPropertyAnimation(widget, "pos", this);
        {
            QPoint startPos = widget->pos();
            startPos.setY(startPos.y() - 50);
            animation->setStartValue(startPos);
            animation->setEndValue(widget->pos());
        }
        break;
        
    case AnimationType::SlideOut:
        animation = new QPropertyAnimation(widget, "pos", this);
        {
            QPoint endPos = widget->pos();
            endPos.setY(endPos.y() - 50);
            animation->setStartValue(widget->pos());
            animation->setEndValue(endPos);
        }
        break;
        
    case AnimationType::Scale:
        animation = new QPropertyAnimation(widget, "geometry", this);
        {
            QRect startRect = widget->geometry();
            QRect endRect = startRect;
            startRect.setSize(startRect.size() * 0.8);
            animation->setStartValue(startRect);
            animation->setEndValue(endRect);
        }
        break;
        
    case AnimationType::Bounce:
        // 弹跳效果需要更复杂的实现
        animation = new QPropertyAnimation(widget, "pos", this);
        animation->setStartValue(widget->pos());
        animation->setEndValue(widget->pos());
        animation->setEasingCurve(QEasingCurve::OutBounce);
        break;
    }
    
    if (animation) {
        animation->setDuration(static_cast<int>(duration * m_animationSpeed));
        connect(animation, &QPropertyAnimation::finished, this, &DisplayModeManager::onAnimationFinished);
        
        m_activeAnimations.append(animation);
        animation->start(QAbstractAnimation::DeleteWhenStopped);
        
        qCDebug(lcDisplayMode) << "Started animation type" << static_cast<int>(type) 
                              << "duration" << duration;
    }
}

void DisplayModeManager::stopAllAnimations()
{
    for (QPropertyAnimation *animation : m_activeAnimations) {
        if (animation) {
            animation->stop();
        }
    }
    m_activeAnimations.clear();
    
    qCDebug(lcDisplayMode) << "All animations stopped";
}

void DisplayModeManager::setAnimationSpeed(double speed)
{
    m_animationSpeed = qBound(0.1, speed, 3.0);
    qCDebug(lcDisplayMode) << "Animation speed set to" << m_animationSpeed;
}

void DisplayModeManager::setVoiceEnabled(bool enabled)
{
    if (m_voiceEnabled == enabled) {
        return;
    }
    
    m_voiceEnabled = enabled;
    
    if (enabled && !m_voiceManager) {
        initializeVoiceManager();
    }
    
    qCDebug(lcDisplayMode) << "Voice" << (enabled ? "enabled" : "disabled");
}

bool DisplayModeManager::isVoiceAvailable() const
{
    return m_voiceManager && m_voiceManager->isRecognitionAvailable();
}

void DisplayModeManager::startVoiceRecognition()
{
    if (m_voiceManager && m_voiceEnabled) {
        m_voiceManager->startRecognition();
        qCDebug(lcDisplayMode) << "Voice recognition started";
    }
}

void DisplayModeManager::stopVoiceRecognition()
{
    if (m_voiceManager) {
        m_voiceManager->stopRecognition();
        qCDebug(lcDisplayMode) << "Voice recognition stopped";
    }
}

void DisplayModeManager::speakText(const QString &text)
{
    if (m_voiceManager && m_voiceEnabled && m_soundEnabled) {
        m_voiceManager->speak(text);
        qCDebug(lcDisplayMode) << "Speaking text:" << text;
    }
}

void DisplayModeManager::showWindow(bool animated)
{
    if (!m_currentWindow) {
        return;
    }

    // 设置窗口位置和大小
    setWindowPosition(m_currentMode);
    resizeWindow(m_currentMode);

    // 设置透明度
    m_currentWindow->setWindowOpacity(animated ? 0.0 : m_opacity);

    // 显示窗口
    m_currentWindow->show();
    m_currentWindow->raise();
    m_currentWindow->activateWindow();

    // 播放动画
    if (animated && m_animationsEnabled) {
        playAnimation(m_currentWindow, AnimationType::FadeIn, DEFAULT_ANIMATION_DURATION);
    }

    emit windowVisibilityChanged(true);
    qCDebug(lcDisplayMode) << "Window shown, animated:" << animated;
}

void DisplayModeManager::hideWindow(bool animated)
{
    if (!m_currentWindow || !m_currentWindow->isVisible()) {
        return;
    }

    if (animated && m_animationsEnabled) {
        // 播放淡出动画后隐藏
        playAnimation(m_currentWindow, AnimationType::FadeOut, DEFAULT_ANIMATION_DURATION);

        QTimer::singleShot(DEFAULT_ANIMATION_DURATION, this, [this]() {
            if (m_currentWindow) {
                m_currentWindow->hide();
            }
        });
    } else {
        m_currentWindow->hide();
    }

    emit windowVisibilityChanged(false);
    qCDebug(lcDisplayMode) << "Window hidden, animated:" << animated;
}

void DisplayModeManager::toggleWindowVisibility()
{
    if (m_currentWindow) {
        if (m_currentWindow->isVisible()) {
            hideWindow(true);
        } else {
            showWindow(true);
        }
    }
}

void DisplayModeManager::setWindowPosition(DisplayMode mode)
{
    if (!m_currentWindow) {
        return;
    }

    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    QRect availableGeometry = screen->availableGeometry();

    QPoint position;

    switch (mode) {
    case DisplayMode::Mini:
        // 迷你模式：屏幕中央偏上
        position.setX((screenGeometry.width() - m_currentWindow->width()) / 2);
        position.setY(screenGeometry.height() / 3);
        break;

    case DisplayMode::Taskbar:
        // 任务栏模式：底部居中
        position.setX((availableGeometry.width() - m_currentWindow->width()) / 2);
        position.setY(availableGeometry.bottom() - m_currentWindow->height() - 10);
        break;

    case DisplayMode::Full:
        // 完整模式：屏幕中央
        position.setX((screenGeometry.width() - m_currentWindow->width()) / 2);
        position.setY((screenGeometry.height() - m_currentWindow->height()) / 2);
        break;

    case DisplayMode::Voice:
        // 语音模式：右下角
        position.setX(availableGeometry.right() - m_currentWindow->width() - 20);
        position.setY(availableGeometry.bottom() - m_currentWindow->height() - 20);
        break;

    default:
        position = m_currentWindow->pos();
        break;
    }

    m_currentWindow->move(position);
    qCDebug(lcDisplayMode) << "Window position set to" << position << "for mode" << static_cast<int>(mode);
}

void DisplayModeManager::resizeWindow(DisplayMode mode)
{
    if (!m_currentWindow) {
        return;
    }

    QSize size;

    switch (mode) {
    case DisplayMode::Mini:
        size = QSize(400, 300);
        break;

    case DisplayMode::Taskbar:
        size = QSize(600, 60);
        break;

    case DisplayMode::Full:
        size = QSize(1000, 700);
        break;

    case DisplayMode::Voice:
        size = QSize(300, 200);
        break;

    default:
        size = m_currentWindow->size();
        break;
    }

    m_currentWindow->resize(size);
    qCDebug(lcDisplayMode) << "Window resized to" << size << "for mode" << static_cast<int>(mode);
}

void DisplayModeManager::onAnimationFinished()
{
    QPropertyAnimation *animation = qobject_cast<QPropertyAnimation*>(sender());
    if (animation) {
        m_activeAnimations.removeAll(animation);
        qCDebug(lcDisplayMode) << "Animation finished";
    }
}

void DisplayModeManager::onTemporaryModeTimeout()
{
    qCDebug(lcDisplayMode) << "Temporary mode timeout, switching back to previous mode";
    switchToMode(m_previousMode, true);
}

void DisplayModeManager::onConfigChanged(const QString &key, const QVariant &value)
{
    if (key == "display.animations_enabled") {
        setAnimationsEnabled(value.toBool());
    } else if (key == "display.sound_enabled") {
        setSoundEnabled(value.toBool());
    } else if (key == "display.opacity") {
        setOpacity(value.toDouble());
    } else if (key == "display.animation_speed") {
        setAnimationSpeed(value.toDouble());
    } else if (key == "voice.enabled") {
        setVoiceEnabled(value.toBool());
    }

    qCDebug(lcDisplayMode) << "Config changed:" << key << "=" << value;
}

void DisplayModeManager::onVoiceRecognitionResult(const QString &text)
{
    qCDebug(lcDisplayMode) << "Voice recognition result:" << text;

    // 这里可以处理语音命令
    // 实际实现中应该通过VoiceManager来处理
}

QWidget* DisplayModeManager::createWindow(DisplayMode mode)
{
    switch (mode) {
    case DisplayMode::Mini:
        if (!m_miniWindow) {
            m_miniWindow = std::make_unique<MiniWindow>();
        }
        return m_miniWindow.get();

    case DisplayMode::Taskbar:
        if (!m_taskbarWindow) {
            m_taskbarWindow = std::make_unique<TaskbarWindow>();
        }
        return m_taskbarWindow.get();

    case DisplayMode::Full:
        if (!m_mainWindow) {
            m_mainWindow = std::make_unique<MainWindow>();
        }
        return m_mainWindow.get();

    case DisplayMode::Voice:
        // 语音模式可以复用迷你窗口
        if (!m_miniWindow) {
            m_miniWindow = std::make_unique<MiniWindow>();
        }
        return m_miniWindow.get();

    default:
        return m_mainWindow.get();
    }
}

void DisplayModeManager::initializeVoiceManager()
{
    if (!m_voiceManager) {
        m_voiceManager = std::make_unique<VoiceManager>(this);

        if (m_voiceManager->initialize()) {
            connect(m_voiceManager.get(), &VoiceManager::recognitionResult,
                    this, &DisplayModeManager::onVoiceRecognitionResult);

            qCDebug(lcDisplayMode) << "Voice manager initialized";
        } else {
            qCWarning(lcDisplayMode) << "Failed to initialize voice manager";
            m_voiceManager.reset();
        }
    }
}

void DisplayModeManager::loadConfiguration()
{
    if (!m_configManager) {
        return;
    }

    m_animationsEnabled = m_configManager->getBool("display.animations_enabled", true);
    m_soundEnabled = m_configManager->getBool("display.sound_enabled", true);
    m_opacity = m_configManager->getDouble("display.opacity", DEFAULT_OPACITY);
    m_animationSpeed = m_configManager->getDouble("display.animation_speed", 1.0);
    m_voiceEnabled = m_configManager->getBool("voice.enabled", false);

    // 加载默认模式
    QString defaultMode = m_configManager->getString("display.default_mode", "full");
    if (defaultMode == "mini") {
        m_currentMode = DisplayMode::Mini;
    } else if (defaultMode == "taskbar") {
        m_currentMode = DisplayMode::Taskbar;
    } else if (defaultMode == "voice") {
        m_currentMode = DisplayMode::Voice;
    } else {
        m_currentMode = DisplayMode::Full;
    }

    qCDebug(lcDisplayMode) << "Configuration loaded";
}

QString DisplayModeManager::getModeDisplayName(DisplayMode mode) const
{
    switch (mode) {
    case DisplayMode::Mini:
        return "迷你模式";
    case DisplayMode::Taskbar:
        return "任务栏模式";
    case DisplayMode::Full:
        return "完整模式";
    case DisplayMode::Voice:
        return "语音模式";
    case DisplayMode::Hidden:
        return "隐藏模式";
    default:
        return "未知模式";
    }
}
