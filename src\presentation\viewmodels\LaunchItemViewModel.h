#pragma once

#include "BaseViewModel.h"
#include "business/entities/LaunchItem.h"
#include <QTimer>
#include <memory>

// 前置声明
class LaunchManager;
class SearchEngine;
class ConfigManager;

/**
 * @brief 启动项视图模型
 * 
 * 为UI提供启动项数据的MVVM绑定，支持：
 * - 启动项列表显示
 * - 搜索和过滤
 * - 排序和分组
 * - 实时更新
 */
class LaunchItemViewModel : public BaseViewModel
{
    Q_OBJECT
    
    // 搜索和过滤属性
    Q_PROPERTY(QString searchText READ searchText WRITE setSearchText NOTIFY searchTextChanged)
    Q_PROPERTY(int selectedCategoryId READ selectedCategoryId WRITE setSelectedCategoryId NOTIFY selectedCategoryIdChanged)
    Q_PROPERTY(LaunchItemType filterType READ filterType WRITE setFilterType NOTIFY filterTypeChanged)
    Q_PROPERTY(bool showOnlyEnabled READ showOnlyEnabled WRITE setShowOnlyEnabled NOTIFY showOnlyEnabledChanged)
    Q_PROPERTY(bool showOnlyPinned READ showOnlyPinned WRITE setShowOnlyPinned NOTIFY showOnlyPinnedChanged)
    
    // 排序属性
    Q_PROPERTY(QString sortBy READ sortBy WRITE setSortBy NOTIFY sortByChanged)
    Q_PROPERTY(bool sortAscending READ sortAscending WRITE setSortAscending NOTIFY sortAscendingChanged)
    
    // 显示属性
    Q_PROPERTY(QString displayMode READ displayMode WRITE setDisplayMode NOTIFY displayModeChanged)
    Q_PROPERTY(int itemsPerPage READ itemsPerPage WRITE setItemsPerPage NOTIFY itemsPerPageChanged)
    Q_PROPERTY(int currentPage READ currentPage WRITE setCurrentPage NOTIFY currentPageChanged)
    Q_PROPERTY(int totalPages READ totalPages NOTIFY totalPagesChanged)

public:
    /**
     * @brief 数据角色枚举
     */
    enum Roles {
        IdRole = Qt::UserRole + 1,
        NameRole,
        PathRole,
        DescriptionRole,
        IconRole,
        TypeRole,
        TypeDisplayNameRole,
        CategoryIdRole,
        CategoryNameRole,
        UseCountRole,
        LastUsedRole,
        FirstUsedRole,
        RatingRole,
        IsEnabledRole,
        IsVisibleRole,
        IsPinnedRole,
        TagsRole,
        KeywordsRole,
        ShortcutRole,
        PriorityRole,
        FileSizeRole,
        FileModifiedRole,
        CreatedAtRole,
        UpdatedAtRole,
        RelevanceScoreRole,
        ItemObjectRole  // 完整的LaunchItem对象
    };
    Q_ENUM(Roles)

    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit LaunchItemViewModel(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~LaunchItemViewModel() override;

    // QAbstractListModel接口实现
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QHash<int, QByteArray> roleNames() const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;

    // 属性访问器
    QString searchText() const;
    void setSearchText(const QString &text);
    
    int selectedCategoryId() const;
    void setSelectedCategoryId(int categoryId);
    
    LaunchItemType filterType() const;
    void setFilterType(LaunchItemType type);
    
    bool showOnlyEnabled() const;
    void setShowOnlyEnabled(bool enabled);
    
    bool showOnlyPinned() const;
    void setShowOnlyPinned(bool pinned);
    
    QString sortBy() const;
    void setSortBy(const QString &field);
    
    bool sortAscending() const;
    void setSortAscending(bool ascending);
    
    QString displayMode() const;
    void setDisplayMode(const QString &mode);
    
    int itemsPerPage() const;
    void setItemsPerPage(int count);
    
    int currentPage() const;
    void setCurrentPage(int page);
    
    int totalPages() const;

    // 公共方法
    
    /**
     * @brief 获取指定索引的启动项
     * @param index 模型索引
     * @return 启动项对象
     */
    Q_INVOKABLE LaunchItem getItem(int index) const;
    
    /**
     * @brief 根据ID获取启动项
     * @param id 启动项ID
     * @return 启动项对象
     */
    Q_INVOKABLE LaunchItem getItemById(int id) const;
    
    /**
     * @brief 启动指定索引的项目
     * @param index 模型索引
     * @return 是否成功
     */
    Q_INVOKABLE bool launchItem(int index);
    
    /**
     * @brief 根据ID启动项目
     * @param id 启动项ID
     * @return 是否成功
     */
    Q_INVOKABLE bool launchItemById(int id);
    
    /**
     * @brief 切换项目的置顶状态
     * @param index 模型索引
     * @return 是否成功
     */
    Q_INVOKABLE bool togglePinned(int index);
    
    /**
     * @brief 切换项目的启用状态
     * @param index 模型索引
     * @return 是否成功
     */
    Q_INVOKABLE bool toggleEnabled(int index);
    
    /**
     * @brief 删除项目
     * @param index 模型索引
     * @return 是否成功
     */
    Q_INVOKABLE bool removeItem(int index);
    
    /**
     * @brief 获取选中的项目ID列表
     * @return ID列表
     */
    Q_INVOKABLE QList<int> getSelectedItemIds() const;
    
    /**
     * @brief 清除选择
     */
    Q_INVOKABLE void clearSelection();
    
    /**
     * @brief 全选
     */
    Q_INVOKABLE void selectAll();
    
    /**
     * @brief 反选
     */
    Q_INVOKABLE void invertSelection();
    
    /**
     * @brief 重置过滤器
     */
    Q_INVOKABLE void resetFilters();
    
    /**
     * @brief 重置排序
     */
    Q_INVOKABLE void resetSort();
    
    /**
     * @brief 跳转到下一页
     * @return 是否成功
     */
    Q_INVOKABLE bool nextPage();
    
    /**
     * @brief 跳转到上一页
     * @return 是否成功
     */
    Q_INVOKABLE bool previousPage();
    
    /**
     * @brief 跳转到首页
     */
    Q_INVOKABLE void firstPage();
    
    /**
     * @brief 跳转到末页
     */
    Q_INVOKABLE void lastPage();

    // BaseViewModel接口实现
    void refresh() override;
    void clear() override;
    void loadAsync() override;

signals:
    // 属性变更信号
    void searchTextChanged(const QString &text);
    void selectedCategoryIdChanged(int categoryId);
    void filterTypeChanged(LaunchItemType type);
    void showOnlyEnabledChanged(bool enabled);
    void showOnlyPinnedChanged(bool pinned);
    void sortByChanged(const QString &field);
    void sortAscendingChanged(bool ascending);
    void displayModeChanged(const QString &mode);
    void itemsPerPageChanged(int count);
    void currentPageChanged(int page);
    void totalPagesChanged(int pages);
    
    // 操作信号
    void itemLaunched(int id, bool success);
    void itemUpdated(int id);
    void itemRemoved(int id);
    void selectionChanged();
    void searchCompleted(int resultCount);

private slots:
    /**
     * @brief 搜索延迟定时器超时
     */
    void onSearchDelayTimeout();
    
    /**
     * @brief 启动项变更处理
     */
    void onItemsChanged();
    
    /**
     * @brief 启动项添加处理
     * @param item 启动项
     */
    void onItemAdded(const LaunchItem &item);
    
    /**
     * @brief 启动项更新处理
     * @param item 启动项
     */
    void onItemUpdated(const LaunchItem &item);
    
    /**
     * @brief 启动项删除处理
     * @param id 启动项ID
     */
    void onItemRemoved(int id);
    
    /**
     * @brief 搜索完成处理
     * @param results 搜索结果
     */
    void onSearchCompleted(const QList<LaunchItem> &results);
    
    /**
     * @brief 配置变更处理
     * @param key 配置键
     * @param value 配置值
     */
    void onConfigChanged(const QString &key, const QVariant &value);

private:
    /**
     * @brief 加载数据
     */
    void loadData();
    
    /**
     * @brief 应用过滤器
     */
    void applyFilters();
    
    /**
     * @brief 应用排序
     */
    void applySorting();
    
    /**
     * @brief 应用分页
     */
    void applyPaging();
    
    /**
     * @brief 执行搜索
     */
    void performSearch();
    
    /**
     * @brief 更新分页信息
     */
    void updatePagingInfo();
    
    /**
     * @brief 过滤项目
     * @param item 启动项
     * @return 是否通过过滤
     */
    bool filterItem(const LaunchItem &item) const;
    
    /**
     * @brief 比较项目（用于排序）
     * @param left 左项目
     * @param right 右项目
     * @return 比较结果
     */
    bool compareItems(const LaunchItem &left, const LaunchItem &right) const;
    
    /**
     * @brief 查找项目索引
     * @param id 启动项ID
     * @return 索引，-1表示未找到
     */
    int findItemIndex(int id) const;

private:
    // 服务引用
    std::shared_ptr<LaunchManager> m_launchManager;
    std::shared_ptr<SearchEngine> m_searchEngine;
    std::shared_ptr<ConfigManager> m_configManager;
    
    // 数据
    QList<LaunchItem> m_allItems;        // 所有项目
    QList<LaunchItem> m_filteredItems;   // 过滤后的项目
    QList<LaunchItem> m_displayItems;    // 显示的项目（分页后）
    QSet<int> m_selectedItems;           // 选中的项目ID
    
    // 搜索和过滤
    QString m_searchText;
    int m_selectedCategoryId = -1;
    LaunchItemType m_filterType = static_cast<LaunchItemType>(-1); // 无过滤
    bool m_showOnlyEnabled = false;
    bool m_showOnlyPinned = false;
    
    // 排序
    QString m_sortBy = "name";
    bool m_sortAscending = true;
    
    // 显示
    QString m_displayMode = "list";      // list, grid, compact
    int m_itemsPerPage = 50;
    int m_currentPage = 0;
    int m_totalPages = 0;
    
    // 定时器
    QTimer *m_searchDelayTimer;
    
    // 常量
    static const int SEARCH_DELAY = 300;  // 搜索延迟（毫秒）
};
