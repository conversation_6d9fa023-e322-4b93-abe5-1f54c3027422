syntax = "v1"

// ============================================================================
// 认证相关类型定义
// ============================================================================

// 用户注册请求
type RegisterReq {
    Email       string     `json:"email" validate:"required,email,max=100"`        // 邮箱地址
    Password    string     `json:"password" validate:"required,min=8,max=50"`      // 密码(客户端已加密)
    Username    string     `json:"username" validate:"required,min=2,max=50"`      // 用户名
    DeviceInfo  DeviceInfo `json:"device_info" validate:"required"`               // 设备信息
    InviteCode  string     `json:"invite_code,optional"`                           // 邀请码
    AcceptTerms bool       `json:"accept_terms" validate:"required"`               // 是否接受服务条款
}

// 用户登录请求
type LoginReq {
    Email      string     `json:"email" validate:"required,email"`                // 邮箱地址
    Password   string     `json:"password" validate:"required"`                   // 密码(客户端已加密)
    DeviceInfo DeviceInfo `json:"device_info" validate:"required"`               // 设备信息
    RememberMe bool       `json:"remember_me,optional"`                           // 是否记住登录状态
    TwoFactorCode string  `json:"two_factor_code,optional"`                       // 双因素认证码
}

// 刷新Token请求
type RefreshTokenReq {
    RefreshToken string     `json:"refresh_token" validate:"required"`             // 刷新令牌
    DeviceId     string     `json:"device_id" validate:"required"`                 // 设备ID
}

// 登出请求
type LogoutReq {
    DeviceId    string `json:"device_id,optional"`                                // 设备ID
    AllDevices  bool   `json:"all_devices,optional"`                              // 是否登出所有设备
}

// 认证响应
type AuthResp {
    BaseResp
    Data struct {
        UserId       int64  `json:"user_id"`        // 用户ID
        Username     string `json:"username"`       // 用户名
        Email        string `json:"email"`          // 邮箱
        Avatar       string `json:"avatar"`         // 头像URL
        AccessToken  string `json:"access_token"`   // 访问令牌
        RefreshToken string `json:"refresh_token"`  // 刷新令牌
        TokenType    string `json:"token_type"`     // 令牌类型(Bearer)
        ExpiresIn    int64  `json:"expires_in"`     // 过期时间(秒)
        Scope        string `json:"scope"`          // 权限范围
        UserInfo     struct {
            Plan         string   `json:"plan"`          // 订阅计划: free/premium/pro
            Features     []string `json:"features"`      // 可用功能列表
            Permissions  []string `json:"permissions"`   // 权限列表
            ExpiresAt    string   `json:"expires_at,optional"` // 订阅过期时间
            DeviceLimit  int      `json:"device_limit"`  // 设备数量限制
            StorageLimit int64    `json:"storage_limit"` // 存储空间限制(字节)
        } `json:"user_info"`
        DeviceInfo struct {
            DeviceId     string `json:"device_id"`      // 设备ID
            DeviceName   string `json:"device_name"`    // 设备名称
            IsNewDevice  bool   `json:"is_new_device"`  // 是否新设备
            LastLoginAt  string `json:"last_login_at"`  // 最后登录时间
            LoginCount   int64  `json:"login_count"`    // 登录次数
        } `json:"device_info"`
    } `json:"data"`
}

// 用户信息
type UserInfo {
    UserId      int64  `json:"user_id"`       // 用户ID
    Username    string `json:"username"`      // 用户名
    Email       string `json:"email"`         // 邮箱
    Avatar      string `json:"avatar"`        // 头像URL
    Status      string `json:"status"`        // 账户状态: active/suspended/deleted
    Plan        string `json:"plan"`          // 订阅计划
    CreatedAt   string `json:"created_at"`    // 创建时间
    LastLoginAt string `json:"last_login_at"` // 最后登录时间
    Settings    struct {
        Theme         string `json:"theme"`          // 主题偏好: light/dark/auto
        Language      string `json:"language"`       // 语言偏好
        Notifications bool   `json:"notifications"`  // 通知开关
        SyncEnabled   bool   `json:"sync_enabled"`   // 同步开关
        PrivacyLevel  string `json:"privacy_level"`  // 隐私级别: public/private
        Timezone      string `json:"timezone"`       // 时区
    } `json:"settings"`
    Subscription struct {
        Plan        string   `json:"plan"`         // 订阅计划
        Status      string   `json:"status"`       // 订阅状态: active/expired/cancelled
        ExpiresAt   string   `json:"expires_at"`   // 过期时间
        Features    []string `json:"features"`     // 可用功能
        Limits      struct {
            Devices     int   `json:"devices"`      // 设备数量限制
            Storage     int64 `json:"storage"`      // 存储空间限制(字节)
            SyncItems   int   `json:"sync_items"`   // 同步项目数量限制
            ApiCalls    int   `json:"api_calls"`    // API调用次数限制
        } `json:"limits"`
        Usage struct {
            Devices   int   `json:"devices"`    // 已使用设备数
            Storage   int64 `json:"storage"`    // 已使用存储空间
            SyncItems int   `json:"sync_items"` // 已同步项目数
            ApiCalls  int   `json:"api_calls"`  // 已使用API调用次数
        } `json:"usage"`
    } `json:"subscription"`
}

// 设备管理
type Device {
    DeviceId     string `json:"device_id"`      // 设备ID
    DeviceName   string `json:"device_name"`    // 设备名称
    Platform     string `json:"platform"`       // 操作系统平台
    ClientVersion string `json:"client_version"` // 客户端版本
    IsActive     bool   `json:"is_active"`      // 是否活跃
    LastActive   string `json:"last_active"`    // 最后活跃时间
    IpAddress    string `json:"ip_address"`     // IP地址
    Location     *LocationInfo `json:"location,optional"` // 地理位置
    IsCurrentDevice bool `json:"is_current_device"` // 是否当前设备
    LoginCount   int64  `json:"login_count"`    // 登录次数
    CreatedAt    string `json:"created_at"`     // 首次登录时间
}

// 获取用户信息请求
type GetUserInfoReq {
    IncludeSettings     bool `form:"include_settings,optional"`     // 是否包含设置信息
    IncludeSubscription bool `form:"include_subscription,optional"` // 是否包含订阅信息
    IncludeDevices      bool `form:"include_devices,optional"`      // 是否包含设备信息
}

// 获取用户信息响应
type GetUserInfoResp {
    BaseResp
    Data    UserInfo `json:"data"`              // 用户信息
    Devices []Device `json:"devices,optional"`  // 设备列表
}

// 更新用户信息请求
type UpdateUserInfoReq {
    Username string `json:"username,optional" validate:"min=2,max=50"`     // 用户名
    Avatar   string `json:"avatar,optional" validate:"url"`                // 头像URL
    Settings struct {
        Theme         string `json:"theme,optional" validate:"oneof=light dark auto"`        // 主题
        Language      string `json:"language,optional" validate:"len=5"`                     // 语言
        Notifications bool   `json:"notifications,optional"`                                 // 通知开关
        SyncEnabled   bool   `json:"sync_enabled,optional"`                                  // 同步开关
        PrivacyLevel  string `json:"privacy_level,optional" validate:"oneof=public private"` // 隐私级别
        Timezone      string `json:"timezone,optional"`                                      // 时区
    } `json:"settings,optional"` // 用户设置
}

// 修改密码请求
type ChangePasswordReq {
    OldPassword string `json:"old_password" validate:"required"`           // 旧密码
    NewPassword string `json:"new_password" validate:"required,min=8,max=50"` // 新密码
    DeviceId    string `json:"device_id" validate:"required"`              // 设备ID
}

// 重置密码请求
type ResetPasswordReq {
    Email string `json:"email" validate:"required,email"`                 // 邮箱地址
}

// 确认重置密码请求
type ConfirmResetPasswordReq {
    Token       string `json:"token" validate:"required"`                  // 重置令牌
    NewPassword string `json:"new_password" validate:"required,min=8,max=50"` // 新密码
    DeviceInfo  DeviceInfo `json:"device_info" validate:"required"`       // 设备信息
}

// 获取设备列表请求
type GetDevicesReq {
    IncludeInactive bool `form:"include_inactive,optional"`                // 是否包含非活跃设备
    SortBy          string `form:"sort_by,optional"`                       // 排序字段: last_active/created_at/login_count
    SortOrder       string `form:"sort_order,optional"`                    // 排序方向: asc/desc
}

// 获取设备列表响应
type GetDevicesResp {
    BaseResp
    Data []Device `json:"data"`                                           // 设备列表
    Stats struct {
        Total    int `json:"total"`     // 总设备数
        Active   int `json:"active"`    // 活跃设备数
        Inactive int `json:"inactive"`  // 非活跃设备数
        Limit    int `json:"limit"`     // 设备数量限制
    } `json:"stats"`
}

// 撤销设备授权请求
type RevokeDeviceReq {
    DeviceId string `path:"deviceId" validate:"required"`                  // 设备ID
}

// 更新设备信息请求
type UpdateDeviceReq {
    DeviceId   string `path:"deviceId" validate:"required"`               // 设备ID
    DeviceName string `json:"device_name,optional" validate:"min=1,max=100"` // 设备名称
}

// 双因素认证设置
type TwoFactorAuth {
    Enabled    bool   `json:"enabled"`     // 是否启用
    Method     string `json:"method"`      // 认证方式: totp/sms/email
    BackupCodes []string `json:"backup_codes,optional"` // 备用码
    SetupAt    string `json:"setup_at"`    // 设置时间
    LastUsed   string `json:"last_used,optional"` // 最后使用时间
}

// 启用双因素认证请求
type EnableTwoFactorReq {
    Method string `json:"method" validate:"required,oneof=totp sms email"`  // 认证方式
    Phone  string `json:"phone,optional" validate:"phone"`                  // 手机号(SMS方式)
    Code   string `json:"code" validate:"required,len=6"`                   // 验证码
}

// 启用双因素认证响应
type EnableTwoFactorResp {
    BaseResp
    Data struct {
        BackupCodes []string `json:"backup_codes"` // 备用码
        QRCode      string   `json:"qr_code,optional"` // 二维码(TOTP方式)
        Secret      string   `json:"secret,optional"`  // 密钥(TOTP方式)
    } `json:"data"`
}

// 禁用双因素认证请求
type DisableTwoFactorReq {
    Password string `json:"password" validate:"required"`                   // 当前密码
    Code     string `json:"code,optional"`                                  // 验证码或备用码
}

// 验证双因素认证请求
type VerifyTwoFactorReq {
    Code string `json:"code" validate:"required"`                          // 验证码或备用码
}

// 会话管理
type Session {
    SessionId   string `json:"session_id"`   // 会话ID
    DeviceId    string `json:"device_id"`    // 设备ID
    DeviceName  string `json:"device_name"`  // 设备名称
    IpAddress   string `json:"ip_address"`   // IP地址
    Location    *LocationInfo `json:"location,optional"` // 地理位置
    UserAgent   string `json:"user_agent"`   // 用户代理
    CreatedAt   string `json:"created_at"`   // 创建时间
    LastActive  string `json:"last_active"`  // 最后活跃时间
    ExpiresAt   string `json:"expires_at"`   // 过期时间
    IsActive    bool   `json:"is_active"`    // 是否活跃
    IsCurrent   bool   `json:"is_current"`   // 是否当前会话
}

// 获取会话列表请求
type GetSessionsReq {
    IncludeExpired bool   `form:"include_expired,optional"`                 // 是否包含过期会话
    DeviceId       string `form:"device_id,optional"`                       // 设备ID过滤
    Limit          int    `form:"limit,optional" validate:"min=1,max=100"`  // 限制数量
}

// 获取会话列表响应
type GetSessionsResp {
    BaseResp
    Data []Session `json:"data"`                                           // 会话列表
    Stats struct {
        Total   int `json:"total"`    // 总会话数
        Active  int `json:"active"`   // 活跃会话数
        Expired int `json:"expired"`  // 过期会话数
    } `json:"stats"`
}

// 撤销会话请求
type RevokeSessionReq {
    SessionId string `path:"sessionId" validate:"required"`                 // 会话ID
}

// 撤销所有会话请求
type RevokeAllSessionsReq {
    ExceptCurrent bool `json:"except_current,optional"`                     // 是否保留当前会话
    Password      string `json:"password" validate:"required"`              // 当前密码确认
}

// 账户删除请求
type DeleteAccountReq {
    Password      string `json:"password" validate:"required"`              // 当前密码
    Reason        string `json:"reason,optional" validate:"max=500"`        // 删除原因
    DeleteData    bool   `json:"delete_data"`                               // 是否删除所有数据
    Confirmation  string `json:"confirmation" validate:"required,eq=DELETE"` // 确认字符串
}

// JWT Token信息
type TokenInfo {
    TokenType   string   `json:"token_type"`    // 令牌类型
    AccessToken string   `json:"access_token"`  // 访问令牌
    ExpiresIn   int64    `json:"expires_in"`    // 过期时间(秒)
    Scope       string   `json:"scope"`         // 权限范围
    IssuedAt    int64    `json:"issued_at"`     // 签发时间
    Claims      struct {
        UserId      int64    `json:"user_id"`      // 用户ID
        DeviceId    string   `json:"device_id"`    // 设备ID
        Permissions []string `json:"permissions"`  // 权限列表
        Plan        string   `json:"plan"`         // 订阅计划
        Features    []string `json:"features"`     // 可用功能
    } `json:"claims"` // Token声明
}

// 验证Token请求
type VerifyTokenReq {
    Token string `json:"token" validate:"required"`                        // 要验证的Token
}

// 验证Token响应
type VerifyTokenResp {
    BaseResp
    Data struct {
        Valid     bool      `json:"valid"`      // 是否有效
        TokenInfo TokenInfo `json:"token_info"` // Token信息
        ExpiresIn int64     `json:"expires_in"` // 剩余有效时间(秒)
    } `json:"data"`
}
