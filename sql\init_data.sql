-- ============================================================================
-- 智能启动助手 - 初始化数据SQL
-- 版本: 1.0.0
-- 创建时间: 2024-12-17
-- 说明: 插入默认配置数据和系统预设数据
-- ============================================================================

-- ============================================================================
-- 1. 默认分类数据
-- ============================================================================
INSERT OR IGNORE INTO categories (id, name, display_name, icon_name, color, description, sort_order, is_system, is_visible) VALUES
(1, 'default', '默认分类', 'folder', '#6C757D', '未分类的应用程序', 999, 1, 1),
(2, 'development', '开发工具', 'code', '#007ACC', '编程开发相关工具', 1, 1, 1),
(3, 'productivity', '办公效率', 'briefcase', '#28A745', '办公和效率工具', 2, 1, 1),
(4, 'media', '多媒体', 'play-circle', '#DC3545', '音视频处理工具', 3, 1, 1),
(5, 'graphics', '图形设计', 'image', '#FD7E14', '图像和设计工具', 4, 1, 1),
(6, 'system', '系统工具', 'settings', '#6F42C1', '系统管理和维护工具', 5, 1, 1),
(7, 'network', '网络工具', 'globe', '#20C997', '网络和通信工具', 6, 1, 1),
(8, 'games', '游戏娱乐', 'gamepad', '#E83E8C', '游戏和娱乐应用', 7, 1, 1),
(9, 'education', '教育学习', 'book', '#17A2B8', '学习和教育软件', 8, 1, 1),
(10, 'utilities', '实用工具', 'tool', '#FFC107', '各种实用小工具', 9, 1, 1);

-- ============================================================================
-- 2. 默认应用配置
-- ============================================================================
INSERT OR IGNORE INTO app_settings (setting_key, setting_value, value_type, category, description, is_user_configurable, requires_restart) VALUES
-- 界面设置
('ui.theme', 'auto', 'string', 'ui', '界面主题: light/dark/auto', 1, 0),
('ui.language', 'zh-CN', 'string', 'ui', '界面语言', 1, 1),
('ui.window_width', '800', 'int', 'ui', '主窗口宽度', 1, 0),
('ui.window_height', '600', 'int', 'ui', '主窗口高度', 1, 0),
('ui.window_opacity', '0.95', 'float', 'ui', '窗口透明度(0.1-1.0)', 1, 0),
('ui.show_categories', 'true', 'bool', 'ui', '显示分类面板', 1, 0),
('ui.show_search_history', 'true', 'bool', 'ui', '显示搜索历史', 1, 0),
('ui.animation_enabled', 'true', 'bool', 'ui', '启用界面动画', 1, 0),
('ui.font_family', 'Microsoft YaHei', 'string', 'ui', '界面字体', 1, 1),
('ui.font_size', '12', 'int', 'ui', '字体大小', 1, 0),

-- 行为设置
('behavior.auto_start', 'false', 'bool', 'behavior', '开机自动启动', 1, 0),
('behavior.minimize_to_tray', 'true', 'bool', 'behavior', '最小化到系统托盘', 1, 0),
('behavior.close_to_tray', 'true', 'bool', 'behavior', '关闭时最小化到托盘', 1, 0),
('behavior.show_on_startup', 'false', 'bool', 'behavior', '启动时显示主窗口', 1, 0),
('behavior.search_delay', '300', 'int', 'behavior', '搜索延迟时间(毫秒)', 1, 0),
('behavior.max_search_results', '20', 'int', 'behavior', '最大搜索结果数', 1, 0),
('behavior.max_recent_items', '10', 'int', 'behavior', '最大最近使用项目数', 1, 0),
('behavior.double_click_action', 'launch', 'string', 'behavior', '双击动作: launch/edit', 1, 0),

-- 推荐设置
('recommendation.enabled', 'true', 'bool', 'recommendation', '启用智能推荐', 1, 0),
('recommendation.learn_from_usage', 'true', 'bool', 'recommendation', '从使用行为中学习', 1, 0),
('recommendation.context_aware', 'true', 'bool', 'recommendation', '启用上下文感知推荐', 1, 0),
('recommendation.clipboard_analysis', 'true', 'bool', 'recommendation', '分析剪贴板内容', 1, 0),
('recommendation.time_based', 'true', 'bool', 'recommendation', '基于时间的推荐', 1, 0),
('recommendation.max_suggestions', '5', 'int', 'recommendation', '最大推荐数量', 1, 0),
('recommendation.min_confidence', '0.3', 'float', 'recommendation', '最小置信度阈值', 1, 0),

-- 性能设置
('performance.enable_cache', 'true', 'bool', 'performance', '启用缓存', 1, 1),
('performance.cache_size_mb', '50', 'int', 'performance', '缓存大小(MB)', 1, 1),
('performance.scan_interval', '300', 'int', 'performance', '文件扫描间隔(秒)', 1, 0),
('performance.max_threads', '4', 'int', 'performance', '最大线程数', 1, 1),
('performance.enable_indexing', 'true', 'bool', 'performance', '启用文件索引', 1, 1),

-- 安全设置
('security.require_confirmation', 'false', 'bool', 'security', '删除时需要确认', 1, 0),
('security.log_user_actions', 'true', 'bool', 'security', '记录用户操作', 1, 0),
('security.encrypt_sensitive_data', 'false', 'bool', 'security', '加密敏感数据', 1, 1),
('security.auto_backup', 'true', 'bool', 'security', '自动备份配置', 1, 0),
('security.backup_interval_days', '7', 'int', 'security', '备份间隔(天)', 1, 0),

-- 网络设置
('network.enable_sync', 'false', 'bool', 'network', '启用云同步', 1, 0),
('network.server_url', '', 'string', 'network', '服务器地址', 1, 0),
('network.sync_interval', '300', 'int', 'network', '同步间隔(秒)', 1, 0),
('network.enable_updates', 'true', 'bool', 'network', '启用自动更新检查', 1, 0),
('network.update_channel', 'stable', 'string', 'network', '更新渠道: stable/beta', 1, 0),

-- 高级设置
('advanced.debug_mode', 'false', 'bool', 'advanced', '调试模式', 1, 1),
('advanced.log_level', 'INFO', 'string', 'advanced', '日志级别: DEBUG/INFO/WARN/ERROR', 1, 1),
('advanced.max_log_files', '10', 'int', 'advanced', '最大日志文件数', 1, 0),
('advanced.log_file_size_mb', '10', 'int', 'advanced', '单个日志文件大小(MB)', 1, 0),
('advanced.enable_crash_reporting', 'true', 'bool', 'advanced', '启用崩溃报告', 1, 0);

-- ============================================================================
-- 3. 默认快捷键配置
-- ============================================================================
INSERT OR IGNORE INTO hotkey_settings (action_name, display_name, key_sequence, description, is_global, is_enabled) VALUES
('show_main_window', '显示主窗口', 'Ctrl+Space', '显示或隐藏主窗口', 1, 1),
('quick_search', '快速搜索', 'Ctrl+Shift+F', '打开快速搜索对话框', 1, 1),
('toggle_theme', '切换主题', 'Ctrl+Shift+T', '在浅色和深色主题间切换', 0, 1),
('show_settings', '显示设置', 'Ctrl+,', '打开设置对话框', 0, 1),
('refresh_items', '刷新列表', 'F5', '刷新启动项列表', 0, 1),
('add_new_item', '添加新项目', 'Ctrl+N', '添加新的启动项', 0, 1),
('delete_selected', '删除选中项', 'Delete', '删除选中的启动项', 0, 1),
('edit_selected', '编辑选中项', 'F2', '编辑选中的启动项', 0, 1),
('show_item_info', '显示项目信息', 'Ctrl+I', '显示启动项详细信息', 0, 1),
('export_config', '导出配置', 'Ctrl+E', '导出配置到文件', 0, 0),
('import_config', '导入配置', 'Ctrl+Shift+I', '从文件导入配置', 0, 0),
('show_statistics', '显示统计', 'Ctrl+Shift+S', '显示使用统计信息', 0, 1),
('minimize_to_tray', '最小化到托盘', 'Ctrl+M', '最小化到系统托盘', 0, 1),
('exit_application', '退出程序', 'Ctrl+Q', '完全退出应用程序', 0, 1);

-- ============================================================================
-- 4. 默认推荐规则
-- ============================================================================
INSERT OR IGNORE INTO recommendation_rules (name, description, rule_type, condition_data, action_data, priority, is_enabled) VALUES
-- 文件类型推荐规则
('JSON文件推荐', '当检测到JSON内容时推荐相关编辑器', 'file_type', 
 '{"file_extensions": [".json", ".jsonc"], "content_patterns": ["^\\s*[{\\[]"]}',
 '{"recommended_apps": ["vscode", "notepad++", "sublime"], "confidence": 0.9}', 
 10, 1),

('代码文件推荐', '当检测到代码文件时推荐开发工具', 'file_type',
 '{"file_extensions": [".cpp", ".h", ".py", ".js", ".ts", ".java", ".cs"]}',
 '{"recommended_apps": ["vscode", "visual_studio", "clion"], "confidence": 0.95}',
 10, 1),

('图片文件推荐', '当检测到图片文件时推荐图像处理工具', 'file_type',
 '{"file_extensions": [".jpg", ".png", ".gif", ".bmp", ".svg", ".webp"]}',
 '{"recommended_apps": ["photoshop", "gimp", "paint"], "confidence": 0.8}',
 8, 1),

('视频文件推荐', '当检测到视频文件时推荐播放器', 'file_type',
 '{"file_extensions": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv"]}',
 '{"recommended_apps": ["vlc", "potplayer", "kmplayer"], "confidence": 0.9}',
 8, 1),

('文档文件推荐', '当检测到文档文件时推荐办公软件', 'file_type',
 '{"file_extensions": [".doc", ".docx", ".pdf", ".txt", ".rtf"]}',
 '{"recommended_apps": ["word", "wps", "notepad"], "confidence": 0.85}',
 7, 1),

-- 时间段推荐规则
('工作时间推荐', '工作时间推荐开发和办公工具', 'time',
 '{"time_ranges": [{"start": "09:00", "end": "18:00", "days": [1,2,3,4,5]}]}',
 '{"boost_categories": ["development", "productivity"], "boost_factor": 1.5}',
 5, 1),

('休息时间推荐', '休息时间推荐娱乐和媒体工具', 'time',
 '{"time_ranges": [{"start": "19:00", "end": "23:00"}, {"start": "00:00", "end": "08:00"}]}',
 '{"boost_categories": ["media", "games"], "boost_factor": 1.3}',
 5, 1),

-- 剪贴板内容推荐规则
('URL链接推荐', '检测到URL时推荐浏览器', 'clipboard',
 '{"patterns": ["^https?://", "^www\\."]}',
 '{"recommended_apps": ["chrome", "firefox", "edge"], "confidence": 0.95}',
 9, 1),

('邮箱地址推荐', '检测到邮箱地址时推荐邮件客户端', 'clipboard',
 '{"patterns": ["[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"]}',
 '{"recommended_apps": ["outlook", "thunderbird"], "confidence": 0.8}',
 6, 1),

-- 上下文推荐规则
('浏览器活跃推荐', '当浏览器处于活跃状态时推荐开发工具', 'context',
 '{"active_windows": ["chrome.exe", "firefox.exe", "edge.exe"]}',
 '{"boost_categories": ["development", "network"], "boost_factor": 1.2}',
 4, 1);

-- ============================================================================
-- 5. 常见文件类型关联(示例数据)
-- ============================================================================
-- 注意: 这些是示例数据，实际使用时需要根据用户系统中安装的软件来动态生成
-- 这里只插入一些通用的文件类型，不关联具体的launch_item_id

-- ============================================================================
-- 6. 系统初始化日志
-- ============================================================================
INSERT INTO system_logs (log_level, category, message, details) VALUES
('INFO', 'system', '数据库初始化完成', '成功创建所有表结构和初始化数据'),
('INFO', 'system', '默认配置加载完成', '加载了' || (SELECT COUNT(*) FROM app_settings) || '项配置'),
('INFO', 'system', '默认分类创建完成', '创建了' || (SELECT COUNT(*) FROM categories) || '个分类'),
('INFO', 'system', '快捷键配置完成', '配置了' || (SELECT COUNT(*) FROM hotkey_settings) || '个快捷键'),
('INFO', 'system', '推荐规则加载完成', '加载了' || (SELECT COUNT(*) FROM recommendation_rules) || '条推荐规则');

-- ============================================================================
-- 7. 创建默认视图数据(用于测试)
-- ============================================================================

-- 插入一些示例启动项(仅用于开发测试)
-- 实际部署时应该删除或注释掉这部分
/*
INSERT OR IGNORE INTO launch_items (name, path, category_id, description, tags) VALUES
('记事本', 'C:\\Windows\\System32\\notepad.exe', 10, 'Windows系统自带的文本编辑器', '["文本编辑", "系统工具"]'),
('计算器', 'C:\\Windows\\System32\\calc.exe', 10, 'Windows系统自带的计算器', '["计算器", "系统工具"]'),
('画图', 'C:\\Windows\\System32\\mspaint.exe', 5, 'Windows系统自带的画图工具', '["图像编辑", "系统工具"]'),
('命令提示符', 'C:\\Windows\\System32\\cmd.exe', 6, 'Windows命令行界面', '["命令行", "系统工具", "开发"]'),
('任务管理器', 'C:\\Windows\\System32\\taskmgr.exe', 6, 'Windows任务管理器', '["系统监控", "系统工具"]');
*/

-- ============================================================================
-- 8. 数据完整性检查
-- ============================================================================

-- 检查必要的数据是否存在
SELECT 
    'categories' as table_name, 
    COUNT(*) as record_count,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'ERROR' END as status
FROM categories
WHERE is_system = 1

UNION ALL

SELECT 
    'app_settings' as table_name, 
    COUNT(*) as record_count,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'ERROR' END as status
FROM app_settings

UNION ALL

SELECT 
    'hotkey_settings' as table_name, 
    COUNT(*) as record_count,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'ERROR' END as status
FROM hotkey_settings

UNION ALL

SELECT 
    'recommendation_rules' as table_name, 
    COUNT(*) as record_count,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'ERROR' END as status
FROM recommendation_rules;

-- ============================================================================
-- 9. 版本信息记录
-- ============================================================================
INSERT OR IGNORE INTO app_settings (setting_key, setting_value, value_type, category, description, is_user_configurable) VALUES
('system.database_version', '1.0.0', 'string', 'system', '数据库版本号', 0),
('system.init_date', datetime('now'), 'string', 'system', '数据库初始化时间', 0),
('system.last_migration', '1.0.0', 'string', 'system', '最后执行的迁移版本', 0);

-- 记录初始化完成日志
INSERT INTO system_logs (log_level, category, message, details) VALUES
('INFO', 'system', '数据库初始化脚本执行完成', 
 'Database version: 1.0.0, Initialization completed at: ' || datetime('now'));
