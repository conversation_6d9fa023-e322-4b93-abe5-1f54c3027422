#include "ServiceContainer.h"
#include <QLoggingCategory>
#include <QDebug>

Q_LOGGING_CATEGORY(lcServiceContainer, "servicecontainer")

/**
 * @brief 可初始化接口
 * 
 * 服务如果实现了此接口，在服务容器初始化时会自动调用initialize方法
 */
class IInitializable {
public:
    virtual ~IInitializable() = default;
    virtual bool initialize() = 0;
};

ServiceContainer::ServiceContainer(QObject *parent)
    : QObject(parent)
    , m_initialized(false)
{
    qCDebug(lcServiceContainer) << "ServiceContainer created";
}

ServiceContainer::~ServiceContainer()
{
    qCDebug(lcServiceContainer) << "ServiceContainer destructor called";
    clear();
}

void ServiceContainer::initializeServices()
{
    if (m_initialized) {
        qCWarning(lcServiceContainer) << "Services already initialized";
        return;
    }
    
    qCInfo(lcServiceContainer) << "Initializing services...";
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    int initializedCount = 0;
    int failedCount = 0;
    
    // 遍历所有注册的服务
    for (auto &pair : m_services) {
        auto &registration = pair.second;
        
        try {
            initializeService(registration);
            
            if (registration.isInitialized) {
                initializedCount++;
                qCDebug(lcServiceContainer) << "Service initialized:" << registration.typeName;
            } else {
                failedCount++;
                qCWarning(lcServiceContainer) << "Service initialization failed:" << registration.typeName;
            }
            
        } catch (const std::exception& e) {
            failedCount++;
            qCCritical(lcServiceContainer) << "Exception during service initialization:" 
                                          << registration.typeName << e.what();
        }
    }
    
    m_initialized = true;
    
    qCInfo(lcServiceContainer) << "Services initialization completed."
                              << "Initialized:" << initializedCount
                              << "Failed:" << failedCount
                              << "Total:" << m_services.size();
    
    emit servicesInitialized();
}

void ServiceContainer::clear()
{
    qCDebug(lcServiceContainer) << "Clearing all services";
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 清理所有服务实例
    for (auto &pair : m_services) {
        auto &registration = pair.second;
        
        // 如果服务实现了清理接口，调用清理方法
        if (registration.instance) {
            // 这里可以添加清理逻辑
            qCDebug(lcServiceContainer) << "Cleaning up service:" << registration.typeName;
        }
    }
    
    m_services.clear();
    m_initialized = false;
    
    qCDebug(lcServiceContainer) << "All services cleared";
}

size_t ServiceContainer::getServiceCount() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_services.size();
}

QStringList ServiceContainer::getRegisteredServiceNames() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    QStringList names;
    names.reserve(static_cast<int>(m_services.size()));
    
    for (const auto &pair : m_services) {
        names.append(pair.second.typeName);
    }
    
    return names;
}

void ServiceContainer::initializeService(ServiceRegistration &registration)
{
    if (registration.isInitialized) {
        return;
    }
    
    try {
        // 获取或创建服务实例
        std::shared_ptr<void> instance;
        
        if (registration.isSingleton && registration.instance) {
            // 单例模式，使用已有实例
            instance = registration.instance;
        } else if (registration.factory) {
            // 使用工厂函数创建实例
            instance = registration.factory();
            
            // 如果是单例，缓存实例
            if (registration.isSingleton) {
                registration.instance = instance;
            }
        } else {
            qCWarning(lcServiceContainer) << "No factory or instance available for service:" 
                                         << registration.typeName;
            return;
        }
        
        if (!instance) {
            qCWarning(lcServiceContainer) << "Failed to create instance for service:" 
                                         << registration.typeName;
            return;
        }
        
        // 尝试调用初始化方法
        auto initializable = std::dynamic_pointer_cast<IInitializable>(instance);
        if (initializable) {
            qCDebug(lcServiceContainer) << "Calling initialize() for service:" 
                                       << registration.typeName;
            
            if (initializable->initialize()) {
                registration.isInitialized = true;
                qCDebug(lcServiceContainer) << "Service initialized successfully:" 
                                           << registration.typeName;
            } else {
                qCWarning(lcServiceContainer) << "Service initialize() returned false:" 
                                             << registration.typeName;
            }
        } else {
            // 服务没有实现IInitializable接口，标记为已初始化
            registration.isInitialized = true;
            qCDebug(lcServiceContainer) << "Service marked as initialized (no IInitializable):" 
                                       << registration.typeName;
        }
        
    } catch (const std::exception& e) {
        qCCritical(lcServiceContainer) << "Exception during service initialization:" 
                                      << registration.typeName << e.what();
        throw;
    }
}

// 用于调试的辅助函数
void ServiceContainer::dumpServices() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    qCDebug(lcServiceContainer) << "=== Service Container Dump ===";
    qCDebug(lcServiceContainer) << "Total services:" << m_services.size();
    qCDebug(lcServiceContainer) << "Initialized:" << m_initialized;
    
    for (const auto &pair : m_services) {
        const auto &registration = pair.second;
        
        qCDebug(lcServiceContainer) << "Service:" << registration.typeName
                                   << "Singleton:" << registration.isSingleton
                                   << "Initialized:" << registration.isInitialized
                                   << "Has Instance:" << (registration.instance != nullptr)
                                   << "Has Factory:" << (registration.factory != nullptr);
    }
    
    qCDebug(lcServiceContainer) << "=== End Dump ===";
}

// 获取服务的详细信息（用于调试和监控）
QVariantMap ServiceContainer::getServiceInfo(const QString &serviceName) const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    for (const auto &pair : m_services) {
        const auto &registration = pair.second;
        
        if (registration.typeName == serviceName) {
            QVariantMap info;
            info["name"] = registration.typeName;
            info["isSingleton"] = registration.isSingleton;
            info["isInitialized"] = registration.isInitialized;
            info["hasInstance"] = (registration.instance != nullptr);
            info["hasFactory"] = (registration.factory != nullptr);
            
            return info;
        }
    }
    
    return QVariantMap();
}

// 检查服务依赖关系（简化版本）
bool ServiceContainer::checkDependencies() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 这里可以实现更复杂的依赖检查逻辑
    // 例如检查循环依赖、缺失依赖等
    
    qCDebug(lcServiceContainer) << "Dependency check completed";
    return true;
}

// 获取服务创建的统计信息
QVariantMap ServiceContainer::getStatistics() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    QVariantMap stats;
    stats["totalServices"] = static_cast<int>(m_services.size());
    stats["initialized"] = m_initialized;
    
    int singletonCount = 0;
    int initializedCount = 0;
    int instanceCount = 0;
    
    for (const auto &pair : m_services) {
        const auto &registration = pair.second;
        
        if (registration.isSingleton) {
            singletonCount++;
        }
        
        if (registration.isInitialized) {
            initializedCount++;
        }
        
        if (registration.instance) {
            instanceCount++;
        }
    }
    
    stats["singletonCount"] = singletonCount;
    stats["initializedCount"] = initializedCount;
    stats["instanceCount"] = instanceCount;
    
    return stats;
}
