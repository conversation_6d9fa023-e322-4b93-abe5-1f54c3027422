#pragma once

#include <QString>
#include <QStringList>
#include <QDateTime>
#include <QIcon>
#include <QVariantMap>

/**
 * @brief 启动项类型枚举
 */
enum class LaunchItemType {
    Application = 0,    // 应用程序
    Document = 1,       // 文档文件
    Folder = 2,         // 文件夹
    Url = 3,           // 网址
    Command = 4,       // 命令行
    Script = 5         // 脚本
};

/**
 * @brief 启动项实体类
 * 
 * 表示一个可启动的项目，包含应用程序、文档、文件夹、网址等
 */
class LaunchItem
{
public:
    /**
     * @brief 默认构造函数
     */
    LaunchItem();
    
    /**
     * @brief 构造函数
     * @param name 名称
     * @param path 路径
     * @param type 类型
     */
    LaunchItem(const QString &name, const QString &path, LaunchItemType type = LaunchItemType::Application);
    
    /**
     * @brief 拷贝构造函数
     */
    LaunchItem(const LaunchItem &other);
    
    /**
     * @brief 赋值操作符
     */
    LaunchItem& operator=(const LaunchItem &other);
    
    /**
     * @brief 移动构造函数
     */
    LaunchItem(LaunchItem &&other) noexcept;
    
    /**
     * @brief 移动赋值操作符
     */
    LaunchItem& operator=(LaunchItem &&other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~LaunchItem() = default;

    // 基本属性
    int id = -1;                           // 唯一标识符
    QString name;                          // 显示名称
    QString path;                          // 文件路径或URL
    QString description;                   // 描述信息
    QString iconPath;                      // 图标路径
    LaunchItemType type = LaunchItemType::Application; // 项目类型
    int categoryId = -1;                   // 分类ID
    
    // 启动参数
    QString arguments;                     // 启动参数
    QString workingDirectory;              // 工作目录
    QString runAsUser;                     // 运行用户（Linux/macOS）
    bool runAsAdmin = false;               // 是否以管理员身份运行
    bool runInTerminal = false;            // 是否在终端中运行
    
    // 显示属性
    QStringList tags;                      // 标签列表
    QStringList keywords;                  // 关键词列表（用于搜索）
    QString shortcut;                      // 快捷键
    int priority = 0;                      // 优先级（用于排序）
    bool isEnabled = true;                 // 是否启用
    bool isVisible = true;                 // 是否在列表中显示
    bool isPinned = false;                 // 是否置顶
    
    // 统计信息
    int useCount = 0;                      // 使用次数
    QDateTime lastUsed;                    // 最后使用时间
    QDateTime firstUsed;                   // 首次使用时间
    int totalRunTime = 0;                  // 总运行时间（秒）
    int avgRunTime = 0;                    // 平均运行时间（秒）
    double rating = 0.0;                   // 用户评分（0-5）
    
    // 文件信息
    qint64 fileSize = 0;                   // 文件大小
    QDateTime fileModified;                // 文件修改时间
    QString fileVersion;                   // 文件版本
    QString fileDescription;               // 文件描述
    QString fileCompany;                   // 文件公司
    QString fileCopyright;                 // 文件版权
    
    // 系统信息
    QDateTime createdAt;                   // 创建时间
    QDateTime updatedAt;                   // 更新时间
    QString createdBy;                     // 创建者
    QString source;                        // 来源（手动添加、自动扫描、导入等）
    
    // 扩展属性
    QVariantMap metadata;                  // 元数据（自定义属性）
    QVariantMap settings;                  // 设置信息
    
    /**
     * @brief 检查启动项是否有效
     * @return 是否有效
     */
    bool isValid() const;
    
    /**
     * @brief 检查文件是否存在
     * @return 文件是否存在
     */
    bool fileExists() const;
    
    /**
     * @brief 获取显示名称（如果name为空则使用文件名）
     * @return 显示名称
     */
    QString getDisplayName() const;
    
    /**
     * @brief 获取文件名（不含路径）
     * @return 文件名
     */
    QString getFileName() const;
    
    /**
     * @brief 获取文件扩展名
     * @return 扩展名
     */
    QString getFileExtension() const;
    
    /**
     * @brief 获取文件目录
     * @return 目录路径
     */
    QString getFileDirectory() const;
    
    /**
     * @brief 获取图标
     * @return 图标对象
     */
    QIcon getIcon() const;
    
    /**
     * @brief 获取类型字符串
     * @return 类型字符串
     */
    QString getTypeString() const;
    
    /**
     * @brief 获取类型显示名称
     * @return 类型显示名称
     */
    QString getTypeDisplayName() const;
    
    /**
     * @brief 更新文件信息
     * 
     * 从文件系统读取文件的最新信息并更新相关属性
     */
    void updateFileInfo();
    
    /**
     * @brief 记录使用
     * @param runTime 运行时间（秒）
     */
    void recordUsage(int runTime = 0);
    
    /**
     * @brief 计算相关性分数
     * @param query 查询字符串
     * @return 相关性分数（0-1）
     */
    double calculateRelevanceScore(const QString &query) const;
    
    /**
     * @brief 匹配搜索查询
     * @param query 查询字符串
     * @param fuzzy 是否模糊匹配
     * @return 是否匹配
     */
    bool matchesQuery(const QString &query, bool fuzzy = true) const;
    
    /**
     * @brief 转换为JSON对象
     * @return JSON对象
     */
    QVariantMap toJson() const;
    
    /**
     * @brief 从JSON对象创建
     * @param json JSON对象
     * @return 启动项对象
     */
    static LaunchItem fromJson(const QVariantMap &json);
    
    /**
     * @brief 比较操作符
     */
    bool operator==(const LaunchItem &other) const;
    bool operator!=(const LaunchItem &other) const;
    bool operator<(const LaunchItem &other) const;
    
    /**
     * @brief 获取哈希值
     * @return 哈希值
     */
    uint hash() const;

private:
    /**
     * @brief 初始化默认值
     */
    void initializeDefaults();
    
    /**
     * @brief 规范化路径
     * @param path 原始路径
     * @return 规范化后的路径
     */
    QString normalizePath(const QString &path) const;
    
    /**
     * @brief 提取文件图标
     * @param filePath 文件路径
     * @return 图标路径
     */
    QString extractFileIcon(const QString &filePath) const;
    
    /**
     * @brief 计算名称匹配分数
     * @param query 查询字符串
     * @return 匹配分数
     */
    double calculateNameScore(const QString &query) const;
    
    /**
     * @brief 计算路径匹配分数
     * @param query 查询字符串
     * @return 匹配分数
     */
    double calculatePathScore(const QString &query) const;
    
    /**
     * @brief 计算标签匹配分数
     * @param query 查询字符串
     * @return 匹配分数
     */
    double calculateTagScore(const QString &query) const;
    
    /**
     * @brief 计算使用频率分数
     * @return 使用频率分数
     */
    double calculateUsageScore() const;
};

/**
 * @brief 启动项类型转换函数
 */
QString launchItemTypeToString(LaunchItemType type);
LaunchItemType launchItemTypeFromString(const QString &typeString);

/**
 * @brief 哈希函数（用于QHash）
 */
uint qHash(const LaunchItem &item, uint seed = 0);
