#include "TrayIcon.h"
#include "MainWindow.h"
#include "business/managers/LaunchManager.h"
#include "business/managers/ConfigManager.h"
#include <QApplication>
#include <QMenu>
#include <QAction>
#include <QTimer>
#include <QLoggingCategory>
#include <QMessageBox>
#include <QDesktopServices>
#include <QUrl>

Q_LOGGING_CATEGORY(lcTrayIcon, "trayicon")

TrayIcon::TrayIcon(QObject *parent)
    : QSystemTrayIcon(parent)
    , m_notificationTimer(new QTimer(this))
{
    // 设置图标
    setIcon(QIcon(":/icons/app.png"));
    setToolTip("KK QuickLaunch - 智能启动助手");
    
    // 设置通知定时器
    m_notificationTimer->setSingleShot(true);
    m_notificationTimer->setInterval(NOTIFICATION_DURATION);
    connect(m_notificationTimer, &QTimer::timeout, this, &TrayIcon::onNotificationTimeout);
    
    // 创建上下文菜单
    createContextMenu();
    
    // 连接信号
    setupConnections();
    
    qCDebug(lcTrayIcon) << "TrayIcon created";
}

TrayIcon::~TrayIcon()
{
    qCDebug(lcTrayIcon) << "TrayIcon destroyed";
}

bool TrayIcon::initialize()
{
    if (!QSystemTrayIcon::isSystemTrayAvailable()) {
        qCWarning(lcTrayIcon) << "System tray is not available";
        return false;
    }
    
    // 显示托盘图标
    show();
    
    // 显示启动通知
    if (m_configManager && m_configManager->getBool("ui.show_startup_notification", true)) {
        showNotification("KK QuickLaunch", "应用程序已启动，点击托盘图标打开主窗口", NotificationType::Information);
    }
    
    qCInfo(lcTrayIcon) << "TrayIcon initialized successfully";
    return true;
}

void TrayIcon::showNotification(const QString &title, const QString &message, NotificationType type)
{
    QSystemTrayIcon::MessageIcon icon;
    
    switch (type) {
    case NotificationType::Information:
        icon = QSystemTrayIcon::Information;
        break;
    case NotificationType::Warning:
        icon = QSystemTrayIcon::Warning;
        break;
    case NotificationType::Critical:
        icon = QSystemTrayIcon::Critical;
        break;
    default:
        icon = QSystemTrayIcon::NoIcon;
        break;
    }
    
    showMessage(title, message, icon, NOTIFICATION_DURATION);
    
    // 记录通知
    NotificationInfo notification;
    notification.title = title;
    notification.message = message;
    notification.type = type;
    notification.timestamp = QDateTime::currentDateTime();
    
    m_recentNotifications.prepend(notification);
    
    // 限制通知历史数量
    while (m_recentNotifications.size() > MAX_NOTIFICATION_HISTORY) {
        m_recentNotifications.removeLast();
    }
    
    qCDebug(lcTrayIcon) << "Notification shown:" << title << "-" << message;
}

void TrayIcon::showQuickLaunchMenu()
{
    if (!m_quickLaunchMenu) {
        createQuickLaunchMenu();
    }
    
    updateQuickLaunchMenu();
    
    // 显示菜单
    QPoint pos = geometry().bottomLeft();
    m_quickLaunchMenu->popup(pos);
    
    qCDebug(lcTrayIcon) << "Quick launch menu shown";
}

void TrayIcon::updateQuickLaunchMenu()
{
    if (!m_quickLaunchMenu || !m_launchManager) {
        return;
    }
    
    // 清除现有的快速启动项
    for (QAction *action : m_quickLaunchActions) {
        m_quickLaunchMenu->removeAction(action);
        delete action;
    }
    m_quickLaunchActions.clear();
    
    // 获取置顶项目
    QList<LaunchItem> pinnedItems = m_launchManager->getPinnedItems();
    
    if (pinnedItems.isEmpty()) {
        QAction *emptyAction = new QAction("没有置顶项目", this);
        emptyAction->setEnabled(false);
        m_quickLaunchMenu->addAction(emptyAction);
        m_quickLaunchActions.append(emptyAction);
    } else {
        for (const LaunchItem &item : pinnedItems) {
            QAction *action = new QAction(item.name, this);
            action->setIcon(QIcon(item.iconPath));
            action->setToolTip(item.path);
            action->setData(item.id);
            
            connect(action, &QAction::triggered, this, [this, item]() {
                launchItem(item.id);
            });
            
            m_quickLaunchMenu->addAction(action);
            m_quickLaunchActions.append(action);
        }
    }
    
    qCDebug(lcTrayIcon) << "Quick launch menu updated with" << pinnedItems.size() << "items";
}

void TrayIcon::launchItem(int itemId)
{
    if (!m_launchManager) {
        qCWarning(lcTrayIcon) << "LaunchManager not available";
        return;
    }
    
    LaunchResult result = m_launchManager->launchItem(itemId);
    
    if (result == LaunchResult::Success) {
        LaunchItem item = m_launchManager->getItem(itemId);
        showNotification("启动成功", QString("已启动: %1").arg(item.name), NotificationType::Information);
    } else {
        showNotification("启动失败", "无法启动选定的项目", NotificationType::Warning);
    }
    
    qCDebug(lcTrayIcon) << "Launch item" << itemId << "result:" << static_cast<int>(result);
}

void TrayIcon::onActivated(QSystemTrayIcon::ActivationReason reason)
{
    switch (reason) {
    case QSystemTrayIcon::Trigger:
        // 单击 - 显示/隐藏主窗口
        emit showMainWindowRequested();
        break;
        
    case QSystemTrayIcon::DoubleClick:
        // 双击 - 显示主窗口
        emit showMainWindowRequested();
        break;
        
    case QSystemTrayIcon::MiddleClick:
        // 中键点击 - 显示快速启动菜单
        showQuickLaunchMenu();
        break;
        
    case QSystemTrayIcon::Context:
        // 右键点击 - 显示上下文菜单（自动处理）
        break;
        
    default:
        break;
    }
    
    qCDebug(lcTrayIcon) << "Tray icon activated, reason:" << reason;
}

void TrayIcon::onMessageClicked()
{
    // 点击通知消息时显示主窗口
    emit showMainWindowRequested();
    qCDebug(lcTrayIcon) << "Notification message clicked";
}

void TrayIcon::onShowMainWindow()
{
    emit showMainWindowRequested();
}

void TrayIcon::onShowSettings()
{
    emit showSettingsRequested();
}

void TrayIcon::onShowStatistics()
{
    emit showStatisticsRequested();
}

void TrayIcon::onShowAbout()
{
    emit showAboutRequested();
}

void TrayIcon::onQuitApplication()
{
    // 显示确认对话框
    if (m_configManager && m_configManager->getBool("ui.confirm_quit", true)) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            nullptr,
            "确认退出",
            "确定要退出 KK QuickLaunch 吗？",
            QMessageBox::Yes | QMessageBox::No,
            QMessageBox::No
        );
        
        if (reply != QMessageBox::Yes) {
            return;
        }
    }
    
    emit quitApplicationRequested();
    qCDebug(lcTrayIcon) << "Quit application requested";
}

void TrayIcon::onNotificationTimeout()
{
    // 通知超时处理
    qCDebug(lcTrayIcon) << "Notification timeout";
}

void TrayIcon::onConfigChanged(const QString &key, const QVariant &value)
{
    Q_UNUSED(value)
    
    if (key.startsWith("tray.")) {
        // 托盘相关配置变更
        updateTraySettings();
    }
    
    qCDebug(lcTrayIcon) << "Config changed:" << key;
}

void TrayIcon::createContextMenu()
{
    m_contextMenu = new QMenu();
    
    // 主要操作
    m_showAction = new QAction("显示主窗口(&S)", this);
    m_showAction->setIcon(QIcon(":/icons/show.png"));
    connect(m_showAction, &QAction::triggered, this, &TrayIcon::onShowMainWindow);
    
    m_quickLaunchAction = new QAction("快速启动(&Q)", this);
    m_quickLaunchAction->setIcon(QIcon(":/icons/launch.png"));
    connect(m_quickLaunchAction, &QAction::triggered, this, &TrayIcon::showQuickLaunchMenu);
    
    // 设置和帮助
    m_settingsAction = new QAction("设置(&P)", this);
    m_settingsAction->setIcon(QIcon(":/icons/settings.png"));
    connect(m_settingsAction, &QAction::triggered, this, &TrayIcon::onShowSettings);
    
    m_statisticsAction = new QAction("统计(&T)", this);
    m_statisticsAction->setIcon(QIcon(":/icons/statistics.png"));
    connect(m_statisticsAction, &QAction::triggered, this, &TrayIcon::onShowStatistics);
    
    m_aboutAction = new QAction("关于(&A)", this);
    m_aboutAction->setIcon(QIcon(":/icons/about.png"));
    connect(m_aboutAction, &QAction::triggered, this, &TrayIcon::onShowAbout);
    
    m_quitAction = new QAction("退出(&X)", this);
    m_quitAction->setIcon(QIcon(":/icons/quit.png"));
    connect(m_quitAction, &QAction::triggered, this, &TrayIcon::onQuitApplication);
    
    // 构建菜单
    m_contextMenu->addAction(m_showAction);
    m_contextMenu->addAction(m_quickLaunchAction);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_settingsAction);
    m_contextMenu->addAction(m_statisticsAction);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_aboutAction);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_quitAction);
    
    setContextMenu(m_contextMenu);
    
    qCDebug(lcTrayIcon) << "Context menu created";
}

void TrayIcon::createQuickLaunchMenu()
{
    m_quickLaunchMenu = new QMenu();
    m_quickLaunchMenu->setTitle("快速启动");
    
    qCDebug(lcTrayIcon) << "Quick launch menu created";
}

void TrayIcon::setupConnections()
{
    connect(this, &QSystemTrayIcon::activated, this, &TrayIcon::onActivated);
    connect(this, &QSystemTrayIcon::messageClicked, this, &TrayIcon::onMessageClicked);
    
    qCDebug(lcTrayIcon) << "Connections setup completed";
}

void TrayIcon::updateTraySettings()
{
    if (!m_configManager) {
        return;
    }
    
    // 更新托盘图标
    QString iconPath = m_configManager->getString("tray.icon_path", ":/icons/app.png");
    setIcon(QIcon(iconPath));
    
    // 更新工具提示
    QString tooltip = m_configManager->getString("tray.tooltip", "KK QuickLaunch");
    setToolTip(tooltip);
    
    qCDebug(lcTrayIcon) << "Tray settings updated";
}
