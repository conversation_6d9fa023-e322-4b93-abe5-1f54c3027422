#pragma once

#include <memory>
#include <unordered_map>
#include <typeindex>
#include <functional>
#include <mutex>
#include <QObject>

/**
 * @brief 依赖注入服务容器
 * 
 * 提供服务的注册、获取和生命周期管理功能。
 * 支持单例模式和工厂模式的服务创建。
 */
class ServiceContainer : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit ServiceContainer(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~ServiceContainer();

    /**
     * @brief 注册服务类型（工厂模式）
     * @tparam Interface 接口类型
     * @tparam Implementation 实现类型
     */
    template<typename Interface, typename Implementation>
    void registerService();
    
    /**
     * @brief 注册服务实例（单例模式）
     * @tparam Interface 接口类型
     * @param instance 服务实例
     */
    template<typename Interface>
    void registerService(std::shared_ptr<Interface> instance);
    
    /**
     * @brief 注册服务工厂函数
     * @tparam Interface 接口类型
     * @param factory 工厂函数
     */
    template<typename Interface>
    void registerFactory(std::function<std::shared_ptr<Interface>()> factory);

    /**
     * @brief 获取服务实例
     * @tparam Interface 接口类型
     * @return 服务实例，如果未注册则返回nullptr
     */
    template<typename Interface>
    std::shared_ptr<Interface> getService();
    
    /**
     * @brief 获取服务实例（常量版本）
     * @tparam Interface 接口类型
     * @return 服务实例，如果未注册则返回nullptr
     */
    template<typename Interface>
    std::shared_ptr<const Interface> getService() const;

    /**
     * @brief 检查服务是否已注册
     * @tparam Interface 接口类型
     * @return 是否已注册
     */
    template<typename Interface>
    bool hasService() const;
    
    /**
     * @brief 移除服务注册
     * @tparam Interface 接口类型
     */
    template<typename Interface>
    void removeService();

    /**
     * @brief 初始化所有服务
     * 
     * 对于实现了IInitializable接口的服务，会调用其initialize方法
     */
    void initializeServices();
    
    /**
     * @brief 清理所有服务
     */
    void clear();
    
    /**
     * @brief 获取已注册服务的数量
     * @return 服务数量
     */
    size_t getServiceCount() const;
    
    /**
     * @brief 获取所有已注册的服务类型名称
     * @return 服务类型名称列表
     */
    QStringList getRegisteredServiceNames() const;

signals:
    /**
     * @brief 服务注册信号
     * @param serviceName 服务名称
     */
    void serviceRegistered(const QString &serviceName);
    
    /**
     * @brief 服务移除信号
     * @param serviceName 服务名称
     */
    void serviceRemoved(const QString &serviceName);
    
    /**
     * @brief 服务初始化完成信号
     */
    void servicesInitialized();

private:
    /**
     * @brief 服务注册信息
     */
    struct ServiceRegistration {
        std::shared_ptr<void> instance;                    // 服务实例
        std::function<std::shared_ptr<void>()> factory;    // 工厂函数
        std::type_index typeIndex;                         // 类型索引
        QString typeName;                                  // 类型名称
        bool isSingleton;                                  // 是否单例
        bool isInitialized;                               // 是否已初始化
    };

    /**
     * @brief 获取类型名称
     * @tparam T 类型
     * @return 类型名称
     */
    template<typename T>
    QString getTypeName() const;
    
    /**
     * @brief 初始化单个服务
     * @param registration 服务注册信息
     */
    void initializeService(ServiceRegistration &registration);

private:
    mutable std::mutex m_mutex;                                           // 线程安全锁
    std::unordered_map<std::type_index, ServiceRegistration> m_services; // 服务注册表
    bool m_initialized = false;                                           // 是否已初始化
};

// 模板方法实现

template<typename Interface, typename Implementation>
void ServiceContainer::registerService()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto typeIndex = std::type_index(typeid(Interface));
    auto factory = []() -> std::shared_ptr<void> {
        return std::static_pointer_cast<void>(std::make_shared<Implementation>());
    };
    
    ServiceRegistration registration;
    registration.factory = factory;
    registration.typeIndex = typeIndex;
    registration.typeName = getTypeName<Interface>();
    registration.isSingleton = false;
    registration.isInitialized = false;
    
    m_services[typeIndex] = std::move(registration);
    
    emit serviceRegistered(getTypeName<Interface>());
}

template<typename Interface>
void ServiceContainer::registerService(std::shared_ptr<Interface> instance)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto typeIndex = std::type_index(typeid(Interface));
    
    ServiceRegistration registration;
    registration.instance = std::static_pointer_cast<void>(instance);
    registration.typeIndex = typeIndex;
    registration.typeName = getTypeName<Interface>();
    registration.isSingleton = true;
    registration.isInitialized = false;
    
    m_services[typeIndex] = std::move(registration);
    
    emit serviceRegistered(getTypeName<Interface>());
}

template<typename Interface>
void ServiceContainer::registerFactory(std::function<std::shared_ptr<Interface>()> factory)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto typeIndex = std::type_index(typeid(Interface));
    auto wrappedFactory = [factory]() -> std::shared_ptr<void> {
        return std::static_pointer_cast<void>(factory());
    };
    
    ServiceRegistration registration;
    registration.factory = wrappedFactory;
    registration.typeIndex = typeIndex;
    registration.typeName = getTypeName<Interface>();
    registration.isSingleton = false;
    registration.isInitialized = false;
    
    m_services[typeIndex] = std::move(registration);
    
    emit serviceRegistered(getTypeName<Interface>());
}

template<typename Interface>
std::shared_ptr<Interface> ServiceContainer::getService()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto typeIndex = std::type_index(typeid(Interface));
    auto it = m_services.find(typeIndex);
    
    if (it == m_services.end()) {
        return nullptr;
    }
    
    auto &registration = it->second;
    
    // 如果是单例且已有实例，直接返回
    if (registration.isSingleton && registration.instance) {
        return std::static_pointer_cast<Interface>(registration.instance);
    }
    
    // 如果有工厂函数，使用工厂创建实例
    if (registration.factory) {
        auto instance = registration.factory();
        
        // 如果是单例，缓存实例
        if (registration.isSingleton) {
            registration.instance = instance;
        }
        
        return std::static_pointer_cast<Interface>(instance);
    }
    
    return nullptr;
}

template<typename Interface>
std::shared_ptr<const Interface> ServiceContainer::getService() const
{
    return const_cast<ServiceContainer*>(this)->getService<Interface>();
}

template<typename Interface>
bool ServiceContainer::hasService() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto typeIndex = std::type_index(typeid(Interface));
    return m_services.find(typeIndex) != m_services.end();
}

template<typename Interface>
void ServiceContainer::removeService()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto typeIndex = std::type_index(typeid(Interface));
    auto it = m_services.find(typeIndex);
    
    if (it != m_services.end()) {
        QString serviceName = it->second.typeName;
        m_services.erase(it);
        emit serviceRemoved(serviceName);
    }
}

template<typename T>
QString ServiceContainer::getTypeName() const
{
    // 简化的类型名称获取，实际项目中可能需要更复杂的实现
    return QString::fromStdString(typeid(T).name());
}
