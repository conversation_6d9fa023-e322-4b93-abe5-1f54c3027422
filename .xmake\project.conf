# XMake 项目配置文件
# 用于配置项目的默认设置和环境

# 项目基本信息
project_name = "KKQuickLaunch"
project_version = "1.0.0"
project_description = "智能启动助手 - 现代化的应用程序启动器"

# 默认构建配置
default_mode = "release"
default_plat = "auto"  # 自动检测平台
default_arch = "auto"  # 自动检测架构

# Qt配置
qt_version = "6.5"
qt_modules = ["widgets", "core", "gui", "sql", "multimedia", "texttospeech"]

# 编译器配置
cxx_standard = "c++20"
warning_level = "all"
treat_warnings_as_errors = true

# 优化配置
[optimization]
debug_symbols = true
debug_optimize = "none"
release_optimize = "fastest"
release_strip = true

# 平台特定配置
[windows]
subsystem = "windows"
runtime = "MT"  # 静态链接运行时
additional_libs = ["user32", "shell32", "ole32", "oleaut32", "uuid", "advapi32"]

[linux]
additional_libs = ["pthread", "dl"]
pic = true  # 位置无关代码

[macosx]
frameworks = ["Cocoa", "Foundation"]
deployment_target = "10.15"

# 目录配置
[directories]
source_dir = "src"
include_dir = "src"
resource_dir = "resources"
test_dir = "tests"
build_dir = "build"
install_dir = "install"

# 包管理配置
[packages]
# Qt6相关包
qt6widgets = { version = ">=6.5.0", configs = {shared = true} }
qt6core = { version = ">=6.5.0", configs = {shared = true} }
qt6gui = { version = ">=6.5.0", configs = {shared = true} }
qt6sql = { version = ">=6.5.0", configs = {shared = true} }
qt6multimedia = { version = ">=6.5.0", configs = {shared = true} }
qt6texttospeech = { version = ">=6.5.0", configs = {shared = true} }
qt6test = { version = ">=6.5.0", configs = {shared = true}, optional = true }

# 测试配置
[testing]
framework = "qt"
timeout = 30  # 测试超时时间（秒）
parallel = true  # 并行运行测试
coverage = false  # 代码覆盖率

# 打包配置
[packaging]
formats = ["zip", "tar.gz"]
include_docs = true
include_resources = true
include_dependencies = false  # 不包含Qt依赖（用户需要单独安装）

# 安装配置
[installation]
# Windows默认安装路径
windows_prefix = "C:/Program Files/KKQuickLaunch"
# Linux/macOS默认安装路径
unix_prefix = "/usr/local"

# 创建桌面快捷方式
create_desktop_shortcut = true
# 创建开始菜单项
create_start_menu = true
# 注册文件关联
register_file_associations = false

# 开发配置
[development]
auto_reload = true  # 自动重新加载配置
verbose_output = false  # 详细输出
parallel_build = true  # 并行构建
ccache = true  # 使用ccache加速编译

# 代码质量配置
[quality]
static_analysis = false  # 静态代码分析
format_check = false  # 代码格式检查
lint = false  # 代码检查

# 文档配置
[documentation]
generate_docs = false  # 生成文档
doc_format = "html"  # 文档格式
include_private = false  # 包含私有成员

# 部署配置
[deployment]
auto_deploy = false  # 自动部署
deploy_target = "local"  # 部署目标
backup_before_deploy = true  # 部署前备份
