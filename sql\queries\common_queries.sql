-- ============================================================================
-- 智能启动助手 - 常用查询SQL
-- 版本: 1.0.0
-- 创建时间: 2024-12-17
-- 说明: 包含常用的数据查询和统计SQL语句
-- ============================================================================

-- ============================================================================
-- 1. 启动项相关查询
-- ============================================================================

-- 1.1 获取所有启用的启动项(按使用频率排序)
SELECT 
    li.id,
    li.name,
    li.path,
    c.display_name as category,
    li.use_count,
    li.last_used,
    li.priority,
    CASE 
        WHEN li.last_used > datetime('now', '-7 days') THEN '最近使用'
        WHEN li.last_used > datetime('now', '-30 days') THEN '偶尔使用'
        WHEN li.last_used IS NULL THEN '从未使用'
        ELSE '很久未用'
    END as usage_status
FROM launch_items li
LEFT JOIN categories c ON li.category_id = c.id
WHERE li.is_enabled = 1
ORDER BY li.use_count DESC, li.last_used DESC;

-- 1.2 搜索启动项(模糊匹配名称和标签)
-- 参数: :search_term
SELECT 
    li.id,
    li.name,
    li.path,
    li.description,
    c.display_name as category,
    li.use_count,
    -- 计算匹配度分数
    (CASE WHEN li.name LIKE '%' || :search_term || '%' THEN 10 ELSE 0 END +
     CASE WHEN li.description LIKE '%' || :search_term || '%' THEN 5 ELSE 0 END +
     CASE WHEN li.tags LIKE '%' || :search_term || '%' THEN 3 ELSE 0 END +
     CASE WHEN c.display_name LIKE '%' || :search_term || '%' THEN 2 ELSE 0 END) as relevance_score
FROM launch_items li
LEFT JOIN categories c ON li.category_id = c.id
WHERE li.is_enabled = 1
  AND (li.name LIKE '%' || :search_term || '%' 
       OR li.description LIKE '%' || :search_term || '%'
       OR li.tags LIKE '%' || :search_term || '%'
       OR c.display_name LIKE '%' || :search_term || '%')
ORDER BY relevance_score DESC, li.use_count DESC
LIMIT 20;

-- 1.3 获取指定分类的启动项
-- 参数: :category_id
SELECT 
    li.id,
    li.name,
    li.path,
    li.icon_data,
    li.description,
    li.use_count,
    li.last_used,
    li.priority
FROM launch_items li
WHERE li.category_id = :category_id 
  AND li.is_enabled = 1
ORDER BY li.priority DESC, li.use_count DESC, li.name;

-- 1.4 获取最近使用的启动项
SELECT 
    li.id,
    li.name,
    li.path,
    li.icon_data,
    c.display_name as category,
    li.last_used,
    li.use_count,
    ROUND((julianday('now') - julianday(li.last_used)) * 24, 1) as hours_ago
FROM launch_items li
LEFT JOIN categories c ON li.category_id = c.id
WHERE li.is_enabled = 1 
  AND li.last_used IS NOT NULL
ORDER BY li.last_used DESC
LIMIT 10;

-- 1.5 获取从未使用过的启动项
SELECT 
    li.id,
    li.name,
    li.path,
    c.display_name as category,
    li.created_at,
    ROUND((julianday('now') - julianday(li.created_at)) * 24, 1) as hours_since_added
FROM launch_items li
LEFT JOIN categories c ON li.category_id = c.id
WHERE li.is_enabled = 1 
  AND (li.use_count = 0 OR li.last_used IS NULL)
ORDER BY li.created_at DESC;

-- ============================================================================
-- 2. 统计分析查询
-- ============================================================================

-- 2.1 使用统计总览
SELECT 
    COUNT(*) as total_items,
    COUNT(CASE WHEN is_enabled = 1 THEN 1 END) as enabled_items,
    COUNT(CASE WHEN use_count > 0 THEN 1 END) as used_items,
    SUM(use_count) as total_launches,
    AVG(use_count) as avg_launches_per_item,
    MAX(use_count) as max_launches,
    COUNT(CASE WHEN last_used > datetime('now', '-7 days') THEN 1 END) as active_last_week,
    COUNT(CASE WHEN last_used > datetime('now', '-30 days') THEN 1 END) as active_last_month
FROM launch_items;

-- 2.2 分类使用统计
SELECT 
    c.id,
    c.display_name as category,
    COUNT(li.id) as item_count,
    COUNT(CASE WHEN li.is_enabled = 1 THEN 1 END) as enabled_count,
    SUM(COALESCE(li.use_count, 0)) as total_usage,
    AVG(COALESCE(li.use_count, 0)) as avg_usage,
    MAX(li.last_used) as last_used_in_category,
    ROUND(
        COUNT(li.id) * 100.0 / (SELECT COUNT(*) FROM launch_items), 
        2
    ) as percentage_of_total
FROM categories c
LEFT JOIN launch_items li ON c.id = li.category_id
WHERE c.is_visible = 1
GROUP BY c.id, c.display_name
ORDER BY total_usage DESC;

-- 2.3 每日使用趋势(最近30天)
SELECT 
    DATE(ua.timestamp) as date,
    COUNT(*) as launch_count,
    COUNT(DISTINCT ua.launch_item_id) as unique_apps,
    COUNT(DISTINCT ua.session_id) as sessions,
    AVG(ua.response_time) as avg_response_time
FROM user_actions ua
WHERE ua.action_type = 'launch' 
  AND ua.timestamp >= datetime('now', '-30 days')
GROUP BY DATE(ua.timestamp)
ORDER BY date DESC;

-- 2.4 每小时使用模式
SELECT 
    CAST(strftime('%H', ua.timestamp) AS INTEGER) as hour,
    COUNT(*) as launch_count,
    COUNT(DISTINCT ua.launch_item_id) as unique_apps,
    AVG(ua.response_time) as avg_response_time,
    -- 计算该小时占全天的百分比
    ROUND(
        COUNT(*) * 100.0 / (
            SELECT COUNT(*) 
            FROM user_actions 
            WHERE action_type = 'launch' 
              AND timestamp >= datetime('now', '-30 days')
        ), 
        2
    ) as percentage
FROM user_actions ua
WHERE ua.action_type = 'launch' 
  AND ua.timestamp >= datetime('now', '-30 days')
GROUP BY CAST(strftime('%H', ua.timestamp) AS INTEGER)
ORDER BY hour;

-- 2.5 最受欢迎的应用TOP 10
SELECT 
    li.id,
    li.name,
    li.path,
    c.display_name as category,
    li.use_count,
    li.last_used,
    ROUND(
        li.use_count * 100.0 / (
            SELECT SUM(use_count) 
            FROM launch_items 
            WHERE use_count > 0
        ), 
        2
    ) as usage_percentage,
    -- 计算使用频率(次/天)
    CASE 
        WHEN li.created_at IS NOT NULL THEN
            ROUND(
                li.use_count * 1.0 / 
                MAX(1, (julianday('now') - julianday(li.created_at))), 
                2
            )
        ELSE 0
    END as frequency_per_day
FROM launch_items li
LEFT JOIN categories c ON li.category_id = c.id
WHERE li.use_count > 0
ORDER BY li.use_count DESC
LIMIT 10;

-- ============================================================================
-- 3. 推荐系统相关查询
-- ============================================================================

-- 3.1 获取文件类型推荐
-- 参数: :file_extension
SELECT 
    li.id,
    li.name,
    li.path,
    li.icon_data,
    fa.confidence_score,
    fa.use_count as association_usage,
    li.use_count as app_usage,
    fa.is_default,
    -- 计算综合推荐分数
    (fa.confidence_score * 0.4 + 
     LEAST(fa.use_count / 10.0, 1.0) * 0.3 + 
     LEAST(li.use_count / 100.0, 1.0) * 0.3) as recommendation_score
FROM file_associations fa
JOIN launch_items li ON fa.launch_item_id = li.id
WHERE fa.file_extension = :file_extension
  AND li.is_enabled = 1
ORDER BY recommendation_score DESC, fa.is_default DESC
LIMIT 5;

-- 3.2 基于使用时间的推荐
-- 参数: :current_hour (0-23)
SELECT 
    li.id,
    li.name,
    li.path,
    li.icon_data,
    c.display_name as category,
    COUNT(ua.id) as usage_at_this_hour,
    li.use_count as total_usage,
    -- 计算在此时间段的使用比例
    ROUND(
        COUNT(ua.id) * 100.0 / NULLIF(li.use_count, 0), 
        2
    ) as time_preference_percentage
FROM launch_items li
LEFT JOIN categories c ON li.category_id = c.id
LEFT JOIN user_actions ua ON li.id = ua.launch_item_id 
    AND ua.action_type = 'launch'
    AND CAST(strftime('%H', ua.timestamp) AS INTEGER) = :current_hour
WHERE li.is_enabled = 1
  AND li.use_count > 0
GROUP BY li.id, li.name, li.path, li.icon_data, c.display_name, li.use_count
HAVING COUNT(ua.id) > 0
ORDER BY time_preference_percentage DESC, usage_at_this_hour DESC
LIMIT 8;

-- 3.3 推荐规则效果统计
SELECT 
    rr.id,
    rr.name,
    rr.rule_type,
    rr.priority,
    rr.match_count,
    rr.success_count,
    CASE 
        WHEN rr.match_count > 0 
        THEN ROUND(rr.success_count * 100.0 / rr.match_count, 2)
        ELSE 0 
    END as success_rate,
    rr.is_enabled,
    rr.created_at
FROM recommendation_rules rr
ORDER BY success_rate DESC, rr.match_count DESC;

-- ============================================================================
-- 4. 搜索历史分析
-- ============================================================================

-- 4.1 热门搜索关键词
SELECT 
    sh.search_query,
    COUNT(*) as search_count,
    AVG(sh.result_count) as avg_results,
    AVG(sh.response_time) as avg_response_time,
    COUNT(CASE WHEN sh.selected_item_id IS NOT NULL THEN 1 END) as selection_count,
    -- 计算选择率
    ROUND(
        COUNT(CASE WHEN sh.selected_item_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 
        2
    ) as selection_rate,
    MAX(sh.timestamp) as last_searched
FROM search_history sh
WHERE sh.timestamp >= datetime('now', '-30 days')
GROUP BY sh.search_query
HAVING search_count > 1
ORDER BY search_count DESC, selection_rate DESC
LIMIT 20;

-- 4.2 搜索无结果的查询
SELECT 
    sh.search_query,
    COUNT(*) as search_count,
    MAX(sh.timestamp) as last_searched,
    AVG(sh.response_time) as avg_response_time
FROM search_history sh
WHERE sh.result_count = 0
  AND sh.timestamp >= datetime('now', '-30 days')
GROUP BY sh.search_query
ORDER BY search_count DESC, last_searched DESC;

-- 4.3 搜索性能分析
SELECT 
    CASE 
        WHEN response_time < 100 THEN '< 100ms'
        WHEN response_time < 300 THEN '100-300ms'
        WHEN response_time < 500 THEN '300-500ms'
        WHEN response_time < 1000 THEN '500ms-1s'
        ELSE '> 1s'
    END as response_time_range,
    COUNT(*) as search_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM search_history), 2) as percentage,
    AVG(result_count) as avg_results
FROM search_history
WHERE timestamp >= datetime('now', '-30 days')
GROUP BY 
    CASE 
        WHEN response_time < 100 THEN '< 100ms'
        WHEN response_time < 300 THEN '100-300ms'
        WHEN response_time < 500 THEN '300-500ms'
        WHEN response_time < 1000 THEN '500ms-1s'
        ELSE '> 1s'
    END
ORDER BY 
    CASE 
        WHEN response_time_range = '< 100ms' THEN 1
        WHEN response_time_range = '100-300ms' THEN 2
        WHEN response_time_range = '300-500ms' THEN 3
        WHEN response_time_range = '500ms-1s' THEN 4
        ELSE 5
    END;

-- ============================================================================
-- 5. 系统维护查询
-- ============================================================================

-- 5.1 数据库大小和表统计
SELECT 
    name as table_name,
    CASE type
        WHEN 'table' THEN (
            SELECT COUNT(*) 
            FROM sqlite_master sm2 
            WHERE sm2.type = 'index' AND sm2.tbl_name = sm1.name
        )
        ELSE 0
    END as index_count,
    -- 估算记录数(对于表)
    CASE type
        WHEN 'table' THEN 
            CASE name
                WHEN 'launch_items' THEN (SELECT COUNT(*) FROM launch_items)
                WHEN 'categories' THEN (SELECT COUNT(*) FROM categories)
                WHEN 'user_actions' THEN (SELECT COUNT(*) FROM user_actions)
                WHEN 'search_history' THEN (SELECT COUNT(*) FROM search_history)
                WHEN 'app_settings' THEN (SELECT COUNT(*) FROM app_settings)
                WHEN 'hotkey_settings' THEN (SELECT COUNT(*) FROM hotkey_settings)
                WHEN 'recommendation_rules' THEN (SELECT COUNT(*) FROM recommendation_rules)
                WHEN 'file_associations' THEN (SELECT COUNT(*) FROM file_associations)
                WHEN 'plugins' THEN (SELECT COUNT(*) FROM plugins)
                WHEN 'system_logs' THEN (SELECT COUNT(*) FROM system_logs)
                ELSE 0
            END
        ELSE NULL
    END as record_count
FROM sqlite_master sm1
WHERE type IN ('table', 'view')
  AND name NOT LIKE 'sqlite_%'
  AND name NOT LIKE 'backup_%'
ORDER BY type, name;

-- 5.2 查找重复的启动项
SELECT 
    path,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id) as item_ids,
    GROUP_CONCAT(name, ' | ') as names
FROM launch_items
GROUP BY path
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 5.3 查找无效路径的启动项
-- 注意: 这个查询需要在应用程序中执行文件存在性检查
SELECT 
    id,
    name,
    path,
    created_at,
    last_used,
    use_count
FROM launch_items
WHERE is_enabled = 1
  -- 这里只能做基本的路径格式检查，实际文件存在性需要在应用中检查
  AND (path = '' 
       OR path IS NULL 
       OR LENGTH(path) < 3
       OR path NOT LIKE '%:%' -- Windows路径应该包含冒号
      )
ORDER BY created_at DESC;

-- 5.4 清理建议查询
SELECT 
    'user_actions' as table_name,
    'DELETE FROM user_actions WHERE timestamp < datetime(''now'', ''-90 days'')' as cleanup_sql,
    (SELECT COUNT(*) FROM user_actions WHERE timestamp < datetime('now', '-90 days')) as affected_rows,
    '清理90天前的用户行为记录' as description

UNION ALL

SELECT 
    'search_history' as table_name,
    'DELETE FROM search_history WHERE timestamp < datetime(''now'', ''-30 days'')',
    (SELECT COUNT(*) FROM search_history WHERE timestamp < datetime('now', '-30 days')),
    '清理30天前的搜索历史'

UNION ALL

SELECT 
    'system_logs' as table_name,
    'DELETE FROM system_logs WHERE timestamp < datetime(''now'', ''-7 days'') AND log_level NOT IN (''ERROR'', ''FATAL'')',
    (SELECT COUNT(*) FROM system_logs WHERE timestamp < datetime('now', '-7 days') AND log_level NOT IN ('ERROR', 'FATAL')),
    '清理7天前的非错误日志';

-- ============================================================================
-- 6. 配置和设置查询
-- ============================================================================

-- 6.1 获取所有用户可配置的设置
SELECT 
    setting_key,
    setting_value,
    value_type,
    category,
    description,
    requires_restart
FROM app_settings
WHERE is_user_configurable = 1
ORDER BY category, setting_key;

-- 6.2 获取快捷键冲突检查
SELECT 
    h1.action_name as action1,
    h1.display_name as display1,
    h2.action_name as action2,
    h2.display_name as display2,
    h1.key_sequence as conflicting_key,
    h1.is_global as is_global1,
    h2.is_global as is_global2
FROM hotkey_settings h1
JOIN hotkey_settings h2 ON h1.key_sequence = h2.key_sequence 
    AND h1.id < h2.id
WHERE h1.is_enabled = 1 
  AND h2.is_enabled = 1
  AND (h1.is_global = 1 OR h2.is_global = 1 OR h1.is_global = h2.is_global)
ORDER BY h1.key_sequence;

-- 6.3 系统状态检查
SELECT 
    'Database Version' as item,
    setting_value as value,
    'OK' as status
FROM app_settings 
WHERE setting_key = 'system.database_version'

UNION ALL

SELECT 
    'Total Launch Items' as item,
    CAST(COUNT(*) AS TEXT) as value,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'WARNING' END as status
FROM launch_items

UNION ALL

SELECT 
    'Enabled Plugins' as item,
    CAST(COUNT(*) AS TEXT) as value,
    'OK' as status
FROM plugins 
WHERE is_enabled = 1

UNION ALL

SELECT 
    'Active Categories' as item,
    CAST(COUNT(*) AS TEXT) as value,
    CASE WHEN COUNT(*) > 0 THEN 'OK' ELSE 'ERROR' END as status
FROM categories 
WHERE is_visible = 1;
