# KK QuickLaunch - 智能启动助手

一个基于Qt的现代化桌面应用程序启动器，提供智能搜索、推荐系统和丰富的自定义功能。

## 🚀 功能特性

### 核心功能
- **智能搜索**: 支持模糊搜索、拼音搜索、语义搜索
- **推荐系统**: 基于使用习惯的智能推荐
- **分类管理**: 灵活的应用程序分类系统
- **全局热键**: 系统级快捷键支持
- **系统托盘**: 常驻后台，随时调用

### 高级功能
- **插件系统**: 可扩展的插件架构
- **主题系统**: 多种界面主题选择
- **数据同步**: 跨设备配置同步
- **使用统计**: 详细的使用分析报告
- **备份恢复**: 完整的配置备份方案

### 技术特性
- **现代架构**: MVVM + 分层架构设计
- **高性能**: 异步处理和智能缓存
- **跨平台**: 支持Windows、Linux、macOS
- **国际化**: 多语言界面支持
- **可测试**: 完整的单元测试覆盖

## 🏗️ 项目架构

```
KKQuickLaunch/
├── src/                           # 源代码
│   ├── application/               # 应用程序层
│   │   ├── Application.h/cpp      # 应用程序主类
│   │   └── ServiceContainer.h/cpp # 依赖注入容器
│   ├── presentation/              # 表示层
│   │   ├── views/                 # 视图组件
│   │   ├── viewmodels/            # 视图模型
│   │   └── models/                # 数据模型
│   ├── business/                  # 业务逻辑层
│   │   ├── managers/              # 业务管理器
│   │   ├── entities/              # 业务实体
│   │   └── algorithms/            # 算法实现
│   ├── services/                  # 服务层
│   ├── data/                      # 数据访问层
│   ├── infrastructure/            # 基础设施层
│   └── plugins/                   # 插件系统
├── resources/                     # 资源文件
├── tests/                         # 测试代码
├── docs/                          # 文档
└── tools/                         # 工具脚本
```

## 🛠️ 构建要求

### 系统要求
- **操作系统**: Windows 10+, Ubuntu 20.04+, macOS 10.15+
- **编译器**: 
  - Windows: MSVC 2019+ 或 MinGW-w64
  - Linux: GCC 9+ 或 Clang 10+
  - macOS: Xcode 12+

### 依赖库
- **Qt**: 6.5.0 或更高版本
- **构建工具**: XMake 2.8+ 或 CMake 3.20+
- **C++标准**: C++20

### 可选依赖
- **Conan**: 包管理器（推荐）
- **Doxygen**: 文档生成
- **LCOV**: 代码覆盖率分析

## 📦 构建步骤

### 方式一：XMake 构建（推荐）

#### 1. 安装 XMake
```bash
# Windows (PowerShell)
Invoke-Expression (Invoke-Webrequest 'https://xmake.io/psget.text' -UseBasicParsing).Content

# Linux/macOS
curl -fsSL https://xmake.io/shget.text | bash
```

#### 2. 克隆项目
```bash
git clone https://github.com/your-repo/KKQuickLaunch.git
cd KKQuickLaunch
```

#### 3. 快速构建
```bash
# Windows
build_xmake.bat

# Linux/macOS
chmod +x build_xmake.sh
./build_xmake.sh
```

#### 4. 手动构建
```bash
# 配置项目
xmake config -m release

# 构建
xmake build

# 运行
xmake run
```

### 方式二：CMake 构建

#### 1. 克隆项目
```bash
git clone https://github.com/your-repo/KKQuickLaunch.git
cd KKQuickLaunch
```

#### 2. 安装依赖（使用Conan）
```bash
# 安装Conan
pip install conan

# 安装项目依赖
conan install . --build=missing -s build_type=Release
```

#### 3. 配置构建
```bash
# 创建构建目录
mkdir build && cd build

# 配置CMake
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE=../conan_toolchain.cmake
```

### 4. 编译项目
```bash
# 编译
cmake --build . --config Release

# 或者使用并行编译
cmake --build . --config Release --parallel 4
```

### 5. 运行测试
```bash
# 运行所有测试
ctest --output-on-failure

# 或者运行特定测试
ctest -R test_service_container
```

### 6. 安装应用程序
```bash
# 安装到系统
cmake --install . --config Release

# 或者创建安装包
cmake --build . --target package
```

## 🚀 快速开始

### 基本使用
1. 启动应用程序
2. 使用全局热键 `Ctrl+Alt+Q` 调出主界面
3. 在搜索框中输入应用程序名称
4. 按回车键或点击启动应用程序

### 添加启动项
1. 点击主界面的"添加"按钮
2. 选择应用程序可执行文件
3. 设置名称、分类和其他属性
4. 保存启动项

### 配置热键
1. 打开设置界面
2. 切换到"热键"选项卡
3. 设置自定义快捷键
4. 应用设置

## 🔧 开发指南

### 代码规范
- 使用现代C++特性（C++20）
- 遵循Qt编码规范
- 使用智能指针管理内存
- 编写单元测试

### 添加新功能
1. 在相应的层级创建类文件
2. 实现业务逻辑
3. 编写单元测试
4. 更新文档

### 插件开发
1. 实现插件接口
2. 编译为动态库
3. 放置到插件目录
4. 重启应用程序

## 📚 文档

- [架构设计文档](docs/Qt客户端整体架构设计.md)
- [API文档](docs/api/)
- [用户手册](docs/user/)
- [开发者指南](docs/developer/)

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
make run_tests

# 运行特定测试套件
ctest -R unit

# 生成覆盖率报告
make coverage
```

### 测试类型
- **单元测试**: 测试单个组件功能
- **集成测试**: 测试组件间交互
- **性能测试**: 测试系统性能指标

## 🐛 问题报告

如果您发现bug或有功能建议，请：

1. 检查[已知问题](https://github.com/your-repo/KKQuickLaunch/issues)
2. 创建新的Issue，包含：
   - 详细的问题描述
   - 重现步骤
   - 系统环境信息
   - 相关日志文件

## 🤝 贡献指南

我们欢迎各种形式的贡献：

1. **代码贡献**
   - Fork项目
   - 创建功能分支
   - 提交Pull Request

2. **文档改进**
   - 修正错误
   - 添加示例
   - 翻译文档

3. **测试和反馈**
   - 报告bug
   - 提出改进建议
   - 参与讨论

## 📄 许可证

本项目采用 [MIT许可证](LICENSE)。

## 👥 团队

- **项目维护者**: [您的名字](mailto:<EMAIL>)
- **核心开发者**: 开发团队
- **贡献者**: [贡献者列表](CONTRIBUTORS.md)

## 🙏 致谢

感谢以下开源项目的支持：
- [Qt Framework](https://www.qt.io/)
- [CMake](https://cmake.org/)
- [Conan](https://conan.io/)

---

**版本**: 1.0.0  
**最后更新**: 2024-12-17  

如有问题，请联系 [<EMAIL>](mailto:<EMAIL>)
