@echo off
REM KK QuickLaunch 构建脚本 (Windows)
REM 使用方法: build.bat [clean|debug|release|test|package]

setlocal enabledelayedexpansion

REM 设置变量
set BUILD_TYPE=Release
set BUILD_DIR=build
set CLEAN_BUILD=0
set RUN_TESTS=0
set CREATE_PACKAGE=0
set PARALLEL_JOBS=4

REM 解析命令行参数
:parse_args
if "%1"=="clean" (
    set CLEAN_BUILD=1
    shift
    goto parse_args
)
if "%1"=="debug" (
    set BUILD_TYPE=Debug
    shift
    goto parse_args
)
if "%1"=="release" (
    set BUILD_TYPE=Release
    shift
    goto parse_args
)
if "%1"=="test" (
    set RUN_TESTS=1
    shift
    goto parse_args
)
if "%1"=="package" (
    set CREATE_PACKAGE=1
    shift
    goto parse_args
)
if not "%1"=="" (
    echo 未知参数: %1
    goto show_usage
)

echo ========================================
echo KK QuickLaunch 构建脚本
echo ========================================
echo 构建类型: %BUILD_TYPE%
echo 构建目录: %BUILD_DIR%
echo 并行任务: %PARALLEL_JOBS%
echo ========================================

REM 检查必要工具
echo 检查构建工具...

where cmake >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到CMake，请安装CMake并添加到PATH
    exit /b 1
)

where qt6-config >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到Qt6，请确保Qt6已安装并添加到PATH
)

where conan >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到Conan，将跳过依赖管理
    set USE_CONAN=0
) else (
    set USE_CONAN=1
)

REM 清理构建目录
if %CLEAN_BUILD%==1 (
    echo 清理构建目录...
    if exist %BUILD_DIR% (
        rmdir /s /q %BUILD_DIR%
    )
)

REM 创建构建目录
if not exist %BUILD_DIR% (
    mkdir %BUILD_DIR%
)

cd %BUILD_DIR%

REM 安装依赖（如果使用Conan）
if %USE_CONAN%==1 (
    echo 安装项目依赖...
    conan install .. --build=missing -s build_type=%BUILD_TYPE%
    if errorlevel 1 (
        echo 错误: Conan依赖安装失败
        exit /b 1
    )
    set CMAKE_TOOLCHAIN=-DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake
) else (
    set CMAKE_TOOLCHAIN=
)

REM 配置CMake
echo 配置CMake...
cmake .. -DCMAKE_BUILD_TYPE=%BUILD_TYPE% %CMAKE_TOOLCHAIN%
if errorlevel 1 (
    echo 错误: CMake配置失败
    exit /b 1
)

REM 编译项目
echo 编译项目...
cmake --build . --config %BUILD_TYPE% --parallel %PARALLEL_JOBS%
if errorlevel 1 (
    echo 错误: 编译失败
    exit /b 1
)

REM 运行测试
if %RUN_TESTS%==1 (
    echo 运行测试...
    ctest --output-on-failure --parallel %PARALLEL_JOBS%
    if errorlevel 1 (
        echo 警告: 部分测试失败
    )
)

REM 创建安装包
if %CREATE_PACKAGE%==1 (
    echo 创建安装包...
    cmake --build . --target package --config %BUILD_TYPE%
    if errorlevel 1 (
        echo 错误: 创建安装包失败
        exit /b 1
    )
)

cd ..

echo ========================================
echo 构建完成！
echo ========================================
echo 可执行文件: %BUILD_DIR%\%BUILD_TYPE%\KKQuickLaunch.exe
if %CREATE_PACKAGE%==1 (
    echo 安装包: %BUILD_DIR%\packages\
)
echo ========================================

goto end

:show_usage
echo 使用方法: %0 [选项]
echo.
echo 选项:
echo   clean     - 清理构建目录
echo   debug     - 调试构建
echo   release   - 发布构建 (默认)
echo   test      - 运行测试
echo   package   - 创建安装包
echo.
echo 示例:
echo   %0 clean release test    - 清理后进行发布构建并运行测试
echo   %0 debug                 - 调试构建
echo   %0 package               - 创建发布安装包

:end
endlocal
