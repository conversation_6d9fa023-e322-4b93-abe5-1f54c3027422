syntax = "v1"

// ============================================================================
// 用户行为相关类型定义
// ============================================================================

// 用户行为记录
type UserAction {
    Id            int64  `json:"id"`             // 行为记录ID
    LaunchItemId  int64  `json:"launch_item_id,optional"` // 关联的启动项ID
    LaunchItemName string `json:"launch_item_name,optional"` // 启动项名称
    ActionType    string `json:"action_type"`    // 操作类型: launch/search/add/delete/edit/view/recommend
    ActionMethod  string `json:"action_method"`  // 操作方式: click/hotkey/search/drag_drop/recommendation
    SearchQuery   string `json:"search_query,optional"` // 搜索关键词
    ContextData   map[string]interface{} `json:"context_data,optional"` // 上下文数据
    ResponseTime  int    `json:"response_time"`  // 响应时间(毫秒)
    Success       bool   `json:"success"`        // 操作是否成功
    ErrorMessage  string `json:"error_message,optional"` // 错误信息
    SessionId     string `json:"session_id"`     // 会话ID
    DeviceId      string `json:"device_id"`      // 设备ID
    UserId        int64  `json:"user_id"`        // 用户ID
    Timestamp     string `json:"timestamp"`      // 操作时间
}

// 用户行为上下文
type ActionContext {
    FileType          string   `json:"file_type,optional"`          // 当前文件类型
    ClipboardContent  string   `json:"clipboard_content,optional"`  // 剪贴板内容(脱敏)
    ActiveWindow      string   `json:"active_window,optional"`      // 活动窗口
    WorkingDirectory  string   `json:"working_directory,optional"`  // 工作目录
    TimeOfDay         string   `json:"time_of_day,optional"`        // 时间段: morning/afternoon/evening/night
    DayOfWeek         int      `json:"day_of_week,optional"`        // 星期几(1-7)
    SearchQuery       string   `json:"search_query,optional"`       // 搜索关键词
    RecommendationId  string   `json:"recommendation_id,optional"`  // 推荐ID
    Position          int      `json:"position,optional"`           // 在结果中的位置
    RecentFiles       []string `json:"recent_files,optional"`       // 最近文件列表
    UserAgent         string   `json:"user_agent,optional"`         // 用户代理
    ScreenResolution  string   `json:"screen_resolution,optional"`  // 屏幕分辨率
    Language          string   `json:"language,optional"`           // 系统语言
    Timezone          string   `json:"timezone,optional"`           // 时区
}

// 用户行为事件
type UserEvent {
    EventType   string        `json:"event_type" validate:"required,oneof=app_launch search_query recommendation_shown recommendation_clicked config_changed hotkey_used plugin_loaded error_occurred"` // 事件类型
    Timestamp   string        `json:"timestamp" validate:"required"`   // 事件时间戳
    Data        interface{}   `json:"data"`                            // 事件数据
    Context     ActionContext `json:"context,optional"`               // 事件上下文
    Metadata    struct {
        Version       string `json:"version,optional"`        // 客户端版本
        Platform      string `json:"platform,optional"`       // 操作系统平台
        Architecture  string `json:"architecture,optional"`   // 系统架构
        Memory        int64  `json:"memory,optional"`          // 系统内存(MB)
        DiskSpace     int64  `json:"disk_space,optional"`      // 磁盘空间(MB)
    } `json:"metadata,optional"` // 元数据
}

// 批量上报用户行为事件请求
type ReportEventsReq {
    DeviceId  string      `json:"device_id" validate:"required"`  // 设备ID
    SessionId string      `json:"session_id" validate:"required"` // 会话ID
    Events    []UserEvent `json:"events" validate:"required,min=1,max=100,dive"` // 事件列表
    BatchId   string      `json:"batch_id,optional"`              // 批次ID
}

// 获取用户行为记录请求
type GetUserActionsReq {
    LaunchItemId int64  `form:"launch_item_id,optional"`  // 启动项ID过滤
    ActionType   string `form:"action_type,optional"`     // 操作类型过滤
    ActionMethod string `form:"action_method,optional"`   // 操作方式过滤
    Success      *bool  `form:"success,optional"`         // 成功状态过滤
    SessionId    string `form:"session_id,optional"`      // 会话ID过滤
    TimeRangeReq
    PageReq
    SortReq
}

// 获取用户行为记录响应
type GetUserActionsResp {
    BaseResp
    Data     []UserAction `json:"data"`      // 用户行为列表
    PageInfo PageInfo     `json:"page_info"` // 分页信息
    Stats    struct {
        Total         int64 `json:"total"`          // 总记录数
        SuccessCount  int64 `json:"success_count"`  // 成功次数
        FailureCount  int64 `json:"failure_count"`  // 失败次数
        AvgResponseTime float64 `json:"avg_response_time"` // 平均响应时间
        UniqueItems   int64 `json:"unique_items"`   // 涉及的唯一启动项数
        UniqueSessions int64 `json:"unique_sessions"` // 唯一会话数
    } `json:"stats"`
}

// 用户行为统计请求
type GetUserBehaviorStatsReq {
    TimeRange   string `form:"time_range,optional"`   // 时间范围: 1h/1d/7d/30d/90d
    GroupBy     string `form:"group_by,optional"`     // 分组方式: hour/day/week/month
    MetricType  string `form:"metric_type,optional"`  // 指标类型: count/response_time/success_rate
    ActionType  string `form:"action_type,optional"`  // 操作类型过滤
    CategoryId  int64  `form:"category_id,optional"`  // 分类过滤
}

// 用户行为统计响应
type GetUserBehaviorStatsResp {
    BaseResp
    Data struct {
        Timeline []TimeSeriesPoint `json:"timeline"`    // 时间线数据
        Summary  struct {
            TotalActions      int64   `json:"total_actions"`       // 总操作次数
            UniqueApps        int64   `json:"unique_apps"`         // 使用的不同应用数
            AvgActionsPerDay  float64 `json:"avg_actions_per_day"` // 日均操作次数
            MostActiveHour    int     `json:"most_active_hour"`    // 最活跃时间
            SuccessRate       float64 `json:"success_rate"`        // 成功率
            AvgResponseTime   float64 `json:"avg_response_time"`   // 平均响应时间
        } `json:"summary"`
        TopApps []struct {
            LaunchItemId   int64  `json:"launch_item_id"`   // 启动项ID
            LaunchItemName string `json:"launch_item_name"` // 启动项名称
            ActionCount    int64  `json:"action_count"`     // 操作次数
            Percentage     float64 `json:"percentage"`      // 占比
        } `json:"top_apps"` // 最常用应用
        ActionTypes []struct {
            ActionType string  `json:"action_type"` // 操作类型
            Count      int64   `json:"count"`       // 次数
            Percentage float64 `json:"percentage"`  // 占比
        } `json:"action_types"` // 操作类型分布
        HourlyDistribution []struct {
            Hour  int   `json:"hour"`  // 小时(0-23)
            Count int64 `json:"count"` // 操作次数
        } `json:"hourly_distribution"` // 小时分布
        WeeklyDistribution []struct {
            DayOfWeek int   `json:"day_of_week"` // 星期几(1-7)
            Count     int64 `json:"count"`       // 操作次数
        } `json:"weekly_distribution"` // 星期分布
    } `json:"data"`
}

// 用户使用模式分析请求
type GetUsagePatternsReq {
    TimeRange string `form:"time_range,optional"` // 时间范围: 7d/30d/90d
    MinUsage  int    `form:"min_usage,optional"`  // 最小使用次数阈值
}

// 用户使用模式分析响应
type GetUsagePatternsResp {
    BaseResp
    Data struct {
        Patterns []struct {
            Name        string  `json:"name"`         // 模式名称
            Description string  `json:"description"`  // 模式描述
            Confidence  float64 `json:"confidence"`   // 置信度
            Examples    []struct {
                Time        string `json:"time"`        // 时间
                Action      string `json:"action"`      // 操作
                Application string `json:"application"` // 应用
            } `json:"examples"` // 示例
        } `json:"patterns"` // 使用模式
        WorkingHours struct {
            Start     string  `json:"start"`      // 工作开始时间
            End       string  `json:"end"`        // 工作结束时间
            Intensity float64 `json:"intensity"`  // 工作强度
            MainApps  []string `json:"main_apps"` // 主要应用
        } `json:"working_hours"` // 工作时间分析
        Preferences struct {
            PreferredLaunchMethod string   `json:"preferred_launch_method"` // 偏好的启动方式
            MostUsedCategories    []string `json:"most_used_categories"`    // 最常用分类
            AverageSessionLength  int      `json:"average_session_length"`  // 平均会话长度(分钟)
            MultitaskingLevel     string   `json:"multitasking_level"`      // 多任务处理水平: low/medium/high
        } `json:"preferences"` // 用户偏好
    } `json:"data"`
}

// 会话分析请求
type GetSessionAnalysisReq {
    SessionId string `form:"session_id,optional"` // 特定会话ID
    TimeRange string `form:"time_range,optional"` // 时间范围
    MinDuration int  `form:"min_duration,optional"` // 最小会话时长(分钟)
}

// 会话分析响应
type GetSessionAnalysisResp {
    BaseResp
    Data struct {
        Sessions []struct {
            SessionId     string `json:"session_id"`      // 会话ID
            StartTime     string `json:"start_time"`      // 开始时间
            EndTime       string `json:"end_time"`        // 结束时间
            Duration      int    `json:"duration"`        // 持续时间(分钟)
            ActionCount   int64  `json:"action_count"`    // 操作次数
            UniqueApps    int    `json:"unique_apps"`     // 使用的不同应用数
            MainCategory  string `json:"main_category"`   // 主要分类
            Productivity  float64 `json:"productivity"`   // 生产力指数
            Actions       []struct {
                Timestamp    string `json:"timestamp"`     // 时间戳
                ActionType   string `json:"action_type"`   // 操作类型
                Application  string `json:"application"`   // 应用名称
                ResponseTime int    `json:"response_time"` // 响应时间
            } `json:"actions"` // 操作序列
        } `json:"sessions"` // 会话列表
        Summary struct {
            TotalSessions     int     `json:"total_sessions"`      // 总会话数
            AvgSessionLength  float64 `json:"avg_session_length"`  // 平均会话长度
            LongestSession    int     `json:"longest_session"`     // 最长会话时长
            ShortestSession   int     `json:"shortest_session"`    // 最短会话时长
            TotalActiveTime   int     `json:"total_active_time"`   // 总活跃时间
            AvgActionsPerSession float64 `json:"avg_actions_per_session"` // 平均每会话操作数
        } `json:"summary"`
    } `json:"data"`
}

// 错误分析请求
type GetErrorAnalysisReq {
    TimeRange   string `form:"time_range,optional"`   // 时间范围
    ErrorType   string `form:"error_type,optional"`   // 错误类型过滤
    LaunchItemId int64 `form:"launch_item_id,optional"` // 启动项过滤
    GroupBy     string `form:"group_by,optional"`     // 分组方式: error_type/launch_item/time
}

// 错误分析响应
type GetErrorAnalysisResp {
    BaseResp
    Data struct {
        Errors []struct {
            ErrorType     string `json:"error_type"`      // 错误类型
            ErrorMessage  string `json:"error_message"`   // 错误消息
            Count         int64  `json:"count"`           // 出现次数
            LastOccurred  string `json:"last_occurred"`   // 最后出现时间
            AffectedApps  []string `json:"affected_apps"` // 受影响的应用
            Severity      string `json:"severity"`        // 严重程度: low/medium/high/critical
        } `json:"errors"` // 错误列表
        Timeline []struct {
            Timestamp  string `json:"timestamp"`   // 时间戳
            ErrorCount int64  `json:"error_count"` // 错误数量
        } `json:"timeline"` // 错误时间线
        Summary struct {
            TotalErrors    int64   `json:"total_errors"`     // 总错误数
            ErrorRate      float64 `json:"error_rate"`       // 错误率
            MostCommonError string `json:"most_common_error"` // 最常见错误
            ErrorTrend     string  `json:"error_trend"`      // 错误趋势: increasing/decreasing/stable
        } `json:"summary"`
    } `json:"data"`
}

// 清理用户行为数据请求
type CleanupUserActionsReq {
    OlderThan   string `json:"older_than" validate:"required"`     // 清理多久之前的数据: 30d/90d/1y
    ActionTypes []string `json:"action_types,optional"`            // 要清理的操作类型
    KeepErrors  bool   `json:"keep_errors,optional"`               // 是否保留错误记录
    DryRun      bool   `json:"dry_run,optional"`                   // 是否仅模拟运行
}

// 清理用户行为数据响应
type CleanupUserActionsResp {
    BaseResp
    Data struct {
        RecordsToDelete int64 `json:"records_to_delete"` // 将要删除的记录数
        RecordsDeleted  int64 `json:"records_deleted"`   // 已删除的记录数
        SpaceFreed      int64 `json:"space_freed"`       // 释放的空间(字节)
        Categories      []struct {
            ActionType string `json:"action_type"` // 操作类型
            Count      int64  `json:"count"`       // 删除数量
        } `json:"categories"` // 按类型分类的删除统计
    } `json:"data"`
}
