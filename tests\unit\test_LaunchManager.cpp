#include <QtTest>
#include <QSignalSpy>
#include <QTemporaryDir>
#include <QStandardPaths>
#include <memory>

#include "business/managers/LaunchManager.h"
#include "data/repositories/LaunchItemRepository.h"
#include "infrastructure/database/DatabaseManager.h"
#include "business/entities/LaunchItem.h"

class TestLaunchManager : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();
    
    // 基本功能测试
    void testInitialization();
    void testAddItem();
    void testUpdateItem();
    void testRemoveItem();
    void testGetItem();
    
    // 批量操作测试
    void testBatchOperations();
    
    // 启动功能测试
    void testLaunchItem();
    void testLaunchValidation();
    
    // 搜索和过滤测试
    void testItemFiltering();
    
    // 统计功能测试
    void testStatistics();
    
    // 文件系统监控测试
    void testFileSystemWatching();

private:
    void createTestItems();
    LaunchItem createTestItem(const QString &name, const QString &path, LaunchItemType type = LaunchItemType::Application);
    
private:
    std::unique_ptr<LaunchManager> m_launchManager;
    std::unique_ptr<LaunchItemRepository> m_repository;
    std::unique_ptr<DatabaseManager> m_databaseManager;
    QTemporaryDir m_tempDir;
    QList<LaunchItem> m_testItems;
};

void TestLaunchManager::initTestCase()
{
    // 设置测试环境
    QStandardPaths::setTestModeEnabled(true);
    
    // 创建临时目录
    QVERIFY(m_tempDir.isValid());
    
    // 初始化数据库管理器
    m_databaseManager = std::make_unique<DatabaseManager>();
    QString dbPath = m_tempDir.filePath("test.db");
    QVERIFY(m_databaseManager->initialize(dbPath));
    
    // 初始化仓库
    m_repository = std::make_unique<LaunchItemRepository>(m_databaseManager);
    
    // 初始化启动管理器
    m_launchManager = std::make_unique<LaunchManager>();
    // 注意：在实际实现中，应该通过依赖注入设置repository
    
    QVERIFY(m_launchManager->initialize());
    
    qDebug() << "Test case initialized";
}

void TestLaunchManager::cleanupTestCase()
{
    m_launchManager.reset();
    m_repository.reset();
    m_databaseManager.reset();
    
    qDebug() << "Test case cleaned up";
}

void TestLaunchManager::init()
{
    // 每个测试前的初始化
    createTestItems();
}

void TestLaunchManager::cleanup()
{
    // 每个测试后的清理
    m_testItems.clear();
    
    // 清理数据库
    if (m_databaseManager) {
        m_databaseManager->executeNonQuery("DELETE FROM launch_items");
    }
}

void TestLaunchManager::testInitialization()
{
    QVERIFY(m_launchManager != nullptr);
    
    // 测试初始状态
    QList<LaunchItem> items = m_launchManager->getAllItems();
    QVERIFY(items.isEmpty());
    
    LaunchStats stats = m_launchManager->getStatistics();
    QCOMPARE(stats.totalLaunches, 0);
    QCOMPARE(stats.successfulLaunches, 0);
    QCOMPARE(stats.failedLaunches, 0);
}

void TestLaunchManager::testAddItem()
{
    LaunchItem item = createTestItem("Test App", "/path/to/test.exe");
    
    // 测试添加项目
    bool success = m_launchManager->addItem(item);
    QVERIFY(success);
    QVERIFY(item.id > 0);
    
    // 验证项目已添加
    LaunchItem retrieved = m_launchManager->getItem(item.id);
    QVERIFY(retrieved.isValid());
    QCOMPARE(retrieved.name, item.name);
    QCOMPARE(retrieved.path, item.path);
    
    // 测试获取所有项目
    QList<LaunchItem> allItems = m_launchManager->getAllItems();
    QCOMPARE(allItems.size(), 1);
    QCOMPARE(allItems.first().id, item.id);
}

void TestLaunchManager::testUpdateItem()
{
    // 先添加一个项目
    LaunchItem item = createTestItem("Original Name", "/path/to/original.exe");
    QVERIFY(m_launchManager->addItem(item));
    
    // 修改项目
    item.name = "Updated Name";
    item.description = "Updated description";
    item.useCount = 5;
    
    bool success = m_launchManager->updateItem(item);
    QVERIFY(success);
    
    // 验证更新
    LaunchItem updated = m_launchManager->getItem(item.id);
    QCOMPARE(updated.name, QString("Updated Name"));
    QCOMPARE(updated.description, QString("Updated description"));
    QCOMPARE(updated.useCount, 5);
}

void TestLaunchManager::testRemoveItem()
{
    // 添加项目
    LaunchItem item = createTestItem("To Remove", "/path/to/remove.exe");
    QVERIFY(m_launchManager->addItem(item));
    int itemId = item.id;
    
    // 验证项目存在
    QVERIFY(m_launchManager->getItem(itemId).isValid());
    
    // 删除项目
    bool success = m_launchManager->removeItem(itemId);
    QVERIFY(success);
    
    // 验证项目已删除
    LaunchItem removed = m_launchManager->getItem(itemId);
    QVERIFY(!removed.isValid());
    
    // 验证列表为空
    QList<LaunchItem> allItems = m_launchManager->getAllItems();
    QVERIFY(allItems.isEmpty());
}

void TestLaunchManager::testGetItem()
{
    // 测试获取不存在的项目
    LaunchItem nonExistent = m_launchManager->getItem(999);
    QVERIFY(!nonExistent.isValid());
    
    // 添加项目并测试获取
    LaunchItem item = createTestItem("Get Test", "/path/to/get.exe");
    QVERIFY(m_launchManager->addItem(item));
    
    LaunchItem retrieved = m_launchManager->getItem(item.id);
    QVERIFY(retrieved.isValid());
    QCOMPARE(retrieved.id, item.id);
    QCOMPARE(retrieved.name, item.name);
    
    // 测试通过路径获取
    LaunchItem byPath = m_launchManager->getItemByPath(item.path);
    QVERIFY(byPath.isValid());
    QCOMPARE(byPath.id, item.id);
}

void TestLaunchManager::testBatchOperations()
{
    // 准备测试数据
    QList<LaunchItem> items;
    for (int i = 0; i < 5; ++i) {
        items.append(createTestItem(QString("Batch Item %1").arg(i), 
                                   QString("/path/to/batch%1.exe").arg(i)));
    }
    
    // 测试批量添加
    int addedCount = m_launchManager->addItems(items);
    QCOMPARE(addedCount, 5);
    
    // 验证所有项目都有有效ID
    for (const LaunchItem &item : items) {
        QVERIFY(item.id > 0);
    }
    
    // 验证总数
    QList<LaunchItem> allItems = m_launchManager->getAllItems();
    QCOMPARE(allItems.size(), 5);
    
    // 测试批量删除
    QList<int> idsToRemove;
    for (int i = 0; i < 3; ++i) {
        idsToRemove.append(items[i].id);
    }
    
    int removedCount = m_launchManager->removeItems(idsToRemove);
    QCOMPARE(removedCount, 3);
    
    // 验证剩余项目数
    allItems = m_launchManager->getAllItems();
    QCOMPARE(allItems.size(), 2);
}

void TestLaunchManager::testLaunchItem()
{
    // 创建一个可执行的测试项目（使用系统命令）
    LaunchItem item = createTestItem("Echo Test", "echo", LaunchItemType::Command);
    QVERIFY(m_launchManager->addItem(item));
    
    // 测试启动
    LaunchOptions options;
    options.arguments = "Hello World";
    options.detached = true; // 分离模式避免阻塞测试
    
    LaunchResult result = m_launchManager->launchItem(item.id, options);
    QCOMPARE(result, LaunchResult::Success);
    
    // 验证统计信息更新
    LaunchStats stats = m_launchManager->getStatistics();
    QCOMPARE(stats.totalLaunches, 1);
    QCOMPARE(stats.successfulLaunches, 1);
}

void TestLaunchManager::testLaunchValidation()
{
    // 测试启动不存在的项目
    LaunchResult result = m_launchManager->launchItem(999);
    QCOMPARE(result, LaunchResult::FileNotFound);
    
    // 测试启动禁用的项目
    LaunchItem disabledItem = createTestItem("Disabled", "/path/to/disabled.exe");
    disabledItem.isEnabled = false;
    QVERIFY(m_launchManager->addItem(disabledItem));
    
    result = m_launchManager->launchItem(disabledItem.id);
    QCOMPARE(result, LaunchResult::Cancelled);
}

void TestLaunchManager::testItemFiltering()
{
    // 添加不同类型的项目
    LaunchItem app1 = createTestItem("App 1", "/path/to/app1.exe", LaunchItemType::Application);
    LaunchItem app2 = createTestItem("App 2", "/path/to/app2.exe", LaunchItemType::Application);
    LaunchItem doc1 = createTestItem("Doc 1", "/path/to/doc1.pdf", LaunchItemType::Document);
    
    app1.categoryId = 1;
    app2.categoryId = 2;
    doc1.categoryId = 1;
    
    QVERIFY(m_launchManager->addItem(app1));
    QVERIFY(m_launchManager->addItem(app2));
    QVERIFY(m_launchManager->addItem(doc1));
    
    // 测试按分类过滤
    QList<LaunchItem> category1Items = m_launchManager->getItemsByCategory(1);
    QCOMPARE(category1Items.size(), 2);
    
    QList<LaunchItem> category2Items = m_launchManager->getItemsByCategory(2);
    QCOMPARE(category2Items.size(), 1);
}

void TestLaunchManager::testStatistics()
{
    // 初始统计应该为空
    LaunchStats stats = m_launchManager->getStatistics();
    QCOMPARE(stats.totalLaunches, 0);
    
    // 添加项目并记录使用
    LaunchItem item = createTestItem("Stats Test", "echo", LaunchItemType::Command);
    QVERIFY(m_launchManager->addItem(item));
    
    // 模拟使用记录
    m_launchManager->recordUsage(item.id, "test", 100, true);
    
    // 验证项目使用统计
    LaunchItem updated = m_launchManager->getItem(item.id);
    QCOMPARE(updated.useCount, 1);
    QVERIFY(updated.lastUsed.isValid());
}

void TestLaunchManager::testFileSystemWatching()
{
    // 测试文件系统监控开关
    QVERIFY(m_launchManager->isFileSystemWatchingEnabled());
    
    m_launchManager->setFileSystemWatchingEnabled(false);
    QVERIFY(!m_launchManager->isFileSystemWatchingEnabled());
    
    m_launchManager->setFileSystemWatchingEnabled(true);
    QVERIFY(m_launchManager->isFileSystemWatchingEnabled());
}

void TestLaunchManager::createTestItems()
{
    m_testItems.clear();
    
    m_testItems.append(createTestItem("Test App 1", "/path/to/app1.exe"));
    m_testItems.append(createTestItem("Test App 2", "/path/to/app2.exe"));
    m_testItems.append(createTestItem("Test Doc", "/path/to/doc.pdf", LaunchItemType::Document));
    m_testItems.append(createTestItem("Test Folder", "/path/to/folder", LaunchItemType::Folder));
    m_testItems.append(createTestItem("Test URL", "https://example.com", LaunchItemType::Url));
}

LaunchItem TestLaunchManager::createTestItem(const QString &name, const QString &path, LaunchItemType type)
{
    LaunchItem item;
    item.name = name;
    item.path = path;
    item.type = type;
    item.description = QString("Test description for %1").arg(name);
    item.isEnabled = true;
    item.isVisible = true;
    item.categoryId = 1;
    item.priority = 0;
    item.useCount = 0;
    item.rating = 0.0;
    item.source = "test";
    
    return item;
}

QTEST_MAIN(TestLaunchManager)
#include "test_LaunchManager.moc"
