# 智能启动助手 - Qt客户端整体架构设计

## 📋 项目概述

**项目名称**: KK QuickLaunch Qt Client  
**技术栈**: Qt 6.5+ / C++17/20 / SQLite / QML  
**目标平台**: Windows 10+, Linux, macOS  
**架构模式**: MVVM + 分层架构 + 插件系统  

## 🏗️ 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    表示层 (Presentation Layer)                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   主界面    │  │   设置界面  │  │   统计界面  │         │
│  │ MainWindow  │  │ SettingsUI  │  │ StatsWindow │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   搜索界面  │  │   托盘菜单  │  │   插件界面  │         │
│  │ SearchWidget│  │ TrayMenu    │  │ PluginUI    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    视图模型层 (ViewModel Layer)               │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │LaunchItemVM │  │ CategoryVM  │  │SettingsVM   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ SearchVM    │  │RecommendVM  │  │AnalyticsVM  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Business Logic Layer)          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │LaunchManager│  │SearchEngine │  │RecommendSys │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ConfigManager│  │PluginManager│  │HotkeyManager│         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    服务层 (Service Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │DatabaseSvc  │  │ NetworkSvc  │  │ FileService │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │SystemService│  │LoggingService│  │CacheService │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (Data Access Layer)            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │LaunchItemDAO│  │ CategoryDAO │  │SettingsDAO  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │UserActionDAO│  │PluginDAO    │  │AnalyticsDAO │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    基础设施层 (Infrastructure Layer)          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   SQLite    │  │   文件系统  │  │   网络通信  │         │
│  │  Database   │  │FileSystem   │  │  Network    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心设计原则

### 1. MVVM架构模式
- **Model**: 数据模型和业务逻辑
- **View**: 用户界面(Qt Widgets/QML)
- **ViewModel**: 视图逻辑和数据绑定

### 2. 分层架构
- **表示层**: 负责用户交互和界面展示
- **业务逻辑层**: 核心业务逻辑处理
- **数据访问层**: 数据库操作和数据持久化
- **基础设施层**: 系统服务和第三方集成

### 3. 依赖注入
- 使用依赖注入容器管理对象生命周期
- 降低组件间耦合度
- 便于单元测试和模块替换

### 4. 插件化架构
- 支持动态加载插件
- 标准化插件接口
- 热插拔功能扩展

## 📁 项目目录结构

```
KKQuickLaunch/
├── src/                           # 源代码目录
│   ├── main.cpp                   # 程序入口
│   ├── application/               # 应用程序层
│   │   ├── Application.h/cpp      # 应用程序主类
│   │   ├── ServiceContainer.h/cpp # 依赖注入容器
│   │   └── AppConfig.h/cpp        # 应用配置
│   ├── presentation/              # 表示层
│   │   ├── views/                 # 视图组件
│   │   │   ├── MainWindow.h/cpp   # 主窗口
│   │   │   ├── SearchWidget.h/cpp # 搜索组件
│   │   │   ├── SettingsDialog.h/cpp # 设置对话框
│   │   │   ├── TrayIcon.h/cpp     # 系统托盘
│   │   │   └── StatsWindow.h/cpp  # 统计窗口
│   │   ├── viewmodels/            # 视图模型
│   │   │   ├── LaunchItemViewModel.h/cpp
│   │   │   ├── CategoryViewModel.h/cpp
│   │   │   ├── SearchViewModel.h/cpp
│   │   │   ├── SettingsViewModel.h/cpp
│   │   │   └── RecommendationViewModel.h/cpp
│   │   ├── models/                # 数据模型
│   │   │   ├── LaunchItemModel.h/cpp
│   │   │   ├── CategoryModel.h/cpp
│   │   │   └── SearchResultModel.h/cpp
│   │   └── delegates/             # 自定义委托
│   │       ├── LaunchItemDelegate.h/cpp
│   │       └── CategoryDelegate.h/cpp
│   ├── business/                  # 业务逻辑层
│   │   ├── managers/              # 业务管理器
│   │   │   ├── LaunchManager.h/cpp      # 启动项管理
│   │   │   ├── SearchEngine.h/cpp       # 搜索引擎
│   │   │   ├── RecommendationSystem.h/cpp # 推荐系统
│   │   │   ├── ConfigManager.h/cpp      # 配置管理
│   │   │   ├── PluginManager.h/cpp      # 插件管理
│   │   │   ├── HotkeyManager.h/cpp      # 热键管理
│   │   │   └── SyncManager.h/cpp        # 同步管理
│   │   ├── entities/              # 业务实体
│   │   │   ├── LaunchItem.h/cpp         # 启动项实体
│   │   │   ├── Category.h/cpp           # 分类实体
│   │   │   ├── UserAction.h/cpp         # 用户行为实体
│   │   │   ├── RecommendationRule.h/cpp # 推荐规则实体
│   │   │   └── AppSettings.h/cpp        # 应用设置实体
│   │   └── algorithms/            # 算法实现
│   │       ├── SearchAlgorithm.h/cpp    # 搜索算法
│   │       ├── RecommendAlgorithm.h/cpp # 推荐算法
│   │       └── AnalyticsAlgorithm.h/cpp # 分析算法
│   ├── services/                  # 服务层
│   │   ├── DatabaseService.h/cpp        # 数据库服务
│   │   ├── NetworkService.h/cpp         # 网络服务
│   │   ├── FileService.h/cpp            # 文件服务
│   │   ├── SystemService.h/cpp          # 系统服务
│   │   ├── LoggingService.h/cpp         # 日志服务
│   │   ├── CacheService.h/cpp           # 缓存服务
│   │   └── NotificationService.h/cpp    # 通知服务
│   ├── data/                      # 数据访问层
│   │   ├── repositories/          # 仓储模式
│   │   │   ├── LaunchItemRepository.h/cpp
│   │   │   ├── CategoryRepository.h/cpp
│   │   │   ├── UserActionRepository.h/cpp
│   │   │   ├── SettingsRepository.h/cpp
│   │   │   └── PluginRepository.h/cpp
│   │   ├── dao/                   # 数据访问对象
│   │   │   ├── BaseDAO.h/cpp            # 基础DAO
│   │   │   ├── LaunchItemDAO.h/cpp      # 启动项DAO
│   │   │   ├── CategoryDAO.h/cpp        # 分类DAO
│   │   │   ├── UserActionDAO.h/cpp      # 用户行为DAO
│   │   │   └── SettingsDAO.h/cpp        # 设置DAO
│   │   └── migrations/            # 数据库迁移
│   │       ├── MigrationManager.h/cpp   # 迁移管理器
│   │       └── migrations/              # 迁移脚本
│   ├── infrastructure/            # 基础设施层
│   │   ├── database/              # 数据库相关
│   │   │   ├── DatabaseManager.h/cpp    # 数据库管理器
│   │   │   ├── ConnectionPool.h/cpp     # 连接池
│   │   │   └── QueryBuilder.h/cpp       # 查询构建器
│   │   ├── network/               # 网络相关
│   │   │   ├── HttpClient.h/cpp         # HTTP客户端
│   │   │   ├── WebSocketClient.h/cpp    # WebSocket客户端
│   │   │   └── ApiClient.h/cpp          # API客户端
│   │   ├── system/                # 系统相关
│   │   │   ├── SystemInfo.h/cpp         # 系统信息
│   │   │   ├── ProcessManager.h/cpp     # 进程管理
│   │   │   ├── FileWatcher.h/cpp        # 文件监控
│   │   │   └── ClipboardMonitor.h/cpp   # 剪贴板监控
│   │   ├── security/              # 安全相关
│   │   │   ├── Encryption.h/cpp         # 加密解密
│   │   │   ├── Authentication.h/cpp     # 身份认证
│   │   │   └── SecureStorage.h/cpp      # 安全存储
│   │   └── utils/                 # 工具类
│   │       ├── Logger.h/cpp             # 日志工具
│   │       ├── Config.h/cpp             # 配置工具
│   │       ├── StringUtils.h/cpp        # 字符串工具
│   │       ├── FileUtils.h/cpp          # 文件工具
│   │       └── JsonUtils.h/cpp          # JSON工具
│   └── plugins/                   # 插件系统
│       ├── interfaces/            # 插件接口
│       │   ├── IPlugin.h                # 插件基础接口
│       │   ├── ILaunchPlugin.h          # 启动插件接口
│       │   ├── ISearchPlugin.h          # 搜索插件接口
│       │   └── IRecommendPlugin.h       # 推荐插件接口
│       ├── core/                  # 插件核心
│       │   ├── PluginLoader.h/cpp       # 插件加载器
│       │   ├── PluginRegistry.h/cpp     # 插件注册表
│       │   └── PluginContext.h/cpp      # 插件上下文
│       └── builtin/               # 内置插件
│           ├── JsonPlugin/              # JSON处理插件
│           ├── ImagePlugin/             # 图片处理插件
│           └── WebSearchPlugin/         # 网页搜索插件
├── resources/                     # 资源文件
│   ├── icons/                     # 图标资源
│   ├── themes/                    # 主题资源
│   ├── translations/              # 翻译文件
│   ├── sql/                       # SQL脚本
│   └── config/                    # 配置文件
├── tests/                         # 测试代码
│   ├── unit/                      # 单元测试
│   ├── integration/               # 集成测试
│   └── performance/               # 性能测试
├── docs/                          # 文档
│   ├── api/                       # API文档
│   ├── design/                    # 设计文档
│   └── user/                      # 用户文档
├── tools/                         # 工具脚本
│   ├── build/                     # 构建脚本
│   ├── deploy/                    # 部署脚本
│   └── migration/                 # 迁移工具
├── CMakeLists.txt                 # CMake构建文件
├── conanfile.txt                  # Conan依赖文件
├── .gitignore                     # Git忽略文件
└── README.md                      # 项目说明
```

## 🔧 核心组件设计

### 1. 应用程序主类 (Application)

```cpp
class Application : public QApplication {
    Q_OBJECT

public:
    explicit Application(int &argc, char **argv);
    ~Application();

    // 初始化应用程序
    bool initialize();
    
    // 获取服务容器
    ServiceContainer* serviceContainer() const;
    
    // 获取配置管理器
    ConfigManager* configManager() const;
    
    // 单例访问
    static Application* instance();

private slots:
    void onSystemShutdown();
    void onSessionChanged();

private:
    void setupLogging();           // 设置日志系统
    void setupDatabase();          // 设置数据库
    void setupServices();          // 设置服务
    void setupHotkeys();           // 设置全局热键
    void setupTrayIcon();          // 设置系统托盘
    void loadPlugins();            // 加载插件
    void startBackgroundTasks();   // 启动后台任务

private:
    std::unique_ptr<ServiceContainer> m_serviceContainer;
    std::unique_ptr<ConfigManager> m_configManager;
    std::unique_ptr<DatabaseManager> m_databaseManager;
    std::unique_ptr<PluginManager> m_pluginManager;
    std::unique_ptr<HotkeyManager> m_hotkeyManager;
    std::unique_ptr<TrayIcon> m_trayIcon;
    std::unique_ptr<MainWindow> m_mainWindow;
    
    bool m_initialized = false;
    static Application* s_instance;
};
```

### 2. 服务容器 (ServiceContainer)

```cpp
class ServiceContainer {
public:
    ServiceContainer();
    ~ServiceContainer();

    // 注册服务
    template<typename Interface, typename Implementation>
    void registerService();
    
    template<typename Interface>
    void registerService(std::shared_ptr<Interface> instance);

    // 获取服务
    template<typename Interface>
    std::shared_ptr<Interface> getService();

    // 检查服务是否已注册
    template<typename Interface>
    bool hasService() const;

    // 初始化所有服务
    void initializeServices();

private:
    std::unordered_map<std::type_index, std::shared_ptr<void>> m_services;
    std::unordered_map<std::type_index, std::function<std::shared_ptr<void>()>> m_factories;
    mutable std::mutex m_mutex;
};
```

### 3. 主窗口 (MainWindow)

```cpp
class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    // 显示/隐藏窗口
    void showWindow();
    void hideWindow();
    void toggleWindow();

    // 设置搜索焦点
    void focusSearch();

protected:
    void closeEvent(QCloseEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void changeEvent(QEvent *event) override;
    bool eventFilter(QObject *obj, QEvent *event) override;

private slots:
    void onSearchTextChanged(const QString &text);
    void onItemActivated(const QModelIndex &index);
    void onCategoryChanged(int categoryId);
    void onSettingsRequested();
    void onStatsRequested();

private:
    void setupUI();                // 设置用户界面
    void setupConnections();       // 设置信号连接
    void setupShortcuts();         // 设置快捷键
    void loadSettings();           // 加载设置
    void saveSettings();           // 保存设置
    void updateTheme();            // 更新主题
    void updateLayout();           // 更新布局

private:
    Ui::MainWindow *ui;
    
    // 视图模型
    std::unique_ptr<LaunchItemViewModel> m_launchItemViewModel;
    std::unique_ptr<CategoryViewModel> m_categoryViewModel;
    std::unique_ptr<SearchViewModel> m_searchViewModel;
    
    // 子窗口
    std::unique_ptr<SettingsDialog> m_settingsDialog;
    std::unique_ptr<StatsWindow> m_statsWindow;
    
    // 服务引用
    std::shared_ptr<LaunchManager> m_launchManager;
    std::shared_ptr<SearchEngine> m_searchEngine;
    std::shared_ptr<ConfigManager> m_configManager;
    
    // 状态变量
    bool m_isVisible = false;
    QPoint m_lastPosition;
    QSize m_lastSize;
};
```

### 4. 启动项管理器 (LaunchManager)

```cpp
class LaunchManager : public QObject {
    Q_OBJECT

public:
    explicit LaunchManager(QObject *parent = nullptr);
    ~LaunchManager();

    // 启动项操作
    QList<LaunchItem> getAllItems() const;
    QList<LaunchItem> getItemsByCategory(int categoryId) const;
    LaunchItem getItem(int id) const;
    bool addItem(const LaunchItem &item);
    bool updateItem(const LaunchItem &item);
    bool removeItem(int id);
    
    // 批量操作
    bool addItems(const QList<LaunchItem> &items);
    bool removeItems(const QList<int> &ids);
    
    // 启动应用程序
    bool launchItem(int id, const QString &arguments = QString());
    bool launchItemByPath(const QString &path, const QString &arguments = QString());
    
    // 统计信息
    void recordUsage(int id, const QString &method, int responseTime);
    QList<LaunchItem> getMostUsedItems(int limit = 10) const;
    QList<LaunchItem> getRecentlyUsedItems(int limit = 10) const;
    
    // 验证和清理
    QList<int> validateItems() const;      // 返回无效项目的ID
    void cleanupInvalidItems();
    
    // 导入导出
    bool importItems(const QString &filePath, const QString &format);
    bool exportItems(const QString &filePath, const QString &format, const QList<int> &ids = {});

signals:
    void itemAdded(const LaunchItem &item);
    void itemUpdated(const LaunchItem &item);
    void itemRemoved(int id);
    void itemLaunched(int id, bool success);
    void usageRecorded(int id);

private slots:
    void onFileSystemChanged(const QString &path);

private:
    void setupFileWatcher();
    void updateItemInfo(LaunchItem &item);
    bool isValidExecutable(const QString &path) const;
    QIcon extractIcon(const QString &path) const;
    QString getFileVersion(const QString &path) const;
    qint64 getFileSize(const QString &path) const;

private:
    std::shared_ptr<LaunchItemRepository> m_repository;
    std::shared_ptr<UserActionRepository> m_actionRepository;
    std::shared_ptr<FileService> m_fileService;
    std::shared_ptr<SystemService> m_systemService;
    std::shared_ptr<LoggingService> m_logger;
    
    std::unique_ptr<QFileSystemWatcher> m_fileWatcher;
    QTimer *m_validationTimer;
    
    mutable QReadWriteLock m_lock;
};
```

### 5. 搜索引擎 (SearchEngine)

```cpp
class SearchEngine : public QObject {
    Q_OBJECT

public:
    explicit SearchEngine(QObject *parent = nullptr);
    ~SearchEngine();

    // 搜索配置
    struct SearchConfig {
        int maxResults = 20;           // 最大结果数
        bool fuzzySearch = true;       // 模糊搜索
        bool highlightResults = true;  // 高亮结果
        double minScore = 0.1;         // 最小相关性分数
        QStringList searchFields = {"name", "description", "tags"}; // 搜索字段
    };

    // 搜索结果
    struct SearchResult {
        LaunchItem item;               // 启动项
        double score;                  // 相关性分数
        QStringList highlights;        // 高亮片段
        QString matchReason;           // 匹配原因
    };

    // 执行搜索
    QList<SearchResult> search(const QString &query, const SearchConfig &config = {}) const;

    // 异步搜索
    void searchAsync(const QString &query, const SearchConfig &config = {});

    // 搜索建议
    QStringList getSuggestions(const QString &prefix, int maxCount = 10) const;

    // 搜索历史
    void addToHistory(const QString &query, int resultCount, int selectedId = -1);
    QStringList getSearchHistory(int maxCount = 20) const;
    void clearSearchHistory();

    // 索引管理
    void rebuildIndex();
    void updateIndex(const LaunchItem &item);
    void removeFromIndex(int itemId);

    // 配置管理
    void setSearchConfig(const SearchConfig &config);
    SearchConfig getSearchConfig() const;

signals:
    void searchCompleted(const QList<SearchResult> &results);
    void indexUpdated();
    void suggestionReady(const QStringList &suggestions);

private slots:
    void onItemsChanged();
    void onConfigChanged();

private:
    // 搜索算法
    QList<SearchResult> performExactSearch(const QString &query) const;
    QList<SearchResult> performFuzzySearch(const QString &query) const;
    QList<SearchResult> performSemanticSearch(const QString &query) const;

    // 评分算法
    double calculateScore(const LaunchItem &item, const QString &query) const;
    double calculateNameScore(const QString &name, const QString &query) const;
    double calculateDescriptionScore(const QString &description, const QString &query) const;
    double calculateTagScore(const QStringList &tags, const QString &query) const;
    double calculateUsageScore(const LaunchItem &item) const;

    // 索引相关
    void buildSearchIndex();
    void updateSearchIndex();

    // 工具方法
    QStringList tokenize(const QString &text) const;
    QString normalizeText(const QString &text) const;
    QStringList generateHighlights(const QString &text, const QString &query) const;

private:
    std::shared_ptr<LaunchItemRepository> m_repository;
    std::shared_ptr<UserActionRepository> m_actionRepository;
    std::shared_ptr<CacheService> m_cache;
    std::shared_ptr<LoggingService> m_logger;

    SearchConfig m_config;
    QHash<int, LaunchItem> m_searchIndex;      // 搜索索引
    QHash<QString, QList<int>> m_tokenIndex;   // 词汇索引
    QStringList m_searchHistory;               // 搜索历史

    mutable QReadWriteLock m_indexLock;
    QTimer *m_indexUpdateTimer;
    QFutureWatcher<QList<SearchResult>> *m_searchWatcher;
};
```

### 6. 推荐系统 (RecommendationSystem)

```cpp
class RecommendationSystem : public QObject {
    Q_OBJECT

public:
    explicit RecommendationSystem(QObject *parent = nullptr);
    ~RecommendationSystem();

    // 推荐上下文
    struct RecommendationContext {
        QString currentFileType;       // 当前文件类型
        QString clipboardContent;     // 剪贴板内容
        QString activeWindow;         // 活动窗口
        QString workingDirectory;     // 工作目录
        QTime currentTime;            // 当前时间
        QStringList recentFiles;      // 最近文件
        QHash<QString, QVariant> customData; // 自定义数据
    };

    // 推荐结果
    struct Recommendation {
        LaunchItem item;              // 推荐的启动项
        double score;                 // 推荐分数
        double confidence;            // 置信度
        QStringList reasons;          // 推荐理由
        QString algorithm;            // 推荐算法
    };

    // 获取推荐
    QList<Recommendation> getRecommendations(const RecommendationContext &context, int maxCount = 5) const;

    // 异步获取推荐
    void getRecommendationsAsync(const RecommendationContext &context, int maxCount = 5);

    // 记录推荐反馈
    void recordFeedback(int itemId, const QString &action, double rating = -1);

    // 推荐规则管理
    bool addRule(const RecommendationRule &rule);
    bool updateRule(const RecommendationRule &rule);
    bool removeRule(int ruleId);
    QList<RecommendationRule> getRules() const;

    // 算法权重配置
    void setAlgorithmWeights(const QHash<QString, double> &weights);
    QHash<QString, double> getAlgorithmWeights() const;

    // 学习和训练
    void trainModel();
    void updateModel(const QList<UserAction> &actions);

signals:
    void recommendationsReady(const QList<Recommendation> &recommendations);
    void modelUpdated();
    void feedbackRecorded(int itemId, const QString &action);

private slots:
    void onUserActionRecorded(const UserAction &action);
    void onContextChanged();

private:
    // 推荐算法
    QList<Recommendation> getContentBasedRecommendations(const RecommendationContext &context) const;
    QList<Recommendation> getCollaborativeRecommendations(const RecommendationContext &context) const;
    QList<Recommendation> getContextAwareRecommendations(const RecommendationContext &context) const;
    QList<Recommendation> getTimeBasedRecommendations(const RecommendationContext &context) const;
    QList<Recommendation> getUsageBasedRecommendations(const RecommendationContext &context) const;

    // 规则引擎
    QList<Recommendation> applyRules(const RecommendationContext &context) const;
    bool evaluateRule(const RecommendationRule &rule, const RecommendationContext &context) const;

    // 评分和排序
    double calculateRecommendationScore(const LaunchItem &item, const RecommendationContext &context) const;
    QList<Recommendation> mergeAndRankRecommendations(const QList<QList<Recommendation>> &algorithmResults) const;

    // 机器学习
    void loadModel();
    void saveModel();
    void updateUserProfile(const QList<UserAction> &actions);

private:
    std::shared_ptr<LaunchItemRepository> m_itemRepository;
    std::shared_ptr<UserActionRepository> m_actionRepository;
    std::shared_ptr<RecommendationRuleRepository> m_ruleRepository;
    std::shared_ptr<CacheService> m_cache;
    std::shared_ptr<LoggingService> m_logger;

    QHash<QString, double> m_algorithmWeights;
    QHash<int, QHash<QString, double>> m_userProfiles;  // 用户画像
    QHash<int, QHash<int, double>> m_itemSimilarity;    // 物品相似度

    QTimer *m_contextUpdateTimer;
    QFutureWatcher<QList<Recommendation>> *m_recommendationWatcher;

    mutable QReadWriteLock m_modelLock;
};
```

### 7. 配置管理器 (ConfigManager)

```cpp
class ConfigManager : public QObject {
    Q_OBJECT

public:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    // 配置操作
    QVariant getValue(const QString &key, const QVariant &defaultValue = QVariant()) const;
    void setValue(const QString &key, const QVariant &value);
    bool hasKey(const QString &key) const;
    void removeKey(const QString &key);

    // 批量操作
    void setValues(const QHash<QString, QVariant> &values);
    QHash<QString, QVariant> getValues(const QStringList &keys) const;
    QHash<QString, QVariant> getAllValues() const;

    // 配置分组
    void beginGroup(const QString &prefix);
    void endGroup();
    QString group() const;
    QStringList childKeys() const;
    QStringList childGroups() const;

    // 配置同步
    void sync();
    void reload();
    bool isWritable() const;

    // 配置备份和恢复
    bool backup(const QString &backupPath) const;
    bool restore(const QString &backupPath);

    // 配置验证
    bool validate() const;
    QStringList getValidationErrors() const;

    // 配置监听
    void watchKey(const QString &key);
    void unwatchKey(const QString &key);

signals:
    void valueChanged(const QString &key, const QVariant &value);
    void configReloaded();
    void configSynced();

private slots:
    void onFileChanged(const QString &path);

private:
    void loadConfig();
    void saveConfig();
    void setupFileWatcher();
    void validateConfigValue(const QString &key, const QVariant &value);

private:
    std::unique_ptr<QSettings> m_settings;
    std::unique_ptr<QFileSystemWatcher> m_fileWatcher;

    QHash<QString, QVariant> m_cache;
    QStringList m_watchedKeys;
    QStringList m_groupStack;

    mutable QReadWriteLock m_lock;
    bool m_autoSync = true;
    bool m_cacheEnabled = true;
};
```

### 8. 插件管理器 (PluginManager)

```cpp
class PluginManager : public QObject {
    Q_OBJECT

public:
    explicit PluginManager(QObject *parent = nullptr);
    ~PluginManager();

    // 插件生命周期管理
    bool loadPlugin(const QString &pluginPath);
    bool unloadPlugin(const QString &pluginId);
    bool reloadPlugin(const QString &pluginId);

    // 插件查询
    QList<PluginInfo> getLoadedPlugins() const;
    QList<PluginInfo> getAvailablePlugins() const;
    PluginInfo getPluginInfo(const QString &pluginId) const;
    bool isPluginLoaded(const QString &pluginId) const;

    // 插件状态管理
    bool enablePlugin(const QString &pluginId);
    bool disablePlugin(const QString &pluginId);
    bool isPluginEnabled(const QString &pluginId) const;

    // 插件接口访问
    template<typename T>
    QList<T*> getPlugins() const;

    template<typename T>
    T* getPlugin(const QString &pluginId) const;

    // 插件配置
    QVariant getPluginConfig(const QString &pluginId, const QString &key, const QVariant &defaultValue = QVariant()) const;
    void setPluginConfig(const QString &pluginId, const QString &key, const QVariant &value);

    // 插件安装和卸载
    bool installPlugin(const QString &packagePath);
    bool uninstallPlugin(const QString &pluginId);

    // 插件更新
    QList<PluginUpdateInfo> checkForUpdates() const;
    bool updatePlugin(const QString &pluginId);

signals:
    void pluginLoaded(const QString &pluginId);
    void pluginUnloaded(const QString &pluginId);
    void pluginEnabled(const QString &pluginId);
    void pluginDisabled(const QString &pluginId);
    void pluginError(const QString &pluginId, const QString &error);

private slots:
    void onPluginDirectoryChanged(const QString &path);

private:
    struct PluginData {
        QString id;
        QString name;
        QString version;
        QString description;
        QString author;
        QString filePath;
        QPluginLoader *loader;
        IPlugin *instance;
        bool enabled;
        QHash<QString, QVariant> config;
        QDateTime loadTime;
        int errorCount;
    };

    void scanPluginDirectory();
    bool validatePlugin(const QString &pluginPath) const;
    PluginInfo createPluginInfo(const PluginData &data) const;
    void setupPluginSandbox(const QString &pluginId);
    void cleanupPlugin(const QString &pluginId);

private:
    QHash<QString, PluginData> m_plugins;
    QString m_pluginDirectory;
    std::unique_ptr<QFileSystemWatcher> m_directoryWatcher;
    std::shared_ptr<LoggingService> m_logger;

    mutable QReadWriteLock m_lock;
};
```

### 9. 热键管理器 (HotkeyManager)

```cpp
class HotkeyManager : public QObject {
    Q_OBJECT

public:
    explicit HotkeyManager(QObject *parent = nullptr);
    ~HotkeyManager();

    // 热键注册和注销
    bool registerHotkey(const QString &id, const QKeySequence &keySequence, bool global = true);
    bool unregisterHotkey(const QString &id);
    bool updateHotkey(const QString &id, const QKeySequence &keySequence);

    // 热键查询
    QStringList getRegisteredHotkeys() const;
    QKeySequence getHotkey(const QString &id) const;
    bool isHotkeyRegistered(const QString &id) const;
    bool isGlobalHotkey(const QString &id) const;

    // 热键冲突检测
    QStringList checkConflicts(const QKeySequence &keySequence) const;
    bool hasConflicts() const;
    QList<HotkeyConflict> getConflicts() const;

    // 热键启用/禁用
    bool enableHotkey(const QString &id);
    bool disableHotkey(const QString &id);
    bool isHotkeyEnabled(const QString &id) const;

    // 批量操作
    void enableAllHotkeys();
    void disableAllHotkeys();
    void reloadHotkeys();

    // 热键统计
    void recordHotkeyUsage(const QString &id);
    int getHotkeyUsageCount(const QString &id) const;
    QDateTime getLastUsedTime(const QString &id) const;

signals:
    void hotkeyActivated(const QString &id);
    void hotkeyRegistered(const QString &id, const QKeySequence &keySequence);
    void hotkeyUnregistered(const QString &id);
    void hotkeyConflict(const QString &id, const QKeySequence &keySequence);

protected:
    bool nativeEventFilter(const QByteArray &eventType, void *message, qintptr *result) override;

private slots:
    void onConfigChanged(const QString &key, const QVariant &value);

private:
    struct HotkeyData {
        QString id;
        QKeySequence keySequence;
        bool global;
        bool enabled;
        int usageCount;
        QDateTime lastUsed;
        QDateTime registeredAt;
#ifdef Q_OS_WIN
        int virtualKey;
        int modifiers;
        int atomId;
#endif
    };

    void setupNativeHooks();
    void cleanupNativeHooks();
    bool registerNativeHotkey(const HotkeyData &data);
    bool unregisterNativeHotkey(const HotkeyData &data);

#ifdef Q_OS_WIN
    bool registerWindowsHotkey(const HotkeyData &data);
    bool unregisterWindowsHotkey(const HotkeyData &data);
    void processWindowsHotkeyMessage(MSG *msg);
#endif

#ifdef Q_OS_LINUX
    bool registerX11Hotkey(const HotkeyData &data);
    bool unregisterX11Hotkey(const HotkeyData &data);
    void processX11HotkeyEvent(XEvent *event);
#endif

#ifdef Q_OS_MACOS
    bool registerCarbonHotkey(const HotkeyData &data);
    bool unregisterCarbonHotkey(const HotkeyData &data);
    void processCarbonHotkeyEvent(EventRef event);
#endif

private:
    QHash<QString, HotkeyData> m_hotkeys;
    QHash<QKeySequence, QString> m_keySequenceMap;
    std::shared_ptr<ConfigManager> m_configManager;
    std::shared_ptr<LoggingService> m_logger;

    mutable QReadWriteLock m_lock;
    bool m_nativeHooksEnabled = true;

#ifdef Q_OS_WIN
    static HotkeyManager *s_instance;
    int m_nextAtomId = 1;
#endif
};
```

### 10. 数据库管理器 (DatabaseManager)

```cpp
class DatabaseManager : public QObject {
    Q_OBJECT

public:
    explicit DatabaseManager(QObject *parent = nullptr);
    ~DatabaseManager();

    // 数据库连接管理
    bool initialize(const QString &databasePath);
    void close();
    bool isOpen() const;

    // 连接池管理
    QSqlDatabase getConnection(const QString &connectionName = QString());
    void releaseConnection(const QString &connectionName);

    // 事务管理
    bool beginTransaction(const QString &connectionName = QString());
    bool commitTransaction(const QString &connectionName = QString());
    bool rollbackTransaction(const QString &connectionName = QString());

    // 查询执行
    QSqlQuery executeQuery(const QString &sql, const QVariantList &params = {}, const QString &connectionName = QString());
    bool executeNonQuery(const QString &sql, const QVariantList &params = {}, const QString &connectionName = QString());

    // 批量操作
    bool executeBatch(const QString &sql, const QList<QVariantList> &paramsList, const QString &connectionName = QString());

    // 数据库维护
    bool vacuum();
    bool analyze();
    bool checkIntegrity();
    QStringList getIntegrityErrors();

    // 备份和恢复
    bool backup(const QString &backupPath);
    bool restore(const QString &backupPath);

    // 迁移管理
    bool runMigrations();
    QString getCurrentVersion() const;
    QStringList getPendingMigrations() const;

    // 性能监控
    DatabaseStats getStats() const;
    void enableQueryLogging(bool enabled);
    QList<QueryLog> getSlowQueries(int thresholdMs = 1000) const;

signals:
    void connected();
    void disconnected();
    void error(const QString &error);
    void migrationCompleted(const QString &version);
    void slowQueryDetected(const QueryLog &query);

private slots:
    void onConnectionError();
    void onQueryFinished();

private:
    void setupDatabase();
    void createConnectionPool();
    void loadMigrations();
    bool executeMigration(const Migration &migration);
    void logQuery(const QString &sql, const QVariantList &params, qint64 executionTime);

private:
    struct ConnectionInfo {
        QSqlDatabase database;
        bool inUse;
        QDateTime lastUsed;
        int queryCount;
    };

    QString m_databasePath;
    QHash<QString, ConnectionInfo> m_connections;
    QList<Migration> m_migrations;
    std::shared_ptr<LoggingService> m_logger;

    mutable QReadWriteLock m_lock;
    bool m_queryLoggingEnabled = false;
    QList<QueryLog> m_queryLog;

    static const int MAX_CONNECTIONS = 10;
    static const int CONNECTION_TIMEOUT = 300000; // 5 minutes
};
```

## 🛠️ 技术实现细节

### 1. 依赖注入实现

```cpp
// ServiceContainer.cpp 实现示例
template<typename Interface, typename Implementation>
void ServiceContainer::registerService() {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto factory = []() -> std::shared_ptr<void> {
        return std::make_shared<Implementation>();
    };

    m_factories[std::type_index(typeid(Interface))] = factory;
}

template<typename Interface>
std::shared_ptr<Interface> ServiceContainer::getService() {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto typeIndex = std::type_index(typeid(Interface));

    // 检查是否已有实例
    auto it = m_services.find(typeIndex);
    if (it != m_services.end()) {
        return std::static_pointer_cast<Interface>(it->second);
    }

    // 查找工厂函数
    auto factoryIt = m_factories.find(typeIndex);
    if (factoryIt != m_factories.end()) {
        auto instance = factoryIt->second();
        m_services[typeIndex] = instance;
        return std::static_pointer_cast<Interface>(instance);
    }

    return nullptr;
}
```

### 2. MVVM数据绑定

```cpp
// LaunchItemViewModel.h
class LaunchItemViewModel : public QAbstractListModel {
    Q_OBJECT
    Q_PROPERTY(int count READ count NOTIFY countChanged)
    Q_PROPERTY(QString searchText READ searchText WRITE setSearchText NOTIFY searchTextChanged)
    Q_PROPERTY(int selectedCategoryId READ selectedCategoryId WRITE setSelectedCategoryId NOTIFY selectedCategoryIdChanged)

public:
    enum Roles {
        IdRole = Qt::UserRole + 1,
        NameRole,
        PathRole,
        IconRole,
        CategoryRole,
        UseCountRole,
        LastUsedRole,
        ScoreRole
    };

    explicit LaunchItemViewModel(QObject *parent = nullptr);

    // QAbstractListModel interface
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QHash<int, QByteArray> roleNames() const override;

    // Properties
    int count() const { return m_items.size(); }
    QString searchText() const { return m_searchText; }
    void setSearchText(const QString &text);
    int selectedCategoryId() const { return m_selectedCategoryId; }
    void setSelectedCategoryId(int categoryId);

    // Methods
    Q_INVOKABLE void refresh();
    Q_INVOKABLE void launchItem(int index);
    Q_INVOKABLE LaunchItem getItem(int index) const;

signals:
    void countChanged();
    void searchTextChanged();
    void selectedCategoryIdChanged();
    void itemLaunched(int id, bool success);

private slots:
    void onSearchCompleted(const QList<SearchResult> &results);
    void onItemsChanged();

private:
    void updateItems();
    void applyFilters();

private:
    std::shared_ptr<LaunchManager> m_launchManager;
    std::shared_ptr<SearchEngine> m_searchEngine;

    QList<LaunchItem> m_items;
    QList<SearchResult> m_searchResults;
    QString m_searchText;
    int m_selectedCategoryId = -1;

    QTimer *m_searchTimer;
};
```

### 3. 异步操作处理

```cpp
// AsyncTaskManager.h - 异步任务管理器
class AsyncTaskManager : public QObject {
    Q_OBJECT

public:
    template<typename Func, typename... Args>
    auto runAsync(Func&& func, Args&&... args) -> QFuture<std::invoke_result_t<Func, Args...>> {
        using ReturnType = std::invoke_result_t<Func, Args...>;

        auto task = [func = std::forward<Func>(func), args...]() mutable -> ReturnType {
            return func(args...);
        };

        return QtConcurrent::run(QThreadPool::globalInstance(), task);
    }

    template<typename T>
    void runAsyncWithCallback(std::function<T()> task, std::function<void(T)> callback) {
        auto watcher = new QFutureWatcher<T>(this);

        connect(watcher, &QFutureWatcher<T>::finished, [watcher, callback]() {
            callback(watcher->result());
            watcher->deleteLater();
        });

        auto future = QtConcurrent::run(QThreadPool::globalInstance(), task);
        watcher->setFuture(future);
    }

    void cancelAllTasks();
    int getActiveTaskCount() const;

private:
    QList<QFutureWatcher<void>*> m_watchers;
};
```

### 4. 缓存系统实现

```cpp
// CacheService.h
class CacheService : public QObject {
    Q_OBJECT

public:
    explicit CacheService(QObject *parent = nullptr);

    // 基本缓存操作
    void set(const QString &key, const QVariant &value, int ttlSeconds = 3600);
    QVariant get(const QString &key, const QVariant &defaultValue = QVariant()) const;
    bool contains(const QString &key) const;
    void remove(const QString &key);
    void clear();

    // 批量操作
    void setMultiple(const QHash<QString, QVariant> &data, int ttlSeconds = 3600);
    QHash<QString, QVariant> getMultiple(const QStringList &keys) const;

    // 缓存统计
    int size() const;
    qint64 memoryUsage() const;
    double hitRate() const;
    void resetStats();

    // 缓存策略
    void setMaxSize(int maxSize);
    void setDefaultTTL(int ttlSeconds);
    void enableCompression(bool enabled);

signals:
    void itemExpired(const QString &key);
    void cacheCleared();

private slots:
    void onCleanupTimer();

private:
    struct CacheItem {
        QVariant value;
        QDateTime expiry;
        QDateTime lastAccessed;
        int accessCount;
        qint64 size;
    };

    void cleanup();
    void evictLRU();
    qint64 calculateSize(const QVariant &value) const;

private:
    mutable QHash<QString, CacheItem> m_cache;
    mutable QReadWriteLock m_lock;

    QTimer *m_cleanupTimer;
    int m_maxSize = 10000;
    int m_defaultTTL = 3600;
    bool m_compressionEnabled = false;

    // 统计信息
    mutable qint64 m_hits = 0;
    mutable qint64 m_misses = 0;
};
```

### 5. 日志系统

```cpp
// LoggingService.h
class LoggingService : public QObject {
    Q_OBJECT

public:
    enum LogLevel {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3,
        Fatal = 4
    };

    explicit LoggingService(QObject *parent = nullptr);
    ~LoggingService();

    // 日志记录
    void log(LogLevel level, const QString &category, const QString &message, const QString &file = QString(), int line = 0);
    void debug(const QString &message, const QString &category = "General");
    void info(const QString &message, const QString &category = "General");
    void warning(const QString &message, const QString &category = "General");
    void error(const QString &message, const QString &category = "General");
    void fatal(const QString &message, const QString &category = "General");

    // 配置
    void setLogLevel(LogLevel level);
    void setLogFile(const QString &filePath);
    void setMaxFileSize(qint64 maxSize);
    void setMaxFileCount(int maxCount);
    void enableConsoleOutput(bool enabled);
    void enableFileOutput(bool enabled);

    // 过滤器
    void addCategoryFilter(const QString &category, LogLevel minLevel);
    void removeCategoryFilter(const QString &category);

    // 日志查询
    QList<LogEntry> getRecentLogs(int count = 100) const;
    QList<LogEntry> getLogsByLevel(LogLevel level, int count = 100) const;
    QList<LogEntry> getLogsByCategory(const QString &category, int count = 100) const;

signals:
    void logEntryAdded(const LogEntry &entry);

private slots:
    void onLogRotationTimer();

private:
    void writeToFile(const LogEntry &entry);
    void writeToConsole(const LogEntry &entry);
    void rotateLogFiles();
    QString formatLogEntry(const LogEntry &entry) const;

private:
    LogLevel m_logLevel = Info;
    QString m_logFilePath;
    qint64 m_maxFileSize = 10 * 1024 * 1024; // 10MB
    int m_maxFileCount = 5;
    bool m_consoleOutputEnabled = true;
    bool m_fileOutputEnabled = true;

    QHash<QString, LogLevel> m_categoryFilters;
    QList<LogEntry> m_recentLogs;

    std::unique_ptr<QFile> m_logFile;
    std::unique_ptr<QTextStream> m_logStream;
    QTimer *m_rotationTimer;

    mutable QReadWriteLock m_lock;
    static const int MAX_RECENT_LOGS = 1000;
};

// 便利宏定义
#define LOG_DEBUG(message) LoggingService::instance()->debug(message, __FILE__, __LINE__)
#define LOG_INFO(message) LoggingService::instance()->info(message, __FILE__, __LINE__)
#define LOG_WARNING(message) LoggingService::instance()->warning(message, __FILE__, __LINE__)
#define LOG_ERROR(message) LoggingService::instance()->error(message, __FILE__, __LINE__)
#define LOG_FATAL(message) LoggingService::instance()->fatal(message, __FILE__, __LINE__)
```

## 🔧 构建配置

### 1. CMakeLists.txt 主配置

```cmake
cmake_minimum_required(VERSION 3.20)
project(KKQuickLaunch VERSION 1.0.0 LANGUAGES CXX)

# C++标准设置
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Qt配置
find_package(Qt6 REQUIRED COMPONENTS
    Core
    Widgets
    Sql
    Network
    Concurrent
    WebSockets
    Multimedia
)

# 编译选项
if(MSVC)
    add_compile_options(/W4 /WX)
else()
    add_compile_options(-Wall -Wextra -Werror)
endif()

# 预编译头文件
target_precompile_headers(${PROJECT_NAME} PRIVATE
    <QtCore>
    <QtWidgets>
    <QtSql>
    <QtNetwork>
    <QtConcurrent>
    <memory>
    <functional>
    <algorithm>
)

# 源文件
file(GLOB_RECURSE SOURCES
    "src/*.cpp"
    "src/*.h"
)

# 资源文件
qt6_add_resources(RESOURCES
    resources/icons.qrc
    resources/themes.qrc
    resources/translations.qrc
)

# 可执行文件
qt6_add_executable(${PROJECT_NAME} ${SOURCES} ${RESOURCES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    Qt6::Core
    Qt6::Widgets
    Qt6::Sql
    Qt6::Network
    Qt6::Concurrent
    Qt6::WebSockets
    Qt6::Multimedia
)

# 包含目录
target_include_directories(${PROJECT_NAME} PRIVATE
    src
    src/application
    src/presentation
    src/business
    src/services
    src/data
    src/infrastructure
    src/plugins
)

# 编译定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    QT_NO_CAST_FROM_ASCII
    QT_NO_CAST_TO_ASCII
    QT_NO_URL_CAST_FROM_STRING
    QT_NO_CAST_FROM_BYTEARRAY
    QT_STRICT_ITERATORS
    QT_NO_NARROWING_CONVERSIONS_IN_CONNECT
)

# 安装配置
install(TARGETS ${PROJECT_NAME}
    BUNDLE DESTINATION .
    RUNTIME DESTINATION bin
)

# 部署Qt库
qt6_generate_deploy_app_script(
    TARGET ${PROJECT_NAME}
    OUTPUT_SCRIPT deploy_script
    NO_UNSUPPORTED_PLATFORM_ERROR
)
install(SCRIPT ${deploy_script})
```

### 2. Conan依赖管理

```ini
# conanfile.txt
[requires]
qt/6.5.0
sqlite3/3.42.0
openssl/1.1.1t
zlib/1.2.13
nlohmann_json/3.11.2
spdlog/1.12.0
catch2/3.4.0

[generators]
CMakeDeps
CMakeToolchain

[options]
qt:shared=True
qt:qttools=True
qt:qtsvg=True
qt:qtdeclarative=True
qt:qtwebsockets=True
qt:qtmultimedia=True

[imports]
bin, *.dll -> ./bin
lib, *.dylib* -> ./bin
```

### 3. 持续集成配置

```yaml
# .github/workflows/build.yml
name: Build and Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
        build_type: [Release, Debug]

    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v3

    - name: Setup Qt
      uses: jurplel/install-qt-action@v3
      with:
        version: '6.5.0'

    - name: Setup Conan
      uses: turtlebrowser/get-conan@main

    - name: Install dependencies
      run: |
        conan install . --build=missing -s build_type=${{ matrix.build_type }}

    - name: Configure CMake
      run: |
        cmake -B build -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake

    - name: Build
      run: cmake --build build --config ${{ matrix.build_type }}

    - name: Test
      run: ctest --test-dir build --config ${{ matrix.build_type }} --output-on-failure

    - name: Package
      if: matrix.build_type == 'Release'
      run: cmake --build build --target package

    - name: Upload artifacts
      if: matrix.build_type == 'Release'
      uses: actions/upload-artifact@v3
      with:
        name: KKQuickLaunch-${{ matrix.os }}
        path: build/packages/*
```

## 📋 开发最佳实践

### 1. 代码规范

```cpp
// 命名规范
class LaunchManager;           // 类名：PascalCase
void launchItem();            // 方法名：camelCase
int m_itemCount;              // 成员变量：m_前缀 + camelCase
QString g_appVersion;         // 全局变量：g_前缀 + camelCase
const int MAX_ITEMS = 100;    // 常量：UPPER_CASE

// 头文件保护
#pragma once

// 包含顺序
#include <QtCore>             // Qt系统头文件
#include <memory>             // 标准库头文件
#include "LaunchItem.h"       // 项目头文件

// 前置声明
class QTimer;
class DatabaseManager;

// 智能指针使用
std::unique_ptr<QTimer> m_timer;           // 独占所有权
std::shared_ptr<DatabaseManager> m_db;     // 共享所有权
std::weak_ptr<LaunchManager> m_manager;    // 弱引用

// RAII原则
class ResourceManager {
public:
    ResourceManager() { acquire(); }
    ~ResourceManager() { release(); }

    // 禁用拷贝
    ResourceManager(const ResourceManager&) = delete;
    ResourceManager& operator=(const ResourceManager&) = delete;

    // 支持移动
    ResourceManager(ResourceManager&&) = default;
    ResourceManager& operator=(ResourceManager&&) = default;
};
```

### 2. 错误处理

```cpp
// 异常安全的错误处理
class LaunchManager {
public:
    enum class LaunchResult {
        Success,
        FileNotFound,
        AccessDenied,
        InvalidPath,
        SystemError
    };

    struct LaunchError {
        LaunchResult result;
        QString message;
        QString details;
        int systemErrorCode = 0;
    };

    // 使用Expected模式
    template<typename T>
    using Expected = std::variant<T, LaunchError>;

    Expected<bool> launchItem(int id) {
        try {
            auto item = getItem(id);
            if (!item) {
                return LaunchError{LaunchResult::FileNotFound, "Item not found", QString("ID: %1").arg(id)};
            }

            if (!QFile::exists(item->path)) {
                return LaunchError{LaunchResult::FileNotFound, "Executable not found", item->path};
            }

            // 执行启动逻辑
            bool success = executeProcess(item->path, item->arguments);
            if (success) {
                recordUsage(id);
                return true;
            } else {
                return LaunchError{LaunchResult::SystemError, "Failed to start process", item->path};
            }

        } catch (const std::exception& e) {
            return LaunchError{LaunchResult::SystemError, "Unexpected error", e.what()};
        }
    }
};
```

### 3. 性能优化

```cpp
// 延迟加载
class LaunchItemModel {
private:
    mutable QHash<int, QIcon> m_iconCache;

public:
    QIcon getIcon(int itemId) const {
        // 延迟加载图标
        if (!m_iconCache.contains(itemId)) {
            auto item = getItem(itemId);
            m_iconCache[itemId] = loadIcon(item.iconPath);
        }
        return m_iconCache[itemId];
    }
};

// 对象池模式
template<typename T>
class ObjectPool {
public:
    template<typename... Args>
    std::unique_ptr<T> acquire(Args&&... args) {
        std::lock_guard<std::mutex> lock(m_mutex);

        if (!m_pool.empty()) {
            auto obj = std::move(m_pool.back());
            m_pool.pop_back();
            return obj;
        }

        return std::make_unique<T>(std::forward<Args>(args)...);
    }

    void release(std::unique_ptr<T> obj) {
        if (obj) {
            std::lock_guard<std::mutex> lock(m_mutex);
            if (m_pool.size() < m_maxSize) {
                m_pool.push_back(std::move(obj));
            }
        }
    }

private:
    std::vector<std::unique_ptr<T>> m_pool;
    std::mutex m_mutex;
    size_t m_maxSize = 10;
};

// 内存映射文件
class MappedFile {
public:
    bool open(const QString& filePath) {
        m_file.setFileName(filePath);
        if (!m_file.open(QIODevice::ReadOnly)) {
            return false;
        }

        m_data = m_file.map(0, m_file.size());
        return m_data != nullptr;
    }

    const char* data() const { return reinterpret_cast<const char*>(m_data); }
    qint64 size() const { return m_file.size(); }

private:
    QFile m_file;
    uchar* m_data = nullptr;
};
```

### 4. 测试策略

```cpp
// 单元测试示例
class LaunchManagerTest : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    void testAddItem();
    void testRemoveItem();
    void testLaunchItem();
    void testSearchItems();

private:
    std::unique_ptr<LaunchManager> m_manager;
    std::unique_ptr<QTemporaryDir> m_tempDir;
};

void LaunchManagerTest::testAddItem() {
    LaunchItem item;
    item.name = "Test App";
    item.path = "/path/to/test.exe";
    item.categoryId = 1;

    bool result = m_manager->addItem(item);
    QVERIFY(result);

    auto items = m_manager->getAllItems();
    QCOMPARE(items.size(), 1);
    QCOMPARE(items.first().name, "Test App");
}

// Mock对象
class MockDatabaseService : public IDatabaseService {
public:
    MOCK_METHOD(QSqlQuery, executeQuery, (const QString& sql, const QVariantList& params), (override));
    MOCK_METHOD(bool, executeNonQuery, (const QString& sql, const QVariantList& params), (override));
};

// 集成测试
class IntegrationTest : public QObject {
    Q_OBJECT

private slots:
    void testFullWorkflow() {
        // 创建真实的应用程序实例
        Application app;
        app.initialize();

        // 测试完整的用户工作流程
        auto launchManager = app.serviceContainer()->getService<LaunchManager>();
        auto searchEngine = app.serviceContainer()->getService<SearchEngine>();

        // 添加测试项目
        LaunchItem item;
        item.name = "Notepad";
        item.path = "C:\\Windows\\System32\\notepad.exe";
        launchManager->addItem(item);

        // 搜索测试
        auto results = searchEngine->search("notepad");
        QVERIFY(!results.isEmpty());

        // 启动测试
        bool launched = launchManager->launchItem(results.first().item.id);
        QVERIFY(launched);
    }
};
```

## 🚀 部署和分发

### 1. Windows部署

```batch
REM deploy_windows.bat
@echo off
set BUILD_DIR=build\Release
set DEPLOY_DIR=deploy\windows

REM 创建部署目录
mkdir %DEPLOY_DIR%

REM 复制可执行文件
copy %BUILD_DIR%\KKQuickLaunch.exe %DEPLOY_DIR%\

REM 部署Qt依赖
windeployqt --qmldir src\qml %DEPLOY_DIR%\KKQuickLaunch.exe

REM 复制其他依赖
copy %BUILD_DIR%\*.dll %DEPLOY_DIR%\

REM 创建安装包
makensis installer\windows\installer.nsi
```

### 2. Linux部署

```bash
#!/bin/bash
# deploy_linux.sh
BUILD_DIR="build/Release"
DEPLOY_DIR="deploy/linux"
APP_NAME="KKQuickLaunch"

# 创建AppImage结构
mkdir -p $DEPLOY_DIR/$APP_NAME.AppDir/usr/bin
mkdir -p $DEPLOY_DIR/$APP_NAME.AppDir/usr/lib
mkdir -p $DEPLOY_DIR/$APP_NAME.AppDir/usr/share/applications
mkdir -p $DEPLOY_DIR/$APP_NAME.AppDir/usr/share/icons/hicolor/256x256/apps

# 复制可执行文件
cp $BUILD_DIR/$APP_NAME $DEPLOY_DIR/$APP_NAME.AppDir/usr/bin/

# 复制依赖库
ldd $BUILD_DIR/$APP_NAME | grep "=> /" | awk '{print $3}' | xargs -I '{}' cp -v '{}' $DEPLOY_DIR/$APP_NAME.AppDir/usr/lib/

# 创建desktop文件
cat > $DEPLOY_DIR/$APP_NAME.AppDir/usr/share/applications/$APP_NAME.desktop << EOF
[Desktop Entry]
Type=Application
Name=KK QuickLaunch
Exec=$APP_NAME
Icon=$APP_NAME
Categories=Utility;
EOF

# 复制图标
cp resources/icons/app.png $DEPLOY_DIR/$APP_NAME.AppDir/usr/share/icons/hicolor/256x256/apps/$APP_NAME.png

# 创建AppRun
cat > $DEPLOY_DIR/$APP_NAME.AppDir/AppRun << 'EOF'
#!/bin/bash
HERE="$(dirname "$(readlink -f "${0}")")"
export LD_LIBRARY_PATH="${HERE}/usr/lib:${LD_LIBRARY_PATH}"
exec "${HERE}/usr/bin/KKQuickLaunch" "$@"
EOF
chmod +x $DEPLOY_DIR/$APP_NAME.AppDir/AppRun

# 生成AppImage
appimagetool $DEPLOY_DIR/$APP_NAME.AppDir $DEPLOY_DIR/$APP_NAME.AppImage
```

### 3. macOS部署

```bash
#!/bin/bash
# deploy_macos.sh
BUILD_DIR="build/Release"
DEPLOY_DIR="deploy/macos"
APP_NAME="KKQuickLaunch"

# 创建app bundle
mkdir -p $DEPLOY_DIR/$APP_NAME.app/Contents/MacOS
mkdir -p $DEPLOY_DIR/$APP_NAME.app/Contents/Resources

# 复制可执行文件
cp $BUILD_DIR/$APP_NAME $DEPLOY_DIR/$APP_NAME.app/Contents/MacOS/

# 创建Info.plist
cat > $DEPLOY_DIR/$APP_NAME.app/Contents/Info.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>$APP_NAME</string>
    <key>CFBundleIdentifier</key>
    <string>com.kkquicklaunch.app</string>
    <key>CFBundleName</key>
    <string>KK QuickLaunch</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
</dict>
</plist>
EOF

# 部署Qt框架
macdeployqt $DEPLOY_DIR/$APP_NAME.app

# 代码签名
codesign --force --verify --verbose --sign "Developer ID Application: Your Name" $DEPLOY_DIR/$APP_NAME.app

# 创建DMG
hdiutil create -volname "$APP_NAME" -srcfolder $DEPLOY_DIR -ov -format UDZO $DEPLOY_DIR/$APP_NAME.dmg
```

---

**版本**: 1.0.0
**最后更新**: 2024-12-17
**维护者**: 开发团队

这个Qt客户端架构设计提供了：
✅ **完整的分层架构**
✅ **MVVM设计模式**
✅ **依赖注入容器**
✅ **插件化系统**
✅ **异步处理机制**
✅ **缓存和性能优化**
✅ **完善的错误处理**
✅ **单元测试支持**
✅ **跨平台部署方案**

该架构具有高度的可扩展性、可维护性和可测试性，能够支撑复杂的桌面应用程序开发需求。
```
```
```
```
