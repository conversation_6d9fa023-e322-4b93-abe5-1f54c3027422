-- ============================================================================
-- 智能启动助手 - 数据库维护脚本
-- 版本: 1.0.0
-- 创建时间: 2024-12-17
-- 说明: 数据库优化、清理和维护相关SQL语句
-- ============================================================================

-- ============================================================================
-- 1. 数据库健康检查
-- ============================================================================

-- 1.1 完整性检查
PRAGMA integrity_check;

-- 1.2 外键约束检查
PRAGMA foreign_key_check;

-- 1.3 数据库统计信息
SELECT 
    'Database File Size' as metric,
    ROUND(page_count * page_size / 1024.0 / 1024.0, 2) || ' MB' as value
FROM pragma_page_count(), pragma_page_size()

UNION ALL

SELECT 
    'Free Pages' as metric,
    CAST(freelist_count AS TEXT) as value
FROM pragma_freelist_count()

UNION ALL

SELECT 
    'Page Size' as metric,
    CAST(page_size AS TEXT) || ' bytes' as value
FROM pragma_page_size()

UNION ALL

SELECT 
    'Cache Size' as metric,
    CAST(cache_size AS TEXT) || ' pages' as value
FROM pragma_cache_size()

UNION ALL

SELECT 
    'Journal Mode' as metric,
    journal_mode as value
FROM pragma_journal_mode()

UNION ALL

SELECT 
    'Synchronous Mode' as metric,
    CAST(synchronous AS TEXT) as value
FROM pragma_synchronous();

-- ============================================================================
-- 2. 表空间使用分析
-- ============================================================================

-- 2.1 各表的空间使用情况
SELECT 
    name as table_name,
    ROUND(
        (SELECT COUNT(*) FROM pragma_table_info(name)) * 
        (SELECT AVG(LENGTH(sql)) FROM sqlite_master WHERE type='table' AND name=outer.name) / 1024.0, 
        2
    ) as estimated_size_kb,
    CASE name
        WHEN 'launch_items' THEN (SELECT COUNT(*) FROM launch_items)
        WHEN 'categories' THEN (SELECT COUNT(*) FROM categories)
        WHEN 'user_actions' THEN (SELECT COUNT(*) FROM user_actions)
        WHEN 'search_history' THEN (SELECT COUNT(*) FROM search_history)
        WHEN 'app_settings' THEN (SELECT COUNT(*) FROM app_settings)
        WHEN 'hotkey_settings' THEN (SELECT COUNT(*) FROM hotkey_settings)
        WHEN 'recommendation_rules' THEN (SELECT COUNT(*) FROM recommendation_rules)
        WHEN 'file_associations' THEN (SELECT COUNT(*) FROM file_associations)
        WHEN 'plugins' THEN (SELECT COUNT(*) FROM plugins)
        WHEN 'system_logs' THEN (SELECT COUNT(*) FROM system_logs)
        ELSE 0
    END as record_count,
    CASE 
        WHEN name IN ('user_actions', 'search_history', 'system_logs') THEN 'High Growth'
        WHEN name IN ('launch_items', 'file_associations') THEN 'Medium Growth'
        ELSE 'Low Growth'
    END as growth_category
FROM sqlite_master outer
WHERE type = 'table' 
  AND name NOT LIKE 'sqlite_%'
  AND name NOT LIKE 'backup_%'
ORDER BY estimated_size_kb DESC;

-- ============================================================================
-- 3. 数据清理操作
-- ============================================================================

-- 3.1 清理过期的用户行为记录(保留最近90天)
-- 执行前先备份重要数据
CREATE TABLE IF NOT EXISTS backup_user_actions_cleanup AS 
SELECT * FROM user_actions 
WHERE timestamp < datetime('now', '-90 days')
LIMIT 1000; -- 限制备份数量

-- 实际清理操作
DELETE FROM user_actions 
WHERE timestamp < datetime('now', '-90 days');

-- 记录清理结果
INSERT INTO system_logs (log_level, category, message, details) 
VALUES ('INFO', 'maintenance', '清理过期用户行为记录', 
        'Deleted ' || changes() || ' records older than 90 days');

-- 3.2 清理过期的搜索历史(保留最近30天)
CREATE TABLE IF NOT EXISTS backup_search_history_cleanup AS 
SELECT * FROM search_history 
WHERE timestamp < datetime('now', '-30 days')
LIMIT 500;

DELETE FROM search_history 
WHERE timestamp < datetime('now', '-30 days');

INSERT INTO system_logs (log_level, category, message, details) 
VALUES ('INFO', 'maintenance', '清理过期搜索历史', 
        'Deleted ' || changes() || ' search records older than 30 days');

-- 3.3 清理过期的系统日志
-- 保留最近7天的ERROR和FATAL级别日志，30天的其他级别日志
CREATE TABLE IF NOT EXISTS backup_system_logs_cleanup AS 
SELECT * FROM system_logs 
WHERE (log_level IN ('ERROR', 'FATAL') AND timestamp < datetime('now', '-7 days'))
   OR (log_level NOT IN ('ERROR', 'FATAL') AND timestamp < datetime('now', '-30 days'))
LIMIT 1000;

DELETE FROM system_logs 
WHERE (log_level IN ('ERROR', 'FATAL') AND timestamp < datetime('now', '-7 days'))
   OR (log_level NOT IN ('ERROR', 'FATAL') AND timestamp < datetime('now', '-30 days'));

INSERT INTO system_logs (log_level, category, message, details) 
VALUES ('INFO', 'maintenance', '清理过期系统日志', 
        'Deleted ' || changes() || ' log records');

-- 3.4 清理无效的文件关联
-- 删除指向不存在启动项的文件关联
DELETE FROM file_associations 
WHERE launch_item_id NOT IN (SELECT id FROM launch_items);

INSERT INTO system_logs (log_level, category, message, details) 
VALUES ('INFO', 'maintenance', '清理无效文件关联', 
        'Deleted ' || changes() || ' invalid file associations');

-- 3.5 清理重复的搜索历史记录
-- 保留最新的记录，删除重复的搜索查询
DELETE FROM search_history 
WHERE id NOT IN (
    SELECT MIN(id) 
    FROM search_history 
    GROUP BY search_query, DATE(timestamp)
);

INSERT INTO system_logs (log_level, category, message, details) 
VALUES ('INFO', 'maintenance', '清理重复搜索记录', 
        'Deleted ' || changes() || ' duplicate search records');

-- ============================================================================
-- 4. 数据统计更新
-- ============================================================================

-- 4.1 重新计算启动项使用统计
UPDATE launch_items 
SET use_count = (
    SELECT COUNT(*) 
    FROM user_actions 
    WHERE user_actions.launch_item_id = launch_items.id 
      AND user_actions.action_type = 'launch'
),
last_used = (
    SELECT MAX(timestamp) 
    FROM user_actions 
    WHERE user_actions.launch_item_id = launch_items.id 
      AND user_actions.action_type = 'launch'
),
avg_response_time = (
    SELECT AVG(response_time) 
    FROM user_actions 
    WHERE user_actions.launch_item_id = launch_items.id 
      AND user_actions.response_time > 0
);

INSERT INTO system_logs (log_level, category, message, details) 
VALUES ('INFO', 'maintenance', '更新启动项统计', 
        'Updated usage statistics for ' || changes() || ' launch items');

-- 4.2 更新推荐规则统计
UPDATE recommendation_rules 
SET match_count = (
    SELECT COUNT(*) 
    FROM user_actions 
    WHERE user_actions.context_data LIKE '%' || recommendation_rules.rule_type || '%'
      AND user_actions.timestamp >= datetime('now', '-30 days')
),
success_count = (
    SELECT COUNT(*) 
    FROM user_actions 
    WHERE user_actions.context_data LIKE '%' || recommendation_rules.rule_type || '%'
      AND user_actions.action_type = 'launch'
      AND user_actions.timestamp >= datetime('now', '-30 days')
);

INSERT INTO system_logs (log_level, category, message, details) 
VALUES ('INFO', 'maintenance', '更新推荐规则统计', 
        'Updated statistics for ' || changes() || ' recommendation rules');

-- 4.3 更新文件关联使用统计
UPDATE file_associations 
SET use_count = (
    SELECT COUNT(*) 
    FROM user_actions ua
    JOIN launch_items li ON ua.launch_item_id = li.id
    WHERE li.id = file_associations.launch_item_id
      AND ua.action_type = 'launch'
      AND ua.context_data LIKE '%' || file_associations.file_extension || '%'
),
last_used = (
    SELECT MAX(ua.timestamp) 
    FROM user_actions ua
    JOIN launch_items li ON ua.launch_item_id = li.id
    WHERE li.id = file_associations.launch_item_id
      AND ua.action_type = 'launch'
      AND ua.context_data LIKE '%' || file_associations.file_extension || '%'
);

-- ============================================================================
-- 5. 索引维护和优化
-- ============================================================================

-- 5.1 重建所有索引
REINDEX;

-- 5.2 更新表统计信息
ANALYZE;

-- 5.3 检查索引使用情况
SELECT 
    name as index_name,
    tbl_name as table_name,
    CASE 
        WHEN sql LIKE '%UNIQUE%' THEN 'UNIQUE'
        ELSE 'REGULAR'
    END as index_type,
    sql as definition
FROM sqlite_master 
WHERE type = 'index' 
  AND name NOT LIKE 'sqlite_%'
ORDER BY tbl_name, name;

-- ============================================================================
-- 6. 数据库优化
-- ============================================================================

-- 6.1 清理数据库碎片
VACUUM;

-- 6.2 优化数据库设置
PRAGMA optimize;

-- 6.3 设置推荐的PRAGMA参数
PRAGMA journal_mode = WAL;          -- 使用WAL模式提高并发性能
PRAGMA synchronous = NORMAL;        -- 平衡性能和安全性
PRAGMA cache_size = 10000;          -- 增加缓存大小
PRAGMA temp_store = MEMORY;         -- 临时表存储在内存中
PRAGMA mmap_size = 268435456;       -- 256MB内存映射

-- ============================================================================
-- 7. 备份操作
-- ============================================================================

-- 7.1 创建配置备份表
CREATE TABLE IF NOT EXISTS config_backup_$(date +%Y%m%d) AS
SELECT 
    'launch_items' as table_name,
    COUNT(*) as record_count,
    datetime('now') as backup_time
FROM launch_items

UNION ALL

SELECT 
    'app_settings' as table_name,
    COUNT(*) as record_count,
    datetime('now') as backup_time
FROM app_settings

UNION ALL

SELECT 
    'categories' as table_name,
    COUNT(*) as record_count,
    datetime('now') as backup_time
FROM categories;

-- 7.2 导出重要配置(需要在应用程序中执行)
-- .output backup_settings.sql
-- .dump app_settings
-- .dump hotkey_settings
-- .dump categories
-- .output stdout

-- ============================================================================
-- 8. 性能监控查询
-- ============================================================================

-- 8.1 慢查询分析(需要启用查询计划)
-- EXPLAIN QUERY PLAN SELECT * FROM launch_items WHERE name LIKE '%test%';

-- 8.2 表扫描检测
SELECT 
    tbl_name,
    idx_name,
    stat as statistics
FROM sqlite_stat1
ORDER BY tbl_name, idx_name;

-- 8.3 数据库性能指标
SELECT 
    'Total Queries' as metric,
    CAST((SELECT COUNT(*) FROM user_actions WHERE action_type = 'search') AS TEXT) as value

UNION ALL

SELECT 
    'Avg Response Time' as metric,
    ROUND(AVG(response_time), 2) || ' ms' as value
FROM user_actions 
WHERE response_time > 0 
  AND timestamp >= datetime('now', '-7 days')

UNION ALL

SELECT 
    'Cache Hit Rate' as metric,
    '95%' as value  -- 这需要在应用程序中实际计算

UNION ALL

SELECT 
    'Database Growth Rate' as metric,
    ROUND(
        (SELECT COUNT(*) FROM user_actions WHERE timestamp >= datetime('now', '-7 days')) * 1.0 /
        NULLIF((SELECT COUNT(*) FROM user_actions WHERE timestamp >= datetime('now', '-14 days') AND timestamp < datetime('now', '-7 days')), 0) * 100 - 100,
        2
    ) || '%' as value;

-- ============================================================================
-- 9. 维护任务调度建议
-- ============================================================================

-- 9.1 每日维护任务
-- 建议在应用程序中实现定时任务，执行以下操作：
/*
1. 清理当天的临时数据
2. 更新使用统计
3. 检查数据库完整性
4. 记录性能指标
*/

-- 9.2 每周维护任务
/*
1. 清理过期日志
2. 重建索引
3. 更新统计信息
4. 执行VACUUM操作
5. 创建配置备份
*/

-- 9.3 每月维护任务
/*
1. 深度清理历史数据
2. 分析查询性能
3. 优化数据库结构
4. 生成使用报告
*/

-- ============================================================================
-- 10. 维护完成记录
-- ============================================================================

-- 记录维护操作完成
INSERT INTO system_logs (log_level, category, message, details) VALUES
('INFO', 'maintenance', '数据库维护完成', 
 'Maintenance completed at: ' || datetime('now') || 
 ', Database size after maintenance: ' || 
 (SELECT ROUND(page_count * page_size / 1024.0 / 1024.0, 2) FROM pragma_page_count(), pragma_page_size()) || ' MB');

-- 更新最后维护时间
INSERT OR REPLACE INTO app_settings (setting_key, setting_value, value_type, category, description, is_user_configurable) 
VALUES ('system.last_maintenance', datetime('now'), 'string', 'system', '最后维护时间', 0);

-- 显示维护摘要
SELECT 
    'Maintenance Summary' as title,
    'Database maintenance completed successfully' as message,
    datetime('now') as completion_time,
    (SELECT COUNT(*) FROM launch_items) as total_launch_items,
    (SELECT COUNT(*) FROM user_actions) as total_user_actions,
    (SELECT COUNT(*) FROM search_history) as total_search_history,
    (SELECT COUNT(*) FROM system_logs WHERE log_level = 'ERROR') as error_count;

-- ============================================================================
-- 使用说明
-- ============================================================================
/*
此维护脚本应该：

1. 定期执行（建议每周一次）
2. 在应用程序空闲时运行
3. 执行前创建数据库备份
4. 监控执行时间和结果
5. 记录维护日志

执行方式：
1. 在Qt应用程序中通过QSqlQuery执行
2. 分段执行，避免长时间锁定数据库
3. 在事务中执行关键操作
4. 提供进度反馈给用户

注意事项：
1. 大量数据删除前先备份
2. 维护期间可能影响应用性能
3. 定期检查维护脚本的执行结果
4. 根据实际使用情况调整清理策略
*/
