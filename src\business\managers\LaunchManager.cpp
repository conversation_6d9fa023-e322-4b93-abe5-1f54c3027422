#include "LaunchManager.h"
#include "data/repositories/LaunchItemRepository.h"
#include "infrastructure/utils/Logger.h"
#include <QProcess>
#include <QFileInfo>
#include <QDir>
#include <QUrl>
#include <QDesktopServices>
#include <QStandardPaths>
#include <QLoggingCategory>
#include <QElapsedTimer>
#include <QtConcurrent>

Q_LOGGING_CATEGORY(lcLaunchManager, "launchmanager")

// 静态成员初始化
const int LaunchManager::DEFAULT_CLEANUP_INTERVAL = 3600000; // 1小时

LaunchManager::LaunchManager(QObject *parent)
    : QObject(parent)
    , m_fileWatcher(std::make_unique<QFileSystemWatcher>(this))
    , m_cleanupTimer(new QTimer(this))
{
    // 设置清理定时器
    m_cleanupTimer->setInterval(DEFAULT_CLEANUP_INTERVAL);
    m_cleanupTimer->setSingleShot(false);
    connect(m_cleanupTimer, &QTimer::timeout, this, &LaunchManager::onAutoCleanup);
    
    // 连接文件系统监控信号
    connect(m_fileWatcher.get(), &QFileSystemWatcher::fileChanged,
            this, &LaunchManager::onFileSystemChanged);
    connect(m_fileWatcher.get(), &QFileSystemWatcher::directoryChanged,
            this, &LaunchManager::onFileSystemChanged);
    
    qCDebug(lcLaunchManager) << "LaunchManager created";
}

LaunchManager::~LaunchManager()
{
    // 清理运行中的进程
    for (auto it = m_runningProcesses.begin(); it != m_runningProcesses.end(); ++it) {
        if (it->second && it->second->state() != QProcess::NotRunning) {
            it->second->terminate();
            if (!it->second->waitForFinished(3000)) {
                it->second->kill();
            }
        }
    }
    
    qCDebug(lcLaunchManager) << "LaunchManager destroyed";
}

bool LaunchManager::initialize()
{
    qCInfo(lcLaunchManager) << "Initializing LaunchManager";
    
    // 这里应该通过依赖注入获取服务
    // 暂时使用简化的初始化方式
    
    // 启动清理定时器
    if (m_cleanupTimer) {
        m_cleanupTimer->start();
    }
    
    qCInfo(lcLaunchManager) << "LaunchManager initialized successfully";
    return true;
}

QList<LaunchItem> LaunchManager::getAllItems() const
{
    QReadLocker locker(&m_lock);
    
    if (m_repository) {
        return m_repository->getAllItems();
    }
    
    return QList<LaunchItem>();
}

QList<LaunchItem> LaunchManager::getItemsByCategory(int categoryId) const
{
    QReadLocker locker(&m_lock);
    
    if (m_repository) {
        return m_repository->getItemsByCategory(categoryId);
    }
    
    return QList<LaunchItem>();
}

LaunchItem LaunchManager::getItem(int id) const
{
    QReadLocker locker(&m_lock);
    
    if (m_repository) {
        return m_repository->getItem(id);
    }
    
    return LaunchItem();
}

LaunchItem LaunchManager::getItemByPath(const QString &path) const
{
    QReadLocker locker(&m_lock);
    
    if (m_repository) {
        return m_repository->getItemByPath(path);
    }
    
    return LaunchItem();
}

bool LaunchManager::addItem(LaunchItem &item)
{
    QWriteLocker locker(&m_lock);
    
    if (!m_repository) {
        return false;
    }
    
    // 更新文件信息
    updateItemInfo(item);
    
    // 添加到数据库
    bool success = m_repository->addItem(item);
    
    if (success) {
        // 添加文件监控
        if (m_fileWatchingEnabled && item.type != LaunchItemType::Url && item.type != LaunchItemType::Command) {
            m_fileWatcher->addPath(item.path);
        }
        
        emit itemAdded(item);
        qCDebug(lcLaunchManager) << "Item added:" << item.name;
    }
    
    return success;
}

int LaunchManager::addItems(QList<LaunchItem> &items)
{
    if (items.isEmpty()) {
        return 0;
    }
    
    QWriteLocker locker(&m_lock);
    
    if (!m_repository) {
        return 0;
    }
    
    // 更新所有项目的文件信息
    for (LaunchItem &item : items) {
        updateItemInfo(item);
    }
    
    // 批量添加
    int successCount = m_repository->addItems(items);
    
    if (successCount > 0) {
        // 添加文件监控
        if (m_fileWatchingEnabled) {
            for (const LaunchItem &item : items) {
                if (item.id > 0 && item.type != LaunchItemType::Url && item.type != LaunchItemType::Command) {
                    m_fileWatcher->addPath(item.path);
                }
            }
        }
        
        qCInfo(lcLaunchManager) << "Batch added" << successCount << "items";
    }
    
    return successCount;
}

bool LaunchManager::updateItem(const LaunchItem &item)
{
    QWriteLocker locker(&m_lock);
    
    if (!m_repository) {
        return false;
    }
    
    bool success = m_repository->updateItem(item);
    
    if (success) {
        emit itemUpdated(item);
        qCDebug(lcLaunchManager) << "Item updated:" << item.name;
    }
    
    return success;
}

bool LaunchManager::removeItem(int id)
{
    QWriteLocker locker(&m_lock);
    
    if (!m_repository) {
        return false;
    }
    
    // 获取项目信息用于移除文件监控
    LaunchItem item = m_repository->getItem(id);
    
    bool success = m_repository->removeItem(id);
    
    if (success) {
        // 移除文件监控
        if (m_fileWatchingEnabled && item.isValid()) {
            m_fileWatcher->removePath(item.path);
        }
        
        // 终止相关进程
        auto processIt = m_runningProcesses.find(id);
        if (processIt != m_runningProcesses.end()) {
            if (processIt->second && processIt->second->state() != QProcess::NotRunning) {
                processIt->second->terminate();
            }
            m_runningProcesses.erase(processIt);
        }
        
        emit itemRemoved(id);
        qCDebug(lcLaunchManager) << "Item removed:" << id;
    }
    
    return success;
}

int LaunchManager::removeItems(const QList<int> &ids)
{
    if (ids.isEmpty()) {
        return 0;
    }
    
    QWriteLocker locker(&m_lock);
    
    if (!m_repository) {
        return 0;
    }
    
    // 获取所有项目信息
    QList<LaunchItem> items;
    for (int id : ids) {
        LaunchItem item = m_repository->getItem(id);
        if (item.isValid()) {
            items.append(item);
        }
    }
    
    // 批量删除
    int successCount = m_repository->removeItems(ids);
    
    if (successCount > 0) {
        // 移除文件监控和终止进程
        for (const LaunchItem &item : items) {
            if (m_fileWatchingEnabled) {
                m_fileWatcher->removePath(item.path);
            }
            
            auto processIt = m_runningProcesses.find(item.id);
            if (processIt != m_runningProcesses.end()) {
                if (processIt->second && processIt->second->state() != QProcess::NotRunning) {
                    processIt->second->terminate();
                }
                m_runningProcesses.erase(processIt);
            }
        }
        
        qCInfo(lcLaunchManager) << "Batch removed" << successCount << "items";
    }
    
    return successCount;
}

LaunchResult LaunchManager::launchItem(int id, const LaunchOptions &options)
{
    QReadLocker locker(&m_lock);
    
    if (!m_repository) {
        return LaunchResult::SystemError;
    }
    
    LaunchItem item = m_repository->getItem(id);
    if (!item.isValid()) {
        qCWarning(lcLaunchManager) << "Invalid item for launch:" << id;
        return LaunchResult::FileNotFound;
    }
    
    // 验证启动项
    LaunchResult validationResult = validateItemForLaunch(item);
    if (validationResult != LaunchResult::Success) {
        return validationResult;
    }
    
    QElapsedTimer timer;
    timer.start();
    
    // 执行启动
    LaunchResult result = executeItem(item, options);
    
    qint64 launchTime = timer.elapsed();
    
    // 记录使用情况
    if (result == LaunchResult::Success) {
        recordUsage(id, "direct", static_cast<int>(launchTime), true);
        emit itemLaunched(id, true);
    } else {
        emit itemLaunched(id, false);
    }
    
    // 更新统计
    m_stats.totalLaunches++;
    if (result == LaunchResult::Success) {
        m_stats.successfulLaunches++;
    } else {
        m_stats.failedLaunches++;
        m_stats.errorStats[result]++;
    }
    m_stats.successRate = static_cast<double>(m_stats.successfulLaunches) / m_stats.totalLaunches;
    m_stats.avgLaunchTime = (m_stats.avgLaunchTime * (m_stats.totalLaunches - 1) + launchTime) / m_stats.totalLaunches;
    m_stats.lastLaunch = QDateTime::currentDateTime();
    
    LaunchError error;
    error.result = result;
    emit itemLaunchCompleted(id, result, error);
    
    qCDebug(lcLaunchManager) << "Launch completed for item" << id << "result:" << static_cast<int>(result) << "time:" << launchTime << "ms";
    
    return result;
}

LaunchResult LaunchManager::launchItemByPath(const QString &path, const LaunchOptions &options)
{
    LaunchItem item = getItemByPath(path);
    if (item.isValid()) {
        return launchItem(item.id, options);
    }

    // 如果没有找到对应的启动项，尝试直接启动
    LaunchItem tempItem;
    tempItem.name = QFileInfo(path).baseName();
    tempItem.path = path;
    tempItem.type = LaunchItemType::Application;

    return executeItem(tempItem, options);
}

void LaunchManager::launchItemAsync(int id, const LaunchOptions &options)
{
    QtConcurrent::run([this, id, options]() {
        launchItem(id, options);
    });
}

bool LaunchManager::isItemRunning(int id) const
{
    QReadLocker locker(&m_lock);

    auto it = m_runningProcesses.find(id);
    if (it != m_runningProcesses.end() && it->second) {
        return it->second->state() == QProcess::Running;
    }

    return false;
}

bool LaunchManager::terminateItem(int id, bool force)
{
    QWriteLocker locker(&m_lock);

    auto it = m_runningProcesses.find(id);
    if (it == m_runningProcesses.end() || !it->second) {
        return false;
    }

    QProcess *process = it->second.get();

    if (process->state() == QProcess::NotRunning) {
        return true;
    }

    if (force) {
        process->kill();
    } else {
        process->terminate();
        if (!process->waitForFinished(5000)) {
            process->kill();
        }
    }

    qCDebug(lcLaunchManager) << "Process terminated for item:" << id << "force:" << force;
    return true;
}

void LaunchManager::recordUsage(int id, const QString &method, int responseTime, bool success)
{
    if (!m_repository) {
        return;
    }

    LaunchItem item = m_repository->getItem(id);
    if (!item.isValid()) {
        return;
    }

    // 更新使用统计
    item.recordUsage(responseTime);

    // 更新数据库
    m_repository->updateItem(item);

    emit usageRecorded(id);
    qCDebug(lcLaunchManager) << "Usage recorded for item:" << id << "method:" << method << "time:" << responseTime;
}

QList<LaunchItem> LaunchManager::getMostUsedItems(int limit) const
{
    QReadLocker locker(&m_lock);

    if (m_repository) {
        return m_repository->getMostUsedItems(limit);
    }

    return QList<LaunchItem>();
}

QList<LaunchItem> LaunchManager::getRecentlyUsedItems(int limit) const
{
    QReadLocker locker(&m_lock);

    if (m_repository) {
        return m_repository->getRecentlyUsedItems(limit);
    }

    return QList<LaunchItem>();
}

LaunchStats LaunchManager::getStatistics() const
{
    QReadLocker locker(&m_lock);
    return m_stats;
}

QList<int> LaunchManager::validateItems() const
{
    QReadLocker locker(&m_lock);

    if (m_repository) {
        return m_repository->validateItems();
    }

    return QList<int>();
}

int LaunchManager::cleanupInvalidItems()
{
    QWriteLocker locker(&m_lock);

    if (m_repository) {
        return m_repository->cleanupInvalidItems();
    }

    return 0;
}

int LaunchManager::updateFileInfo(const QList<int> &ids)
{
    QWriteLocker locker(&m_lock);

    if (!m_repository) {
        return 0;
    }

    QList<int> targetIds = ids;
    if (targetIds.isEmpty()) {
        // 更新所有项目
        QList<LaunchItem> allItems = m_repository->getAllItems();
        for (const LaunchItem &item : allItems) {
            targetIds.append(item.id);
        }
    }

    int updatedCount = 0;
    for (int id : targetIds) {
        LaunchItem item = m_repository->getItem(id);
        if (item.isValid()) {
            item.updateFileInfo();
            if (m_repository->updateItem(item)) {
                updatedCount++;
            }
        }
    }

    qCInfo(lcLaunchManager) << "Updated file info for" << updatedCount << "items";
    return updatedCount;
}

void LaunchManager::setFileSystemWatchingEnabled(bool enabled)
{
    QWriteLocker locker(&m_lock);

    m_fileWatchingEnabled = enabled;

    if (!enabled) {
        // 移除所有监控路径
        QStringList watchedPaths = m_fileWatcher->files() + m_fileWatcher->directories();
        if (!watchedPaths.isEmpty()) {
            m_fileWatcher->removePaths(watchedPaths);
        }
    } else {
        // 重新添加监控路径
        if (m_repository) {
            QList<LaunchItem> items = m_repository->getAllItems();
            for (const LaunchItem &item : items) {
                if (item.type != LaunchItemType::Url && item.type != LaunchItemType::Command) {
                    m_fileWatcher->addPath(item.path);
                }
            }
        }
    }

    qCDebug(lcLaunchManager) << "File system watching" << (enabled ? "enabled" : "disabled");
}

bool LaunchManager::isFileSystemWatchingEnabled() const
{
    QReadLocker locker(&m_lock);
    return m_fileWatchingEnabled;
}

void LaunchManager::setAutoCleanupInterval(int intervalMs)
{
    if (m_cleanupTimer) {
        m_cleanupTimer->setInterval(intervalMs);
        qCDebug(lcLaunchManager) << "Auto cleanup interval set to:" << intervalMs << "ms";
    }
}

void LaunchManager::onFileSystemChanged(const QString &path)
{
    qCDebug(lcLaunchManager) << "File system changed:" << path;

    // 查找受影响的启动项
    if (m_repository) {
        LaunchItem item = m_repository->getItemByPath(path);
        if (item.isValid()) {
            // 更新文件信息
            item.updateFileInfo();
            m_repository->updateItem(item);

            emit itemUpdated(item);
        }
    }

    emit fileSystemChanged(path);
}

void LaunchManager::onAutoCleanup()
{
    qCDebug(lcLaunchManager) << "Performing auto cleanup";

    // 清理无效项目
    int cleanedCount = cleanupInvalidItems();

    // 清理已完成的进程
    auto it = m_runningProcesses.begin();
    while (it != m_runningProcesses.end()) {
        if (!it->second || it->second->state() == QProcess::NotRunning) {
            it = m_runningProcesses.erase(it);
        } else {
            ++it;
        }
    }

    qCDebug(lcLaunchManager) << "Auto cleanup completed, cleaned" << cleanedCount << "invalid items";
}

void LaunchManager::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    QProcess *process = qobject_cast<QProcess*>(sender());
    if (!process) {
        return;
    }

    // 查找对应的启动项ID
    int itemId = -1;
    for (auto it = m_runningProcesses.begin(); it != m_runningProcesses.end(); ++it) {
        if (it->second.get() == process) {
            itemId = it->first;
            break;
        }
    }

    qCDebug(lcLaunchManager) << "Process finished for item:" << itemId
                            << "exitCode:" << exitCode
                            << "exitStatus:" << exitStatus;

    // 移除进程引用
    if (itemId != -1) {
        m_runningProcesses.erase(itemId);
    }
}

void LaunchManager::onProcessError(QProcess::ProcessError error)
{
    QProcess *process = qobject_cast<QProcess*>(sender());
    if (!process) {
        return;
    }

    qCWarning(lcLaunchManager) << "Process error:" << error << "details:" << process->errorString();
}

LaunchResult LaunchManager::executeItem(const LaunchItem &item, const LaunchOptions &options)
{
    qCDebug(lcLaunchManager) << "Executing item:" << item.name << "path:" << item.path;

    switch (item.type) {
    case LaunchItemType::Application:
    case LaunchItemType::Script:
        return executeApplication(item, options);

    case LaunchItemType::Document:
        return executeDocument(item, options);

    case LaunchItemType::Folder:
        return executeFolder(item, options);

    case LaunchItemType::Url:
        return executeUrl(item, options);

    case LaunchItemType::Command:
        return executeCommand(item, options);

    default:
        return LaunchResult::InvalidPath;
    }
}

LaunchResult LaunchManager::executeApplication(const LaunchItem &item, const LaunchOptions &options)
{
    if (!QFileInfo::exists(item.path)) {
        return LaunchResult::FileNotFound;
    }

    if (!isValidExecutable(item.path)) {
        return LaunchResult::InvalidPath;
    }

    auto process = createProcess(item, options);
    if (!process) {
        return LaunchResult::SystemError;
    }

    // 设置程序和参数
    QString program = item.path;
    QStringList arguments;

    // 合并启动项参数和选项参数
    if (!item.arguments.isEmpty()) {
        arguments.append(item.arguments.split(' ', Qt::SkipEmptyParts));
    }
    if (!options.arguments.isEmpty()) {
        arguments.append(options.arguments.split(' ', Qt::SkipEmptyParts));
    }

    // 设置工作目录
    QString workingDir = options.workingDirectory.isEmpty() ? item.workingDirectory : options.workingDirectory;
    if (workingDir.isEmpty()) {
        workingDir = QFileInfo(item.path).absolutePath();
    }
    process->setWorkingDirectory(workingDir);

    // 启动进程
    if (options.detached) {
        qint64 pid;
        bool success = process->startDetached(program, arguments, workingDir, &pid);
        if (success) {
            qCDebug(lcLaunchManager) << "Process started detached, PID:" << pid;
            return LaunchResult::Success;
        } else {
            qCWarning(lcLaunchManager) << "Failed to start detached process:" << process->errorString();
            return LaunchResult::SystemError;
        }
    } else {
        process->start(program, arguments);

        if (!process->waitForStarted(options.timeout)) {
            qCWarning(lcLaunchManager) << "Process failed to start:" << process->errorString();
            return LaunchResult::Timeout;
        }

        // 存储进程引用
        m_runningProcesses[item.id] = std::move(process);

        qCDebug(lcLaunchManager) << "Process started successfully";
        return LaunchResult::Success;
    }
}

LaunchResult LaunchManager::executeDocument(const LaunchItem &item, const LaunchOptions &options)
{
    Q_UNUSED(options)

    if (!QFileInfo::exists(item.path)) {
        return LaunchResult::FileNotFound;
    }

    // 使用系统默认程序打开文档
    bool success = QDesktopServices::openUrl(QUrl::fromLocalFile(item.path));

    if (success) {
        qCDebug(lcLaunchManager) << "Document opened successfully:" << item.path;
        return LaunchResult::Success;
    } else {
        qCWarning(lcLaunchManager) << "Failed to open document:" << item.path;
        return LaunchResult::SystemError;
    }
}

LaunchResult LaunchManager::executeFolder(const LaunchItem &item, const LaunchOptions &options)
{
    Q_UNUSED(options)

    if (!QDir(item.path).exists()) {
        return LaunchResult::FileNotFound;
    }

    // 使用系统文件管理器打开文件夹
    bool success = QDesktopServices::openUrl(QUrl::fromLocalFile(item.path));

    if (success) {
        qCDebug(lcLaunchManager) << "Folder opened successfully:" << item.path;
        return LaunchResult::Success;
    } else {
        qCWarning(lcLaunchManager) << "Failed to open folder:" << item.path;
        return LaunchResult::SystemError;
    }
}

LaunchResult LaunchManager::executeUrl(const LaunchItem &item, const LaunchOptions &options)
{
    Q_UNUSED(options)

    QUrl url(item.path);
    if (!url.isValid()) {
        return LaunchResult::InvalidPath;
    }

    // 使用系统默认浏览器打开URL
    bool success = QDesktopServices::openUrl(url);

    if (success) {
        qCDebug(lcLaunchManager) << "URL opened successfully:" << item.path;
        return LaunchResult::Success;
    } else {
        qCWarning(lcLaunchManager) << "Failed to open URL:" << item.path;
        return LaunchResult::SystemError;
    }
}

LaunchResult LaunchManager::executeCommand(const LaunchItem &item, const LaunchOptions &options)
{
    auto process = createProcess(item, options);
    if (!process) {
        return LaunchResult::SystemError;
    }

    // 设置工作目录
    QString workingDir = options.workingDirectory.isEmpty() ? item.workingDirectory : options.workingDirectory;
    if (!workingDir.isEmpty()) {
        process->setWorkingDirectory(workingDir);
    }

    // 执行命令
    QString command = item.path;
    if (!options.arguments.isEmpty()) {
        command += " " + options.arguments;
    }

    if (options.detached) {
        bool success = process->startDetached(command);
        if (success) {
            qCDebug(lcLaunchManager) << "Command started detached:" << command;
            return LaunchResult::Success;
        } else {
            qCWarning(lcLaunchManager) << "Failed to start detached command:" << process->errorString();
            return LaunchResult::SystemError;
        }
    } else {
        process->start(command);

        if (!process->waitForStarted(options.timeout)) {
            qCWarning(lcLaunchManager) << "Command failed to start:" << process->errorString();
            return LaunchResult::Timeout;
        }

        // 存储进程引用
        m_runningProcesses[item.id] = std::move(process);

        qCDebug(lcLaunchManager) << "Command started successfully:" << command;
        return LaunchResult::Success;
    }
}

std::unique_ptr<QProcess> LaunchManager::createProcess(const LaunchItem &item, const LaunchOptions &options)
{
    auto process = std::make_unique<QProcess>();

    // 连接信号
    connect(process.get(), QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &LaunchManager::onProcessFinished);
    connect(process.get(), &QProcess::errorOccurred,
            this, &LaunchManager::onProcessError);

    // 设置环境变量
    if (!options.environment.isEmpty()) {
        QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
        for (auto it = options.environment.begin(); it != options.environment.end(); ++it) {
            env.insert(it.key(), it.value().toString());
        }
        process->setProcessEnvironment(env);
    }

    return process;
}

LaunchResult LaunchManager::validateItemForLaunch(const LaunchItem &item) const
{
    if (!item.isValid()) {
        return LaunchResult::InvalidPath;
    }

    if (!item.isEnabled) {
        return LaunchResult::Cancelled;
    }

    // 检查文件是否存在
    if (!item.fileExists()) {
        return LaunchResult::FileNotFound;
    }

    return LaunchResult::Success;
}

void LaunchManager::updateItemInfo(LaunchItem &item)
{
    // 更新文件信息
    item.updateFileInfo();

    // 设置默认值
    if (item.createdAt.isNull()) {
        item.createdAt = QDateTime::currentDateTime();
    }
    item.updatedAt = QDateTime::currentDateTime();

    if (item.source.isEmpty()) {
        item.source = "manual";
    }
}

bool LaunchManager::isValidExecutable(const QString &path) const
{
    QFileInfo fileInfo(path);

    if (!fileInfo.exists() || !fileInfo.isFile()) {
        return false;
    }

    // 检查文件权限
    if (!fileInfo.isExecutable()) {
        return false;
    }

    // 检查文件扩展名（Windows）
#ifdef Q_OS_WIN
    QString suffix = fileInfo.suffix().toLower();
    QStringList executableExtensions = {"exe", "com", "bat", "cmd", "msi"};
    if (!executableExtensions.contains(suffix)) {
        return false;
    }
#endif

    return true;
}

int LaunchManager::scanDirectory(const QString &directory, bool recursive, int categoryId)
{
    QDir dir(directory);
    if (!dir.exists()) {
        qCWarning(lcLaunchManager) << "Directory does not exist:" << directory;
        return 0;
    }

    QStringList nameFilters;
#ifdef Q_OS_WIN
    nameFilters << "*.exe" << "*.lnk" << "*.bat" << "*.cmd";
#else
    nameFilters << "*";
#endif

    QDir::Filters filters = QDir::Files | QDir::Readable;
    if (recursive) {
        filters |= QDir::AllDirs | QDir::NoDotAndDotDot;
    }

    QFileInfoList entries = dir.entryInfoList(nameFilters, filters);
    QList<LaunchItem> newItems;

    for (const QFileInfo &entry : entries) {
        if (entry.isDir() && recursive) {
            // 递归扫描子目录
            int subCount = scanDirectory(entry.absoluteFilePath(), true, categoryId);
            qCDebug(lcLaunchManager) << "Scanned subdirectory:" << entry.fileName() << "found:" << subCount;
            continue;
        }

        if (entry.isFile() && isValidExecutable(entry.absoluteFilePath())) {
            // 检查是否已存在
            LaunchItem existing = getItemByPath(entry.absoluteFilePath());
            if (existing.isValid()) {
                continue; // 跳过已存在的项目
            }

            // 创建新的启动项
            LaunchItem item;
            item.name = entry.baseName();
            item.path = entry.absoluteFilePath();
            item.type = LaunchItemType::Application;
            item.categoryId = categoryId;
            item.source = "scan";

            newItems.append(item);
        }
    }

    // 批量添加新项目
    int addedCount = addItems(newItems);

    qCInfo(lcLaunchManager) << "Directory scan completed:" << directory
                           << "found:" << newItems.size()
                           << "added:" << addedCount;

    return addedCount;
}

bool LaunchManager::exportItems(const QString &filePath, const QString &format, const QList<int> &ids)
{
    if (!m_repository) {
        return false;
    }

    return m_repository->exportItems(filePath, format, ids);
}

int LaunchManager::importItems(const QString &filePath, const QString &format)
{
    if (!m_repository) {
        return 0;
    }

    return m_repository->importItems(filePath, format, "merge");
}
