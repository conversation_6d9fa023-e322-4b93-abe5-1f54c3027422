#!/usr/bin/env python3
"""
KK QuickLaunch 项目状态检查脚本
检查项目文件完整性和代码统计
"""

import os
import sys
from pathlib import Path
from collections import defaultdict

def count_lines_in_file(file_path):
    """统计文件行数"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return len(f.readlines())
    except:
        return 0

def get_file_size(file_path):
    """获取文件大小（KB）"""
    try:
        return os.path.getsize(file_path) / 1024
    except:
        return 0

def analyze_project():
    """分析项目结构和统计信息"""
    
    project_root = Path('.')
    
    # 文件类型统计
    file_stats = defaultdict(lambda: {'count': 0, 'lines': 0, 'size': 0})
    
    # 目录结构检查
    expected_dirs = [
        'src/application',
        'src/business/entities',
        'src/business/managers', 
        'src/data/repositories',
        'src/infrastructure/database',
        'src/infrastructure/utils',
        'src/presentation/views',
        'src/presentation/viewmodels',
        'tests/unit',
        'docs',
        'resources'
    ]
    
    print("🏗️  KK QuickLaunch 项目状态检查")
    print("=" * 50)
    
    # 检查目录结构
    print("\n📁 目录结构检查:")
    missing_dirs = []
    for dir_path in expected_dirs:
        full_path = project_root / dir_path
        if full_path.exists():
            print(f"  ✅ {dir_path}")
        else:
            print(f"  ❌ {dir_path} (缺失)")
            missing_dirs.append(dir_path)
    
    # 扫描所有文件
    print("\n📊 文件统计:")
    
    for file_path in project_root.rglob('*'):
        if file_path.is_file() and not any(part.startswith('.') for part in file_path.parts):
            suffix = file_path.suffix.lower()
            if suffix in ['.cpp', '.h', '.py', '.md', '.txt', '.cmake', '.qrc', '.ui', '.qml']:
                lines = count_lines_in_file(file_path)
                size = get_file_size(file_path)
                
                file_stats[suffix]['count'] += 1
                file_stats[suffix]['lines'] += lines
                file_stats[suffix]['size'] += size
    
    # 显示统计结果
    total_files = sum(stats['count'] for stats in file_stats.values())
    total_lines = sum(stats['lines'] for stats in file_stats.values())
    total_size = sum(stats['size'] for stats in file_stats.values())
    
    print(f"\n文件类型统计:")
    for ext, stats in sorted(file_stats.items()):
        print(f"  {ext:8} : {stats['count']:3d} 个文件, {stats['lines']:6d} 行, {stats['size']:6.1f} KB")
    
    print(f"\n总计: {total_files} 个文件, {total_lines} 行代码, {total_size:.1f} KB")
    
    # 核心文件检查
    print("\n🔍 核心文件检查:")
    
    core_files = [
        'src/main.cpp',
        'src/application/Application.h',
        'src/application/Application.cpp',
        'src/application/ServiceContainer.h',
        'src/application/ServiceContainer.cpp',
        'src/infrastructure/utils/Logger.h',
        'src/infrastructure/utils/Logger.cpp',
        'src/infrastructure/database/DatabaseManager.h',
        'src/infrastructure/database/DatabaseManager.cpp',
        'src/business/entities/LaunchItem.h',
        'src/business/entities/LaunchItem.cpp',
        'src/data/repositories/LaunchItemRepository.h',
        'src/data/repositories/LaunchItemRepository.cpp',
        'src/business/managers/ConfigManager.h',
        'src/business/managers/ConfigManager.cpp',
        'src/presentation/viewmodels/BaseViewModel.h',
        'src/presentation/viewmodels/BaseViewModel.cpp',
        'CMakeLists.txt',
        'README.md'
    ]
    
    implemented_files = 0
    for file_path in core_files:
        full_path = project_root / file_path
        if full_path.exists():
            lines = count_lines_in_file(full_path)
            print(f"  ✅ {file_path} ({lines} 行)")
            implemented_files += 1
        else:
            print(f"  ❌ {file_path} (缺失)")
    
    completion_rate = (implemented_files / len(core_files)) * 100
    print(f"\n核心文件完成度: {implemented_files}/{len(core_files)} ({completion_rate:.1f}%)")
    
    # 架构层级分析
    print("\n🏛️  架构层级分析:")
    
    layers = {
        'Application Layer': ['src/application'],
        'Presentation Layer': ['src/presentation'],
        'Business Layer': ['src/business'],
        'Data Layer': ['src/data'],
        'Infrastructure Layer': ['src/infrastructure'],
        'Tests': ['tests']
    }
    
    for layer_name, layer_paths in layers.items():
        layer_files = 0
        layer_lines = 0
        
        for layer_path in layer_paths:
            layer_dir = project_root / layer_path
            if layer_dir.exists():
                for file_path in layer_dir.rglob('*'):
                    if file_path.is_file() and file_path.suffix in ['.cpp', '.h']:
                        layer_files += 1
                        layer_lines += count_lines_in_file(file_path)
        
        print(f"  {layer_name:20} : {layer_files:3d} 文件, {layer_lines:6d} 行")
    
    # 进度评估
    print("\n📈 项目进度评估:")
    
    # 基于文件数量和代码行数的简单评估
    estimated_total_files = 60  # 预估最终文件数
    estimated_total_lines = 15000  # 预估最终代码行数
    
    file_progress = min((total_files / estimated_total_files) * 100, 100)
    line_progress = min((total_lines / estimated_total_lines) * 100, 100)
    
    print(f"  文件完成度: {file_progress:.1f}% ({total_files}/{estimated_total_files})")
    print(f"  代码完成度: {line_progress:.1f}% ({total_lines}/{estimated_total_lines})")
    print(f"  总体进度: {(file_progress + line_progress) / 2:.1f}%")
    
    # 下一步建议
    print("\n🎯 下一步建议:")
    
    if missing_dirs:
        print("  1. 创建缺失的目录结构")
    
    if completion_rate < 80:
        print("  2. 完成核心组件的具体实现")
    
    if total_lines < 5000:
        print("  3. 实现更多业务逻辑和UI组件")
    
    print("  4. 编写单元测试")
    print("  5. 完善文档和注释")
    
    return {
        'total_files': total_files,
        'total_lines': total_lines,
        'completion_rate': completion_rate,
        'file_progress': file_progress,
        'line_progress': line_progress
    }

if __name__ == '__main__':
    try:
        stats = analyze_project()
        print(f"\n✨ 检查完成! 项目整体进度: {(stats['file_progress'] + stats['line_progress']) / 2:.1f}%")
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        sys.exit(1)
