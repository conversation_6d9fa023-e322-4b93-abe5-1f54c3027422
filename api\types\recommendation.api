syntax = "v1"

// ============================================================================
// 推荐系统相关类型定义
// ============================================================================

// 应用推荐信息
type AppRecommendation {
    AppId       string  `json:"app_id"`       // 应用ID
    Name        string  `json:"name"`         // 应用名称
    Description string  `json:"description"`  // 应用描述
    Category    string  `json:"category"`     // 应用分类
    Icon        string  `json:"icon"`         // 图标URL
    DownloadUrl string  `json:"download_url"` // 下载链接
    Score       float64 `json:"score"`        // 推荐分数(0.0-1.0)
    Confidence  float64 `json:"confidence"`   // 置信度(0.0-1.0)
    Reasons     []string `json:"reasons"`     // 推荐理由
    Algorithm   string  `json:"algorithm"`    // 推荐算法: content_based/collaborative/context_aware/hybrid
    Metadata    struct {
        Size            string   `json:"size,optional"`             // 文件大小
        Version         string   `json:"version,optional"`          // 版本号
        Rating          float64  `json:"rating,optional"`           // 用户评分
        Downloads       int64    `json:"downloads,optional"`        // 下载次数
        Tags            []string `json:"tags,optional"`             // 标签
        SupportedFormats []string `json:"supported_formats,optional"` // 支持的文件格式
        SystemRequirements struct {
            OS     string `json:"os,optional"`     // 操作系统要求
            Memory string `json:"memory,optional"` // 内存要求
            Disk   string `json:"disk,optional"`   // 磁盘空间要求
        } `json:"system_requirements,optional"` // 系统要求
        License     string `json:"license,optional"`      // 许可证类型
        Developer   string `json:"developer,optional"`    // 开发商
        Website     string `json:"website,optional"`      // 官方网站
        LastUpdated string `json:"last_updated,optional"` // 最后更新时间
    } `json:"metadata"`
}

// 推荐上下文
type RecommendationContext {
    FileType         string   `json:"file_type,optional"`         // 当前文件类型
    ClipboardContent string   `json:"clipboard_content,optional"` // 剪贴板内容
    ActiveWindow     string   `json:"active_window,optional"`     // 当前活动窗口
    TimeOfDay        string   `json:"time_of_day,optional"`       // 时间段: morning/afternoon/evening/night
    WorkingDirectory string   `json:"working_directory,optional"` // 工作目录
    RecentFiles      []string `json:"recent_files,optional"`      // 最近文件列表
    UserPreferences  struct {
        Categories   []string `json:"categories,optional"`   // 偏好分类
        ExcludeApps  []string `json:"exclude_apps,optional"` // 排除应用
        Language     string   `json:"language,optional"`     // 语言偏好
        PriceRange   string   `json:"price_range,optional"`  // 价格范围: free/paid/any
        LicenseType  string   `json:"license_type,optional"` // 许可证类型偏好
    } `json:"user_preferences,optional"` // 用户偏好
    DeviceInfo struct {
        Platform     string `json:"platform,optional"`     // 操作系统平台
        Architecture string `json:"architecture,optional"` // 系统架构
        Memory       int64  `json:"memory,optional"`       // 系统内存(MB)
        DiskSpace    int64  `json:"disk_space,optional"`   // 可用磁盘空间(MB)
    } `json:"device_info,optional"` // 设备信息
}

// 获取推荐请求
type GetRecommendationsReq {
    Context         RecommendationContext `json:"context"`                              // 推荐上下文
    MaxResults      int                   `json:"max_results,optional" validate:"min=1,max=50"` // 最大结果数
    IncludeReasons  bool                  `json:"include_reasons,optional"`            // 是否包含推荐理由
    AlgorithmWeights struct {
        ContentBased    float64 `json:"content_based,optional"`    // 基于内容推荐权重
        Collaborative   float64 `json:"collaborative,optional"`    // 协同过滤推荐权重
        ContextAware    float64 `json:"context_aware,optional"`    // 上下文感知推荐权重
        Hybrid          float64 `json:"hybrid,optional"`           // 混合推荐权重
    } `json:"algorithm_weights,optional"` // 算法权重配置
    Filters struct {
        MinScore     float64  `json:"min_score,optional"`     // 最小推荐分数
        Categories   []string `json:"categories,optional"`    // 分类过滤
        PriceType    string   `json:"price_type,optional"`    // 价格类型: free/paid/freemium
        Rating       float64  `json:"rating,optional"`        // 最低评分
        Platform     string   `json:"platform,optional"`      // 平台过滤
    } `json:"filters,optional"` // 过滤条件
}

// 获取推荐响应
type GetRecommendationsResp {
    BaseResp
    Data []AppRecommendation `json:"data"`    // 推荐列表
    Meta struct {
        TotalCandidates int     `json:"total_candidates"` // 候选应用总数
        ProcessingTime  int     `json:"processing_time"`  // 处理时间(毫秒)
        AlgorithmsUsed  []string `json:"algorithms_used"` // 使用的算法
        ContextHash     string  `json:"context_hash"`     // 上下文哈希(用于缓存)
        CacheHit        bool    `json:"cache_hit"`        // 是否命中缓存
    } `json:"meta"`
}

// 热门应用请求
type GetTrendingAppsReq {
    Category    string `form:"category,optional"`    // 分类过滤
    TimeRange   string `form:"time_range,optional"`  // 时间范围: 1d/7d/30d
    Platform    string `form:"platform,optional"`    // 平台过滤
    Limit       int    `form:"limit,optional" validate:"min=1,max=100"` // 限制数量
    Region      string `form:"region,optional"`      // 地区过滤
}

// 热门应用响应
type GetTrendingAppsResp {
    BaseResp
    Data []struct {
        AppRecommendation
        TrendScore   float64 `json:"trend_score"`    // 趋势分数
        GrowthRate   float64 `json:"growth_rate"`    // 增长率
        InstallCount int64   `json:"install_count"`  // 安装数量
        Rank         int     `json:"rank"`           // 排名
        RankChange   int     `json:"rank_change"`    // 排名变化
    } `json:"data"`
    Meta struct {
        UpdatedAt   string `json:"updated_at"`   // 数据更新时间
        TimeRange   string `json:"time_range"`   // 统计时间范围
        TotalApps   int    `json:"total_apps"`   // 总应用数
        Region      string `json:"region"`       // 统计地区
    } `json:"meta"`
}

// 相似应用请求
type GetSimilarAppsReq {
    AppId               string `path:"appId" validate:"required"`                    // 应用ID
    Limit               int    `form:"limit,optional" validate:"min=1,max=20"`      // 限制数量
    IncludeAlternatives bool   `form:"include_alternatives,optional"`               // 是否包含替代品
    SimilarityThreshold float64 `form:"similarity_threshold,optional" validate:"min=0,max=1"` // 相似度阈值
}

// 相似应用响应
type GetSimilarAppsResp {
    BaseResp
    Data []struct {
        AppRecommendation
        Similarity          float64  `json:"similarity"`           // 相似度分数
        ComparisonFeatures  []string `json:"comparison_features"`  // 对比特征
        DifferencePoints    []string `json:"difference_points"`    // 差异点
        IsAlternative       bool     `json:"is_alternative"`       // 是否为替代品
    } `json:"data"`
    SourceApp struct {
        AppId       string `json:"app_id"`       // 源应用ID
        Name        string `json:"name"`         // 源应用名称
        Category    string `json:"category"`     // 源应用分类
        Features    []string `json:"features"`   // 源应用特征
    } `json:"source_app"` // 源应用信息
}

// 推荐反馈请求
type RecordFeedbackReq {
    RecommendationId string  `json:"recommendation_id" validate:"required"`        // 推荐ID
    Action           string  `json:"action" validate:"required,oneof=viewed clicked installed dismissed rated"` // 用户行为
    Rating           int     `json:"rating,optional" validate:"min=1,max=5"`       // 评分(1-5)
    Feedback         string  `json:"feedback,optional" validate:"max=1000"`        // 文字反馈
    Context          struct {
        SessionId    string `json:"session_id,optional"`    // 会话ID
        Position     int    `json:"position,optional"`      // 推荐位置
        DisplayTime  int    `json:"display_time,optional"`  // 显示时长(秒)
        ClickTime    int    `json:"click_time,optional"`    // 点击时间(毫秒)
    } `json:"context,optional"` // 反馈上下文
    Metadata map[string]interface{} `json:"metadata,optional"` // 额外元数据
}

// 推荐规则
type RecommendationRule {
    Id          int64  `json:"id"`           // 规则ID
    Name        string `json:"name"`         // 规则名称
    Description string `json:"description"`  // 规则描述
    RuleType    string `json:"rule_type"`    // 规则类型: file_type/time/context/keyword/clipboard/window
    ConditionData map[string]interface{} `json:"condition_data"` // 条件数据(JSON格式)
    ActionData    map[string]interface{} `json:"action_data"`    // 动作数据(JSON格式)
    Priority      int    `json:"priority"`    // 规则优先级
    IsEnabled     bool   `json:"is_enabled"`  // 是否启用
    MatchCount    int64  `json:"match_count"` // 匹配次数统计
    SuccessCount  int64  `json:"success_count"` // 成功推荐次数
    SuccessRate   float64 `json:"success_rate"` // 成功率
    CreatedAt     string `json:"created_at"`  // 创建时间
    UpdatedAt     string `json:"updated_at"`  // 更新时间
}

// 获取推荐规则请求
type GetRecommendationRulesReq {
    RuleType  string `form:"rule_type,optional"`  // 规则类型过滤
    IsEnabled *bool  `form:"is_enabled,optional"` // 启用状态过滤
    Search    string `form:"search,optional"`     // 搜索关键词
    PageReq
    SortReq
}

// 获取推荐规则响应
type GetRecommendationRulesResp {
    BaseResp
    Data     []RecommendationRule `json:"data"`      // 推荐规则列表
    PageInfo PageInfo             `json:"page_info"` // 分页信息
    Stats    struct {
        Total        int64   `json:"total"`         // 总规则数
        Enabled      int64   `json:"enabled"`       // 启用规则数
        AvgSuccessRate float64 `json:"avg_success_rate"` // 平均成功率
        TopPerforming string `json:"top_performing"` // 表现最好的规则
    } `json:"stats"`
}

// 创建推荐规则请求
type CreateRecommendationRuleReq {
    Name        string `json:"name" validate:"required,min=1,max=100"`        // 规则名称
    Description string `json:"description,optional" validate:"max=500"`       // 规则描述
    RuleType    string `json:"rule_type" validate:"required,oneof=file_type time context keyword clipboard window"` // 规则类型
    ConditionData map[string]interface{} `json:"condition_data" validate:"required"` // 条件数据
    ActionData    map[string]interface{} `json:"action_data" validate:"required"`    // 动作数据
    Priority      int  `json:"priority,optional" validate:"min=0,max=100"`    // 规则优先级
    IsEnabled     bool `json:"is_enabled,optional"`                           // 是否启用
}

// 更新推荐规则请求
type UpdateRecommendationRuleReq {
    Id          int64  `path:"id" validate:"required,min=1"`                  // 规则ID
    Name        string `json:"name,optional" validate:"min=1,max=100"`        // 规则名称
    Description string `json:"description,optional" validate:"max=500"`       // 规则描述
    RuleType    string `json:"rule_type,optional" validate:"oneof=file_type time context keyword clipboard window"` // 规则类型
    ConditionData map[string]interface{} `json:"condition_data,optional"`     // 条件数据
    ActionData    map[string]interface{} `json:"action_data,optional"`        // 动作数据
    Priority      int  `json:"priority,optional" validate:"min=0,max=100"`    // 规则优先级
    IsEnabled     bool `json:"is_enabled,optional"`                           // 是否启用
}

// 删除推荐规则请求
type DeleteRecommendationRuleReq {
    Id int64 `path:"id" validate:"required,min=1"` // 规则ID
}

// 推荐规则响应
type RecommendationRuleResp {
    BaseResp
    Data RecommendationRule `json:"data"` // 推荐规则数据
}

// 测试推荐规则请求
type TestRecommendationRuleReq {
    Id      int64                 `json:"id,optional"`      // 规则ID(测试现有规则)
    Rule    RecommendationRule    `json:"rule,optional"`    // 规则数据(测试新规则)
    Context RecommendationContext `json:"context"`          // 测试上下文
}

// 测试推荐规则响应
type TestRecommendationRuleResp {
    BaseResp
    Data struct {
        Matched       bool                `json:"matched"`        // 是否匹配
        Recommendations []AppRecommendation `json:"recommendations"` // 推荐结果
        MatchDetails  struct {
            ConditionResults map[string]bool `json:"condition_results"` // 条件匹配结果
            ProcessingTime   int             `json:"processing_time"`   // 处理时间(毫秒)
            DebugInfo        []string        `json:"debug_info"`        // 调试信息
        } `json:"match_details"`
    } `json:"data"`
}

// 推荐效果统计请求
type GetRecommendationPerformanceReq {
    TimeRange   string `form:"time_range,optional"`   // 时间范围: 1d/7d/30d/90d
    Algorithm   string `form:"algorithm,optional"`    // 算法过滤
    Category    string `form:"category,optional"`     // 分类过滤
    MetricType  string `form:"metric_type,optional"`  // 指标类型: ctr/conversion/rating
}

// 推荐效果统计响应
type GetRecommendationPerformanceResp {
    BaseResp
    Data struct {
        Overall struct {
            TotalRecommendations int64   `json:"total_recommendations"` // 总推荐次数
            ClickThroughRate     float64 `json:"click_through_rate"`    // 点击率
            InstallationRate     float64 `json:"installation_rate"`     // 安装率
            AvgRating            float64 `json:"avg_rating"`            // 平均评分
            UserSatisfaction     float64 `json:"user_satisfaction"`     // 用户满意度
        } `json:"overall"` // 总体统计
        ByAlgorithm []struct {
            Algorithm        string  `json:"algorithm"`         // 算法名称
            Recommendations  int64   `json:"recommendations"`   // 推荐次数
            CTR              float64 `json:"ctr"`               // 点击率
            ConversionRate   float64 `json:"conversion_rate"`   // 转化率
            AvgScore         float64 `json:"avg_score"`         // 平均分数
            Performance      string  `json:"performance"`       // 性能评级: excellent/good/fair/poor
        } `json:"by_algorithm"` // 按算法统计
        ByCategory []struct {
            Category        string   `json:"category"`         // 分类名称
            Recommendations int64    `json:"recommendations"`  // 推荐次数
            CTR             float64  `json:"ctr"`              // 点击率
            PopularApps     []string `json:"popular_apps"`     // 热门应用
            TrendDirection  string   `json:"trend_direction"`  // 趋势方向: up/down/stable
        } `json:"by_category"` // 按分类统计
        Timeline []struct {
            Date            string  `json:"date"`             // 日期
            Recommendations int64   `json:"recommendations"`  // 推荐次数
            Clicks          int64   `json:"clicks"`           // 点击次数
            Installs        int64   `json:"installs"`         // 安装次数
            CTR             float64 `json:"ctr"`              // 点击率
            ConversionRate  float64 `json:"conversion_rate"`  // 转化率
        } `json:"timeline"` // 时间线数据
        TopPerforming []struct {
            AppId           string  `json:"app_id"`           // 应用ID
            AppName         string  `json:"app_name"`         // 应用名称
            Recommendations int64   `json:"recommendations"`  // 推荐次数
            CTR             float64 `json:"ctr"`              // 点击率
            ConversionRate  float64 `json:"conversion_rate"`  // 转化率
            AvgRating       float64 `json:"avg_rating"`       // 平均评分
        } `json:"top_performing"` // 表现最好的应用
    } `json:"data"`
}
