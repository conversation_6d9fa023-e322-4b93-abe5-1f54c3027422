syntax = "v1"

info(
    title: "智能启动助手API"
    desc: "KK QuickLaunch Backend API - 提供配置同步、软件推荐等服务"
    author: "开发团队"
    email: "<EMAIL>"
    version: "v1.0.0"
)

import "types/common.api"
import "types/auth.api"
import "types/launch_item.api"
import "types/category.api"
import "types/user_action.api"
import "types/recommendation.api"
import "types/search.api"
import "types/config.api"
import "types/analytics.api"

// ============================================================================
// 认证相关API
// ============================================================================
@server(
    group: auth
    prefix: /api/v1/auth
)
service quicklaunch-api {
    @doc "用户注册"
    @handler register
    post /register (RegisterReq) returns (AuthResp)
    
    @doc "用户登录"
    @handler login
    post /login (LoginReq) returns (AuthResp)
    
    @doc "刷新Token"
    @handler refresh
    post /refresh (RefreshTokenReq) returns (AuthResp)
    
    @doc "用户登出"
    @handler logout
    post /logout (LogoutReq) returns (BaseResp)
}

// ============================================================================
// 启动项管理API
// ============================================================================
@server(
    group: launchitem
    prefix: /api/v1/launch-items
    jwt: Auth
)
service quicklaunch-api {
    @doc "获取启动项列表"
    @handler getLaunchItems
    get / (GetLaunchItemsReq) returns (GetLaunchItemsResp)
    
    @doc "创建启动项"
    @handler createLaunchItem
    post / (CreateLaunchItemReq) returns (LaunchItemResp)
    
    @doc "获取启动项详情"
    @handler getLaunchItem
    get /:id (GetLaunchItemReq) returns (LaunchItemResp)
    
    @doc "更新启动项"
    @handler updateLaunchItem
    put /:id (UpdateLaunchItemReq) returns (LaunchItemResp)
    
    @doc "删除启动项"
    @handler deleteLaunchItem
    delete /:id (DeleteLaunchItemReq) returns (BaseResp)
    
    @doc "批量操作启动项"
    @handler batchOperateLaunchItems
    post /batch (BatchOperateLaunchItemsReq) returns (BaseResp)
    
    @doc "搜索启动项"
    @handler searchLaunchItems
    get /search (SearchLaunchItemsReq) returns (GetLaunchItemsResp)
    
    @doc "获取最近使用的启动项"
    @handler getRecentLaunchItems
    get /recent (GetRecentLaunchItemsReq) returns (GetLaunchItemsResp)
    
    @doc "记录启动项使用"
    @handler recordLaunchItemUsage
    post /:id/usage (RecordUsageReq) returns (BaseResp)
}

// ============================================================================
// 分类管理API
// ============================================================================
@server(
    group: category
    prefix: /api/v1/categories
    jwt: Auth
)
service quicklaunch-api {
    @doc "获取分类列表"
    @handler getCategories
    get / (GetCategoriesReq) returns (GetCategoriesResp)
    
    @doc "创建分类"
    @handler createCategory
    post / (CreateCategoryReq) returns (CategoryResp)
    
    @doc "更新分类"
    @handler updateCategory
    put /:id (UpdateCategoryReq) returns (CategoryResp)
    
    @doc "删除分类"
    @handler deleteCategory
    delete /:id (DeleteCategoryReq) returns (BaseResp)
    
    @doc "获取分类统计"
    @handler getCategoryStats
    get /stats (GetCategoryStatsReq) returns (GetCategoryStatsResp)
}

// ============================================================================
// 推荐系统API
// ============================================================================
@server(
    group: recommendation
    prefix: /api/v1/recommendations
    jwt: Auth
)
service quicklaunch-api {
    @doc "获取个性化推荐"
    @handler getRecommendations
    post /apps (GetRecommendationsReq) returns (GetRecommendationsResp)
    
    @doc "获取热门软件"
    @handler getTrendingApps
    get /trending (GetTrendingAppsReq) returns (GetTrendingAppsResp)
    
    @doc "获取相似软件推荐"
    @handler getSimilarApps
    get /similar/:appId (GetSimilarAppsReq) returns (GetSimilarAppsResp)
    
    @doc "记录推荐反馈"
    @handler recordRecommendationFeedback
    post /feedback (RecordFeedbackReq) returns (BaseResp)
    
    @doc "获取推荐规则"
    @handler getRecommendationRules
    get /rules (GetRecommendationRulesReq) returns (GetRecommendationRulesResp)
    
    @doc "创建推荐规则"
    @handler createRecommendationRule
    post /rules (CreateRecommendationRuleReq) returns (RecommendationRuleResp)
    
    @doc "更新推荐规则"
    @handler updateRecommendationRule
    put /rules/:id (UpdateRecommendationRuleReq) returns (RecommendationRuleResp)
    
    @doc "删除推荐规则"
    @handler deleteRecommendationRule
    delete /rules/:id (DeleteRecommendationRuleReq) returns (BaseResp)
}

// ============================================================================
// 配置同步API
// ============================================================================
@server(
    group: config
    prefix: /api/v1/config
    jwt: Auth
)
service quicklaunch-api {
    @doc "同步配置"
    @handler syncConfig
    post /sync (SyncConfigReq) returns (SyncConfigResp)
    
    @doc "获取配置"
    @handler getConfig
    get /sync (GetConfigReq) returns (GetConfigResp)
    
    @doc "解决配置冲突"
    @handler resolveConfigConflict
    post /resolve-conflict (ResolveConflictReq) returns (BaseResp)
    
    @doc "获取配置历史"
    @handler getConfigHistory
    get /history (GetConfigHistoryReq) returns (GetConfigHistoryResp)
    
    @doc "恢复历史版本"
    @handler restoreConfigVersion
    post /restore/:version (RestoreConfigReq) returns (BaseResp)
    
    @doc "获取应用设置"
    @handler getAppSettings
    get /settings (GetAppSettingsReq) returns (GetAppSettingsResp)
    
    @doc "更新应用设置"
    @handler updateAppSettings
    put /settings (UpdateAppSettingsReq) returns (BaseResp)
    
    @doc "获取快捷键设置"
    @handler getHotkeySettings
    get /hotkeys (GetHotkeySettingsReq) returns (GetHotkeySettingsResp)
    
    @doc "更新快捷键设置"
    @handler updateHotkeySettings
    put /hotkeys (UpdateHotkeySettingsReq) returns (BaseResp)
}

// ============================================================================
// 用户行为分析API
// ============================================================================
@server(
    group: analytics
    prefix: /api/v1/analytics
    jwt: Auth
)
service quicklaunch-api {
    @doc "上报用户行为事件"
    @handler reportEvents
    post /events (ReportEventsReq) returns (BaseResp)
    
    @doc "获取用户统计"
    @handler getUserStats
    get /user/stats (GetUserStatsReq) returns (GetUserStatsResp)
    
    @doc "获取使用趋势"
    @handler getUsageTrends
    get /trends (GetUsageTrendsReq) returns (GetUsageTrendsResp)
    
    @doc "获取推荐效果统计"
    @handler getRecommendationPerformance
    get /recommendations/performance (GetRecommendationPerformanceReq) returns (GetRecommendationPerformanceResp)
    
    @doc "获取搜索统计"
    @handler getSearchStats
    get /search/stats (GetSearchStatsReq) returns (GetSearchStatsResp)
    
    @doc "获取系统健康状态"
    @handler getSystemHealth
    get /system/health (GetSystemHealthReq) returns (GetSystemHealthResp)
}

// ============================================================================
// 搜索相关API
// ============================================================================
@server(
    group: search
    prefix: /api/v1/search
    jwt: Auth
)
service quicklaunch-api {
    @doc "全局搜索"
    @handler globalSearch
    get / (GlobalSearchReq) returns (GlobalSearchResp)
    
    @doc "搜索建议"
    @handler getSearchSuggestions
    get /suggestions (GetSearchSuggestionsReq) returns (GetSearchSuggestionsResp)
    
    @doc "获取搜索历史"
    @handler getSearchHistory
    get /history (GetSearchHistoryReq) returns (GetSearchHistoryResp)
    
    @doc "清空搜索历史"
    @handler clearSearchHistory
    delete /history (ClearSearchHistoryReq) returns (BaseResp)
    
    @doc "获取热门搜索"
    @handler getPopularSearches
    get /popular (GetPopularSearchesReq) returns (GetPopularSearchesResp)
}

// ============================================================================
// 文件关联API
// ============================================================================
@server(
    group: fileassoc
    prefix: /api/v1/file-associations
    jwt: Auth
)
service quicklaunch-api {
    @doc "获取文件关联"
    @handler getFileAssociations
    get / (GetFileAssociationsReq) returns (GetFileAssociationsResp)
    
    @doc "创建文件关联"
    @handler createFileAssociation
    post / (CreateFileAssociationReq) returns (FileAssociationResp)
    
    @doc "更新文件关联"
    @handler updateFileAssociation
    put /:id (UpdateFileAssociationReq) returns (FileAssociationResp)
    
    @doc "删除文件关联"
    @handler deleteFileAssociation
    delete /:id (DeleteFileAssociationReq) returns (BaseResp)
    
    @doc "根据文件扩展名获取推荐应用"
    @handler getAppsByFileExtension
    get /apps/:extension (GetAppsByFileExtensionReq) returns (GetAppsByFileExtensionResp)
}

// ============================================================================
// 插件管理API
// ============================================================================
@server(
    group: plugin
    prefix: /api/v1/plugins
    jwt: Auth
)
service quicklaunch-api {
    @doc "获取插件列表"
    @handler getPlugins
    get / (GetPluginsReq) returns (GetPluginsResp)
    
    @doc "安装插件"
    @handler installPlugin
    post /install (InstallPluginReq) returns (PluginResp)
    
    @doc "卸载插件"
    @handler uninstallPlugin
    delete /:id (UninstallPluginReq) returns (BaseResp)
    
    @doc "启用/禁用插件"
    @handler togglePlugin
    put /:id/toggle (TogglePluginReq) returns (BaseResp)
    
    @doc "获取插件配置"
    @handler getPluginConfig
    get /:id/config (GetPluginConfigReq) returns (GetPluginConfigResp)
    
    @doc "更新插件配置"
    @handler updatePluginConfig
    put /:id/config (UpdatePluginConfigReq) returns (BaseResp)
}

// ============================================================================
// 系统管理API
// ============================================================================
@server(
    group: system
    prefix: /api/v1/system
    jwt: Auth
)
service quicklaunch-api {
    @doc "获取系统信息"
    @handler getSystemInfo
    get /info (GetSystemInfoReq) returns (GetSystemInfoResp)
    
    @doc "获取系统日志"
    @handler getSystemLogs
    get /logs (GetSystemLogsReq) returns (GetSystemLogsResp)
    
    @doc "执行数据库维护"
    @handler performMaintenance
    post /maintenance (PerformMaintenanceReq) returns (BaseResp)
    
    @doc "创建数据备份"
    @handler createBackup
    post /backup (CreateBackupReq) returns (CreateBackupResp)
    
    @doc "恢复数据备份"
    @handler restoreBackup
    post /restore (RestoreBackupReq) returns (BaseResp)
    
    @doc "获取数据库统计"
    @handler getDatabaseStats
    get /database/stats (GetDatabaseStatsReq) returns (GetDatabaseStatsResp)
}

// ============================================================================
// WebSocket实时通信
// ============================================================================
@server(
    group: websocket
    prefix: /api/v1/ws
)
service quicklaunch-api {
    @doc "WebSocket连接"
    @handler websocketConnect
    get /connect (WebSocketConnectReq) returns (WebSocketConnectResp)
}
