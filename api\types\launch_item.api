syntax = "v1"

// ============================================================================
// 启动项相关类型定义
// ============================================================================

// 启动项基础信息
type LaunchItem {
    Id                int64    `json:"id"`                  // 启动项ID
    Name              string   `json:"name"`                // 应用显示名称
    Path              string   `json:"path"`                // 应用程序完整路径
    CategoryId        int64    `json:"category_id"`         // 分类ID
    CategoryName      string   `json:"category_name,optional"` // 分类名称
    IconData          string   `json:"icon_data,optional"`  // 图标数据(Base64)
    IconPath          string   `json:"icon_path,optional"`  // 图标文件路径
    Description       string   `json:"description"`         // 应用描述信息
    Tags              []string `json:"tags"`                // 标签数组
    Arguments         string   `json:"arguments"`           // 启动参数
    WorkingDirectory  string   `json:"working_directory"`   // 工作目录
    RunAsAdmin        bool     `json:"run_as_admin"`        // 是否以管理员身份运行
    Priority          int      `json:"priority"`            // 优先级
    IsEnabled         bool     `json:"is_enabled"`          // 是否启用
    UseCount          int64    `json:"use_count"`           // 使用次数统计
    LastUsed          string   `json:"last_used,optional"`  // 最后使用时间
    AvgResponseTime   int      `json:"avg_response_time"`   // 平均响应时间(毫秒)
    FileSize          int64    `json:"file_size"`           // 文件大小(字节)
    FileVersion       string   `json:"file_version"`        // 文件版本号
    CreatedAt         string   `json:"created_at"`          // 创建时间
    UpdatedAt         string   `json:"updated_at"`          // 更新时间
}

// 启动项统计信息
type LaunchItemStats {
    Id                int64   `json:"id"`                  // 启动项ID
    Name              string  `json:"name"`                // 应用名称
    UseCount          int64   `json:"use_count"`           // 使用次数
    LastUsed          string  `json:"last_used,optional"`  // 最后使用时间
    AvgResponseTime   int     `json:"avg_response_time"`   // 平均响应时间
    TotalActions      int64   `json:"total_actions"`       // 总操作次数
    LastActionTime    string  `json:"last_action_time,optional"` // 最后操作时间
    UsagePercentage   float64 `json:"usage_percentage"`    // 使用占比
    FrequencyPerDay   float64 `json:"frequency_per_day"`   // 日均使用频率
    Rank              int     `json:"rank"`                // 使用排名
}

// 获取启动项列表请求
type GetLaunchItemsReq {
    CategoryId   int64  `form:"category_id,optional"`   // 分类ID过滤
    IsEnabled    *bool  `form:"is_enabled,optional"`    // 启用状态过滤
    Search       string `form:"search,optional"`        // 搜索关键词
    SortBy       string `form:"sort_by,optional"`       // 排序字段: name/use_count/last_used/priority
    SortOrder    string `form:"sort_order,optional"`    // 排序方向: asc/desc
    IncludeStats bool   `form:"include_stats,optional"` // 是否包含统计信息
    PageReq
}

// 获取启动项列表响应
type GetLaunchItemsResp {
    BaseResp
    Data     []LaunchItem `json:"data"`      // 启动项列表
    PageInfo PageInfo     `json:"page_info"` // 分页信息
    Stats    struct {
        Total        int64 `json:"total"`         // 总数
        Enabled      int64 `json:"enabled"`       // 启用数
        Disabled     int64 `json:"disabled"`      // 禁用数
        RecentlyUsed int64 `json:"recently_used"` // 最近使用数
    } `json:"stats"`
}

// 创建启动项请求
type CreateLaunchItemReq {
    Name             string   `json:"name" validate:"required,max=100"`        // 应用名称
    Path             string   `json:"path" validate:"required,max=500"`        // 应用路径
    CategoryId       int64    `json:"category_id" validate:"required,min=1"`   // 分类ID
    IconData         string   `json:"icon_data,optional"`                      // 图标数据
    IconPath         string   `json:"icon_path,optional"`                      // 图标路径
    Description      string   `json:"description,optional" validate:"max=500"` // 描述
    Tags             []string `json:"tags,optional"`                           // 标签
    Arguments        string   `json:"arguments,optional" validate:"max=1000"`  // 启动参数
    WorkingDirectory string   `json:"working_directory,optional" validate:"max=500"` // 工作目录
    RunAsAdmin       bool     `json:"run_as_admin,optional"`                   // 管理员运行
    Priority         int      `json:"priority,optional" validate:"min=0,max=100"` // 优先级
    IsEnabled        bool     `json:"is_enabled,optional"`                     // 是否启用
}

// 更新启动项请求
type UpdateLaunchItemReq {
    Id               int64    `path:"id" validate:"required,min=1"`            // 启动项ID
    Name             string   `json:"name,optional" validate:"max=100"`        // 应用名称
    Path             string   `json:"path,optional" validate:"max=500"`        // 应用路径
    CategoryId       int64    `json:"category_id,optional" validate:"min=1"`   // 分类ID
    IconData         string   `json:"icon_data,optional"`                      // 图标数据
    IconPath         string   `json:"icon_path,optional"`                      // 图标路径
    Description      string   `json:"description,optional" validate:"max=500"` // 描述
    Tags             []string `json:"tags,optional"`                           // 标签
    Arguments        string   `json:"arguments,optional" validate:"max=1000"`  // 启动参数
    WorkingDirectory string   `json:"working_directory,optional" validate:"max=500"` // 工作目录
    RunAsAdmin       bool     `json:"run_as_admin,optional"`                   // 管理员运行
    Priority         int      `json:"priority,optional" validate:"min=0,max=100"` // 优先级
    IsEnabled        bool     `json:"is_enabled,optional"`                     // 是否启用
}

// 获取启动项详情请求
type GetLaunchItemReq {
    Id           int64 `path:"id" validate:"required,min=1"`     // 启动项ID
    IncludeStats bool  `form:"include_stats,optional"`          // 是否包含统计信息
}

// 删除启动项请求
type DeleteLaunchItemReq {
    Id int64 `path:"id" validate:"required,min=1"` // 启动项ID
}

// 启动项响应
type LaunchItemResp {
    BaseResp
    Data  LaunchItem       `json:"data"`            // 启动项数据
    Stats LaunchItemStats  `json:"stats,optional"`  // 统计信息
}

// 批量操作启动项请求
type BatchOperateLaunchItemsReq {
    Operation string  `json:"operation" validate:"required,oneof=enable disable delete update_category update_priority"` // 操作类型
    Ids       []int64 `json:"ids" validate:"required,min=1,dive,min=1"`  // 启动项ID列表
    Data      struct {
        CategoryId int64 `json:"category_id,optional"` // 新分类ID(用于update_category)
        Priority   int   `json:"priority,optional"`    // 新优先级(用于update_priority)
        IsEnabled  bool  `json:"is_enabled,optional"`  // 启用状态(用于enable/disable)
    } `json:"data,optional"` // 操作数据
}

// 搜索启动项请求
type SearchLaunchItemsReq {
    Query        string `form:"query" validate:"required,min=1"`    // 搜索关键词
    CategoryId   int64  `form:"category_id,optional"`               // 分类过滤
    IsEnabled    *bool  `form:"is_enabled,optional"`                // 启用状态过滤
    SearchFields string `form:"search_fields,optional"`             // 搜索字段: name/description/tags/all
    Highlight    bool   `form:"highlight,optional"`                 // 是否高亮搜索结果
    MinScore     float64 `form:"min_score,optional"`                // 最小相关性分数
    PageReq
}

// 获取最近使用启动项请求
type GetRecentLaunchItemsReq {
    Limit        int    `form:"limit,optional" validate:"min=1,max=50"`     // 限制数量
    Days         int    `form:"days,optional" validate:"min=1,max=90"`      // 最近天数
    CategoryId   int64  `form:"category_id,optional"`                       // 分类过滤
    IncludeStats bool   `form:"include_stats,optional"`                     // 是否包含统计
}

// 记录使用请求
type RecordUsageReq {
    Id           int64  `path:"id" validate:"required,min=1"`               // 启动项ID
    LaunchMethod string `json:"launch_method" validate:"required,oneof=click hotkey search drag_drop recommendation"` // 启动方式
    ResponseTime int    `json:"response_time,optional" validate:"min=0"`    // 响应时间(毫秒)
    Success      bool   `json:"success"`                                     // 是否成功启动
    ErrorMessage string `json:"error_message,optional"`                     // 错误信息
    Context      struct {
        SearchQuery       string `json:"search_query,optional"`       // 搜索关键词
        RecommendationId  string `json:"recommendation_id,optional"`  // 推荐ID
        FileType          string `json:"file_type,optional"`          // 文件类型
        ClipboardContent  string `json:"clipboard_content,optional"`  // 剪贴板内容(脱敏)
        ActiveWindow      string `json:"active_window,optional"`      // 活动窗口
        WorkingDirectory  string `json:"working_directory,optional"`  // 工作目录
        TimeOfDay         string `json:"time_of_day,optional"`        // 时间段
    } `json:"context,optional"` // 使用上下文
}

// 启动项导入请求
type ImportLaunchItemsReq {
    Format      string `json:"format" validate:"required,oneof=json csv xml"`  // 导入格式
    Data        string `json:"data" validate:"required"`                       // 导入数据
    CategoryId  int64  `json:"category_id,optional"`                          // 默认分类ID
    Overwrite   bool   `json:"overwrite,optional"`                            // 是否覆盖重复项
    ValidateOnly bool  `json:"validate_only,optional"`                        // 仅验证不导入
}

// 启动项导出请求
type ExportLaunchItemsReq {
    Format     string  `json:"format" validate:"required,oneof=json csv xml"`  // 导出格式
    CategoryId int64   `json:"category_id,optional"`                          // 分类过滤
    Ids        []int64 `json:"ids,optional"`                                  // 指定ID列表
    Fields     []string `json:"fields,optional"`                              // 导出字段
    IncludeIcon bool   `json:"include_icon,optional"`                         // 是否包含图标
}

// 启动项验证请求
type ValidateLaunchItemsReq {
    Ids         []int64 `json:"ids,optional"`           // 验证指定ID，为空则验证全部
    CheckPath   bool    `json:"check_path,optional"`    // 检查路径有效性
    CheckIcon   bool    `json:"check_icon,optional"`    // 检查图标有效性
    CheckDuplicate bool `json:"check_duplicate,optional"` // 检查重复项
}

// 启动项验证响应
type ValidateLaunchItemsResp {
    BaseResp
    Results []struct {
        Id       int64    `json:"id"`        // 启动项ID
        Name     string   `json:"name"`      // 启动项名称
        Valid    bool     `json:"valid"`     // 是否有效
        Issues   []string `json:"issues"`    // 问题列表
        Warnings []string `json:"warnings"`  // 警告列表
    } `json:"results"`
    Summary struct {
        Total    int `json:"total"`     // 总数
        Valid    int `json:"valid"`     // 有效数
        Invalid  int `json:"invalid"`   // 无效数
        Warnings int `json:"warnings"`  // 警告数
    } `json:"summary"`
}

// 启动项使用统计请求
type GetLaunchItemUsageStatsReq {
    Id        int64  `path:"id" validate:"required,min=1"`     // 启动项ID
    TimeRange string `form:"time_range,optional"`              // 时间范围: 1d/7d/30d/90d
    GroupBy   string `form:"group_by,optional"`                // 分组方式: hour/day/week/month
}

// 启动项使用统计响应
type GetLaunchItemUsageStatsResp {
    BaseResp
    Data struct {
        LaunchItem LaunchItem          `json:"launch_item"`  // 启动项信息
        Timeline   []TimeSeriesPoint   `json:"timeline"`     // 时间线数据
        Summary    struct {
            TotalUsage      int64   `json:"total_usage"`       // 总使用次数
            AvgUsagePerDay  float64 `json:"avg_usage_per_day"` // 日均使用次数
            PeakUsageHour   int     `json:"peak_usage_hour"`   // 使用高峰时段
            UsageTrend      string  `json:"usage_trend"`       // 使用趋势: up/down/stable
            LastUsed        string  `json:"last_used"`         // 最后使用时间
        } `json:"summary"`
        HourlyDistribution []struct {
            Hour  int   `json:"hour"`   // 小时(0-23)
            Count int64 `json:"count"`  // 使用次数
        } `json:"hourly_distribution"` // 小时分布
        WeeklyDistribution []struct {
            DayOfWeek int   `json:"day_of_week"` // 星期几(1-7)
            Count     int64 `json:"count"`       // 使用次数
        } `json:"weekly_distribution"` // 星期分布
    } `json:"data"`
}
