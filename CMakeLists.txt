cmake_minimum_required(VERSION 3.20)
project(KKQuickLaunch VERSION 1.0.0 LANGUAGES CXX)

# C++标准设置
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项
if(MSVC)
    add_compile_options(/W4 /utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra)
endif()

# Qt配置
find_package(Qt6 REQUIRED COMPONENTS 
    Core 
    Widgets 
    Sql 
    Network 
    Concurrent
    WebSockets
)

# 自动处理Qt的MOC、UIC、RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 包含目录
include_directories(
    src
    src/application
    src/presentation
    src/business
    src/services
    src/data
    src/infrastructure
    src/plugins
)

# 源文件分组
file(GLOB_RECURSE APPLICATION_SOURCES
    "src/application/*.cpp"
    "src/application/*.h"
)

file(GLOB_RECURSE PRESENTATION_SOURCES
    "src/presentation/*.cpp"
    "src/presentation/*.h"
    "src/presentation/*.ui"
)

file(GLOB_RECURSE BUSINESS_SOURCES
    "src/business/*.cpp"
    "src/business/*.h"
)

file(GLOB_RECURSE SERVICES_SOURCES
    "src/services/*.cpp"
    "src/services/*.h"
)

file(GLOB_RECURSE DATA_SOURCES
    "src/data/*.cpp"
    "src/data/*.h"
)

file(GLOB_RECURSE INFRASTRUCTURE_SOURCES
    "src/infrastructure/*.cpp"
    "src/infrastructure/*.h"
)

file(GLOB_RECURSE PLUGINS_SOURCES
    "src/plugins/*.cpp"
    "src/plugins/*.h"
)

# 主程序源文件
set(MAIN_SOURCES
    src/main.cpp
)

# 资源文件
set(RESOURCES
    resources/icons.qrc
    resources/themes.qrc
    resources/translations.qrc
)

# 所有源文件
set(ALL_SOURCES
    ${MAIN_SOURCES}
    ${APPLICATION_SOURCES}
    ${PRESENTATION_SOURCES}
    ${BUSINESS_SOURCES}
    ${SERVICES_SOURCES}
    ${DATA_SOURCES}
    ${INFRASTRUCTURE_SOURCES}
    ${PLUGINS_SOURCES}
    ${RESOURCES}
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${ALL_SOURCES})

# 链接Qt库
target_link_libraries(${PROJECT_NAME}
    Qt6::Core
    Qt6::Widgets
    Qt6::Sql
    Qt6::Network
    Qt6::Concurrent
    Qt6::WebSockets
)

# 编译定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    QT_NO_CAST_FROM_ASCII
    QT_NO_CAST_TO_ASCII
    QT_NO_URL_CAST_FROM_STRING
    QT_STRICT_ITERATORS
    QT_NO_NARROWING_CONVERSIONS_IN_CONNECT
    APP_VERSION="${PROJECT_VERSION}"
    APP_NAME="${PROJECT_NAME}"
)

# Windows特定设置
if(WIN32)
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        WIN32_LEAN_AND_MEAN
        NOMINMAX
    )
    
    # 设置Windows应用程序属性
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
endif()

# 预编译头文件
target_precompile_headers(${PROJECT_NAME} PRIVATE
    <QtCore>
    <QtWidgets>
    <QtSql>
    <QtNetwork>
    <QtConcurrent>
    <memory>
    <functional>
    <algorithm>
    <unordered_map>
    <vector>
    <string>
)

# 安装配置
install(TARGETS ${PROJECT_NAME}
    BUNDLE DESTINATION .
    RUNTIME DESTINATION bin
)

# 测试配置
enable_testing()
add_subdirectory(tests)

# 文档生成
find_package(Doxygen)
if(DOXYGEN_FOUND)
    configure_file(${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in 
                   ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile @ONLY)
    add_custom_target(doc
        ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating API documentation with Doxygen" VERBATIM
    )
endif()

# CPack配置
include(InstallRequiredSystemLibraries)
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_PACKAGE_VERSION_MAJOR "${PROJECT_VERSION_MAJOR}")
set(CPACK_PACKAGE_VERSION_MINOR "${PROJECT_VERSION_MINOR}")
set(CPACK_PACKAGE_VERSION_PATCH "${PROJECT_VERSION_PATCH}")
include(CPack)
