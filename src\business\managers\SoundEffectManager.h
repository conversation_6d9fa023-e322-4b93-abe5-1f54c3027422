#pragma once

#include <QObject>
#include <QSoundEffect>
#include <QMediaPlayer>
#include <QAudioOutput>
#include <QTimer>
#include <QHash>
#include <QQueue>
#include <QMutex>
#include <memory>

// 前置声明
class ConfigManager;

/**
 * @brief 声音效果类型
 */
enum class SoundEffectType {
    // 系统音效
    Launch,         // 启动音效
    Search,         // 搜索音效
    Select,         // 选择音效
    Error,          // 错误音效
    Success,        // 成功音效
    Notification,   // 通知音效
    
    // 界面音效
    WindowShow,     // 窗口显示
    WindowHide,     // 窗口隐藏
    ButtonClick,    // 按钮点击
    MenuOpen,       // 菜单打开
    MenuClose,      // 菜单关闭
    
    // 语音音效
    VoiceStart,     // 语音开始
    VoiceEnd,       // 语音结束
    VoiceError,     // 语音错误
    
    // 动画音效
    FadeIn,         // 淡入
    FadeOut,        // 淡出
    SlideIn,        // 滑入
    SlideOut,       // 滑出
    
    // 自定义音效
    Custom1,
    Custom2,
    Custom3
};

/**
 * @brief 声音主题
 */
struct SoundTheme {
    QString name;                           // 主题名称
    QString displayName;                    // 显示名称
    QString description;                    // 描述
    QString author;                         // 作者
    QString version;                        // 版本
    QHash<SoundEffectType, QString> sounds; // 音效文件映射
};

/**
 * @brief 声音播放选项
 */
struct SoundPlayOptions {
    double volume = 1.0;        // 音量 (0.0 - 1.0)
    double pitch = 1.0;         // 音调 (0.5 - 2.0)
    double speed = 1.0;         // 播放速度 (0.5 - 2.0)
    int loops = 1;              // 循环次数 (-1 = 无限循环)
    int delay = 0;              // 延迟播放 (毫秒)
    bool fadeIn = false;        // 淡入效果
    bool fadeOut = false;       // 淡出效果
    int fadeInDuration = 500;   // 淡入时长
    int fadeOutDuration = 500;  // 淡出时长
};

/**
 * @brief 声音效果管理器
 * 
 * 提供完整的声音效果管理功能，包括：
 * - 多种音效播放
 * - 声音主题系统
 * - 音量和音调控制
 * - 淡入淡出效果
 * - 音效队列管理
 * - 自定义音效支持
 */
class SoundEffectManager : public QObject
{
    Q_OBJECT
    
    Q_PROPERTY(bool enabled READ isEnabled WRITE setEnabled NOTIFY enabledChanged)
    Q_PROPERTY(double masterVolume READ masterVolume WRITE setMasterVolume NOTIFY masterVolumeChanged)
    Q_PROPERTY(QString currentTheme READ currentTheme WRITE setCurrentTheme NOTIFY currentThemeChanged)
    Q_PROPERTY(bool fadeEffectsEnabled READ fadeEffectsEnabled WRITE setFadeEffectsEnabled NOTIFY fadeEffectsEnabledChanged)

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit SoundEffectManager(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~SoundEffectManager();

    /**
     * @brief 初始化声音效果管理器
     * @return 是否成功
     */
    bool initialize();

    // 属性访问器
    bool isEnabled() const;
    void setEnabled(bool enabled);
    
    double masterVolume() const;
    void setMasterVolume(double volume);
    
    QString currentTheme() const;
    void setCurrentTheme(const QString &themeName);
    
    bool fadeEffectsEnabled() const;
    void setFadeEffectsEnabled(bool enabled);

    // 音效播放
    
    /**
     * @brief 播放音效
     * @param type 音效类型
     * @param options 播放选项
     */
    Q_INVOKABLE void playSound(SoundEffectType type, const SoundPlayOptions &options = {});
    
    /**
     * @brief 播放自定义音效
     * @param filePath 音效文件路径
     * @param options 播放选项
     */
    Q_INVOKABLE void playCustomSound(const QString &filePath, const SoundPlayOptions &options = {});
    
    /**
     * @brief 停止音效
     * @param type 音效类型
     */
    Q_INVOKABLE void stopSound(SoundEffectType type);
    
    /**
     * @brief 停止所有音效
     */
    Q_INVOKABLE void stopAllSounds();
    
    /**
     * @brief 暂停音效
     * @param type 音效类型
     */
    Q_INVOKABLE void pauseSound(SoundEffectType type);
    
    /**
     * @brief 恢复音效
     * @param type 音效类型
     */
    Q_INVOKABLE void resumeSound(SoundEffectType type);
    
    /**
     * @brief 检查音效是否正在播放
     * @param type 音效类型
     * @return 是否播放中
     */
    bool isSoundPlaying(SoundEffectType type) const;

    // 主题管理
    
    /**
     * @brief 加载声音主题
     * @param themeName 主题名称
     * @return 是否成功
     */
    bool loadTheme(const QString &themeName);
    
    /**
     * @brief 获取可用主题列表
     * @return 主题列表
     */
    QStringList getAvailableThemes() const;
    
    /**
     * @brief 获取主题信息
     * @param themeName 主题名称
     * @return 主题信息
     */
    SoundTheme getThemeInfo(const QString &themeName) const;
    
    /**
     * @brief 安装主题
     * @param themeFile 主题文件路径
     * @return 是否成功
     */
    bool installTheme(const QString &themeFile);
    
    /**
     * @brief 卸载主题
     * @param themeName 主题名称
     * @return 是否成功
     */
    bool uninstallTheme(const QString &themeName);
    
    /**
     * @brief 创建自定义主题
     * @param theme 主题信息
     * @return 是否成功
     */
    bool createCustomTheme(const SoundTheme &theme);

    // 音效管理
    
    /**
     * @brief 预加载音效
     * @param type 音效类型
     */
    void preloadSound(SoundEffectType type);
    
    /**
     * @brief 预加载所有音效
     */
    void preloadAllSounds();
    
    /**
     * @brief 卸载音效
     * @param type 音效类型
     */
    void unloadSound(SoundEffectType type);
    
    /**
     * @brief 卸载所有音效
     */
    void unloadAllSounds();
    
    /**
     * @brief 设置音效文件
     * @param type 音效类型
     * @param filePath 文件路径
     */
    void setSoundFile(SoundEffectType type, const QString &filePath);
    
    /**
     * @brief 获取音效文件路径
     * @param type 音效类型
     * @return 文件路径
     */
    QString getSoundFile(SoundEffectType type) const;

    // 队列管理
    
    /**
     * @brief 添加到播放队列
     * @param type 音效类型
     * @param options 播放选项
     */
    void queueSound(SoundEffectType type, const SoundPlayOptions &options = {});
    
    /**
     * @brief 播放队列中的音效
     */
    void playQueue();
    
    /**
     * @brief 清空播放队列
     */
    void clearQueue();
    
    /**
     * @brief 获取队列长度
     * @return 队列长度
     */
    int getQueueLength() const;

    // 工具方法
    
    /**
     * @brief 测试音效
     * @param type 音效类型
     */
    Q_INVOKABLE void testSound(SoundEffectType type);
    
    /**
     * @brief 获取音效类型名称
     * @param type 音效类型
     * @return 类型名称
     */
    QString getSoundTypeName(SoundEffectType type) const;
    
    /**
     * @brief 检查音效文件是否存在
     * @param type 音效类型
     * @return 是否存在
     */
    bool soundFileExists(SoundEffectType type) const;
    
    /**
     * @brief 获取音效文件信息
     * @param filePath 文件路径
     * @return 文件信息 (时长、格式等)
     */
    QVariantMap getSoundFileInfo(const QString &filePath) const;

signals:
    /**
     * @brief 启用状态变更信号
     * @param enabled 是否启用
     */
    void enabledChanged(bool enabled);
    
    /**
     * @brief 主音量变更信号
     * @param volume 音量
     */
    void masterVolumeChanged(double volume);
    
    /**
     * @brief 当前主题变更信号
     * @param themeName 主题名称
     */
    void currentThemeChanged(const QString &themeName);
    
    /**
     * @brief 淡入淡出效果启用状态变更信号
     * @param enabled 是否启用
     */
    void fadeEffectsEnabledChanged(bool enabled);
    
    /**
     * @brief 音效播放开始信号
     * @param type 音效类型
     */
    void soundStarted(SoundEffectType type);
    
    /**
     * @brief 音效播放完成信号
     * @param type 音效类型
     */
    void soundFinished(SoundEffectType type);
    
    /**
     * @brief 音效播放错误信号
     * @param type 音效类型
     * @param error 错误信息
     */
    void soundError(SoundEffectType type, const QString &error);
    
    /**
     * @brief 主题加载完成信号
     * @param themeName 主题名称
     * @param success 是否成功
     */
    void themeLoaded(const QString &themeName, bool success);

private slots:
    /**
     * @brief 音效播放状态变更处理
     */
    void onSoundStateChanged();
    
    /**
     * @brief 队列播放定时器超时处理
     */
    void onQueueTimerTimeout();
    
    /**
     * @brief 淡入效果定时器超时处理
     */
    void onFadeInTimeout();
    
    /**
     * @brief 淡出效果定时器超时处理
     */
    void onFadeOutTimeout();
    
    /**
     * @brief 配置变更处理
     * @param key 配置键
     * @param value 配置值
     */
    void onConfigChanged(const QString &key, const QVariant &value);

private:
    /**
     * @brief 初始化默认主题
     */
    void initializeDefaultTheme();
    
    /**
     * @brief 加载主题文件
     * @param themeFile 主题文件路径
     * @return 主题信息
     */
    SoundTheme loadThemeFile(const QString &themeFile);
    
    /**
     * @brief 创建音效播放器
     * @param type 音效类型
     * @return 播放器指针
     */
    QSoundEffect* createSoundPlayer(SoundEffectType type);
    
    /**
     * @brief 应用播放选项
     * @param player 播放器
     * @param options 播放选项
     */
    void applySoundOptions(QSoundEffect *player, const SoundPlayOptions &options);
    
    /**
     * @brief 开始淡入效果
     * @param player 播放器
     * @param duration 时长
     */
    void startFadeIn(QSoundEffect *player, int duration);
    
    /**
     * @brief 开始淡出效果
     * @param player 播放器
     * @param duration 时长
     */
    void startFadeOut(QSoundEffect *player, int duration);
    
    /**
     * @brief 获取主题目录
     * @return 主题目录路径
     */
    QString getThemeDirectory() const;
    
    /**
     * @brief 获取默认音效文件路径
     * @param type 音效类型
     * @return 文件路径
     */
    QString getDefaultSoundFile(SoundEffectType type) const;

private:
    // 服务引用
    std::shared_ptr<ConfigManager> m_configManager;
    
    // 音效播放器
    QHash<SoundEffectType, std::unique_ptr<QSoundEffect>> m_soundPlayers;
    QHash<SoundEffectType, QString> m_soundFiles;
    
    // 主题管理
    QHash<QString, SoundTheme> m_themes;
    QString m_currentTheme = "default";
    
    // 播放队列
    struct QueuedSound {
        SoundEffectType type;
        SoundPlayOptions options;
    };
    QQueue<QueuedSound> m_playQueue;
    QTimer *m_queueTimer;
    
    // 淡入淡出效果
    QHash<QSoundEffect*, QTimer*> m_fadeTimers;
    
    // 状态管理
    bool m_enabled = true;
    double m_masterVolume = 0.8;
    bool m_fadeEffectsEnabled = true;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // 常量
    static const int QUEUE_INTERVAL = 100;
    static const int FADE_STEP_INTERVAL = 50;
};
