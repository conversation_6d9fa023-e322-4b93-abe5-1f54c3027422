#!/bin/bash
# KK QuickLaunch 构建脚本 (Linux/macOS)
# 使用方法: ./build.sh [clean|debug|release|test|package]

set -e  # 遇到错误时退出

# 默认设置
BUILD_TYPE="Release"
BUILD_DIR="build"
CLEAN_BUILD=0
RUN_TESTS=0
CREATE_PACKAGE=0
PARALLEL_JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
USE_CONAN=1

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用方法
show_usage() {
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  clean     - 清理构建目录"
    echo "  debug     - 调试构建"
    echo "  release   - 发布构建 (默认)"
    echo "  test      - 运行测试"
    echo "  package   - 创建安装包"
    echo "  no-conan  - 不使用Conan包管理器"
    echo ""
    echo "示例:"
    echo "  $0 clean release test    - 清理后进行发布构建并运行测试"
    echo "  $0 debug                 - 调试构建"
    echo "  $0 package               - 创建发布安装包"
    echo "  $0 no-conan release      - 不使用Conan进行发布构建"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        clean)
            CLEAN_BUILD=1
            shift
            ;;
        debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        release)
            BUILD_TYPE="Release"
            shift
            ;;
        test)
            RUN_TESTS=1
            shift
            ;;
        package)
            CREATE_PACKAGE=1
            shift
            ;;
        no-conan)
            USE_CONAN=0
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_info "========================================"
print_info "KK QuickLaunch 构建脚本"
print_info "========================================"
print_info "构建类型: $BUILD_TYPE"
print_info "构建目录: $BUILD_DIR"
print_info "并行任务: $PARALLEL_JOBS"
print_info "使用Conan: $([ $USE_CONAN -eq 1 ] && echo "是" || echo "否")"
print_info "========================================"

# 检查必要工具
print_info "检查构建工具..."

if ! command -v cmake &> /dev/null; then
    print_error "未找到CMake，请安装CMake"
    exit 1
fi

if ! command -v qmake6 &> /dev/null && ! command -v qmake &> /dev/null; then
    print_warning "未找到Qt6，请确保Qt6已安装并添加到PATH"
fi

if [ $USE_CONAN -eq 1 ]; then
    if ! command -v conan &> /dev/null; then
        print_warning "未找到Conan，将跳过依赖管理"
        USE_CONAN=0
    fi
fi

# 清理构建目录
if [ $CLEAN_BUILD -eq 1 ]; then
    print_info "清理构建目录..."
    rm -rf "$BUILD_DIR"
fi

# 创建构建目录
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# 安装依赖（如果使用Conan）
CMAKE_TOOLCHAIN=""
if [ $USE_CONAN -eq 1 ]; then
    print_info "安装项目依赖..."
    conan install .. --build=missing -s build_type="$BUILD_TYPE"
    if [ $? -eq 0 ]; then
        CMAKE_TOOLCHAIN="-DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake"
    else
        print_error "Conan依赖安装失败"
        exit 1
    fi
fi

# 配置CMake
print_info "配置CMake..."
cmake .. -DCMAKE_BUILD_TYPE="$BUILD_TYPE" $CMAKE_TOOLCHAIN
if [ $? -ne 0 ]; then
    print_error "CMake配置失败"
    exit 1
fi

# 编译项目
print_info "编译项目..."
cmake --build . --config "$BUILD_TYPE" --parallel "$PARALLEL_JOBS"
if [ $? -ne 0 ]; then
    print_error "编译失败"
    exit 1
fi

# 运行测试
if [ $RUN_TESTS -eq 1 ]; then
    print_info "运行测试..."
    ctest --output-on-failure --parallel "$PARALLEL_JOBS"
    if [ $? -ne 0 ]; then
        print_warning "部分测试失败"
    fi
fi

# 创建安装包
if [ $CREATE_PACKAGE -eq 1 ]; then
    print_info "创建安装包..."
    cmake --build . --target package --config "$BUILD_TYPE"
    if [ $? -ne 0 ]; then
        print_error "创建安装包失败"
        exit 1
    fi
fi

cd ..

print_success "========================================"
print_success "构建完成！"
print_success "========================================"
print_success "可执行文件: $BUILD_DIR/KKQuickLaunch"
if [ $CREATE_PACKAGE -eq 1 ]; then
    print_success "安装包: $BUILD_DIR/packages/"
fi
print_success "========================================"

# 显示下一步操作提示
print_info ""
print_info "下一步操作:"
print_info "1. 运行应用程序: ./$BUILD_DIR/KKQuickLaunch"
if [ $RUN_TESTS -eq 0 ]; then
    print_info "2. 运行测试: ./build.sh test"
fi
if [ $CREATE_PACKAGE -eq 0 ]; then
    print_info "3. 创建安装包: ./build.sh package"
fi
print_info "4. 安装应用程序: cd $BUILD_DIR && sudo make install"
