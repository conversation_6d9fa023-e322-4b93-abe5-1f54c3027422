syntax = "v1"

// ============================================================================
// 搜索相关类型定义
// ============================================================================

// 搜索结果项
type SearchResultItem {
    Id          string  `json:"id"`           // 结果ID
    Type        string  `json:"type"`         // 结果类型: launch_item/app/file/web
    Title       string  `json:"title"`        // 标题
    Description string  `json:"description"`  // 描述
    Icon        string  `json:"icon"`         // 图标URL
    Path        string  `json:"path,optional"` // 路径(文件/应用)
    Url         string  `json:"url,optional"`  // URL(网页)
    Score       float64 `json:"score"`        // 相关性分数
    Highlights  []struct {
        Field string `json:"field"`        // 高亮字段
        Text  string `json:"text"`         // 高亮文本
    } `json:"highlights,optional"` // 高亮信息
    Metadata map[string]interface{} `json:"metadata,optional"` // 额外元数据
    Category    string `json:"category,optional"`    // 分类
    Tags        []string `json:"tags,optional"`       // 标签
    LastUsed    string `json:"last_used,optional"`    // 最后使用时间
    UseCount    int64  `json:"use_count,optional"`    // 使用次数
    FileSize    int64  `json:"file_size,optional"`    // 文件大小
    ModifiedAt  string `json:"modified_at,optional"`  // 修改时间
}

// 全局搜索请求
type GlobalSearchReq {
    Query       string   `form:"query" validate:"required,min=1"`           // 搜索关键词
    Types       []string `form:"types,optional"`                            // 搜索类型过滤: launch_item/app/file/web
    Categories  []string `form:"categories,optional"`                       // 分类过滤
    MaxResults  int      `form:"max_results,optional" validate:"min=1,max=100"` // 最大结果数
    Highlight   bool     `form:"highlight,optional"`                        // 是否高亮
    Fuzzy       bool     `form:"fuzzy,optional"`                            // 是否模糊搜索
    MinScore    float64  `form:"min_score,optional" validate:"min=0,max=1"` // 最小相关性分数
    SortBy      string   `form:"sort_by,optional"`                          // 排序字段: score/name/last_used/use_count
    SortOrder   string   `form:"sort_order,optional"`                       // 排序方向: asc/desc
    IncludePath bool     `form:"include_path,optional"`                     // 是否包含路径搜索
    SearchScope string   `form:"search_scope,optional"`                     // 搜索范围: local/cloud/all
}

// 全局搜索响应
type GlobalSearchResp {
    BaseResp
    Data struct {
        Results []SearchResultItem `json:"results"`        // 搜索结果
        Summary struct {
            Total       int64   `json:"total"`        // 总结果数
            LaunchItems int64   `json:"launch_items"` // 启动项数量
            Apps        int64   `json:"apps"`         // 应用数量
            Files       int64   `json:"files"`        // 文件数量
            WebResults  int64   `json:"web_results"`  // 网页结果数量
            SearchTime  int     `json:"search_time"`  // 搜索耗时(毫秒)
            HasMore     bool    `json:"has_more"`     // 是否有更多结果
        } `json:"summary"`
        Suggestions []string `json:"suggestions,optional"` // 搜索建议
        Corrections []string `json:"corrections,optional"` // 拼写纠正建议
        RelatedQueries []string `json:"related_queries,optional"` // 相关查询
    } `json:"data"`
}

// 搜索建议请求
type GetSearchSuggestionsReq {
    Query      string `form:"query" validate:"required,min=1"`              // 搜索关键词前缀
    MaxResults int    `form:"max_results,optional" validate:"min=1,max=20"` // 最大建议数
    Types      []string `form:"types,optional"`                             // 建议类型过滤
    IncludeHistory bool `form:"include_history,optional"`                   // 是否包含历史搜索
    IncludePopular bool `form:"include_popular,optional"`                   // 是否包含热门搜索
}

// 搜索建议响应
type GetSearchSuggestionsResp {
    BaseResp
    Data struct {
        Suggestions []struct {
            Text        string  `json:"text"`         // 建议文本
            Type        string  `json:"type"`         // 建议类型: history/popular/completion/correction
            Score       float64 `json:"score"`        // 相关性分数
            Count       int64   `json:"count,optional"` // 搜索次数(热门搜索)
            LastUsed    string  `json:"last_used,optional"` // 最后使用时间(历史搜索)
            Icon        string  `json:"icon,optional"` // 图标
            Category    string  `json:"category,optional"` // 分类
        } `json:"suggestions"`
        Meta struct {
            ResponseTime int    `json:"response_time"` // 响应时间(毫秒)
            Source       string `json:"source"`        // 数据源: local/cloud/mixed
        } `json:"meta"`
    } `json:"data"`
}

// 搜索历史记录
type SearchHistoryItem {
    Id          int64  `json:"id"`           // 历史记录ID
    SearchQuery string `json:"search_query"` // 搜索关键词
    ResultCount int    `json:"result_count"` // 搜索结果数量
    SelectedItemId int64 `json:"selected_item_id,optional"` // 用户选择的项目ID
    SelectedItemName string `json:"selected_item_name,optional"` // 选择的项目名称
    SearchMethod string `json:"search_method"` // 搜索方式: typing/voice/paste/hotkey
    ResponseTime int    `json:"response_time"` // 搜索响应时间(毫秒)
    Success      bool   `json:"success"`       // 是否成功找到结果
    Context      struct {
        TimeOfDay        string `json:"time_of_day,optional"`        // 搜索时间段
        ActiveWindow     string `json:"active_window,optional"`      // 活动窗口
        WorkingDirectory string `json:"working_directory,optional"`  // 工作目录
        FileType         string `json:"file_type,optional"`          // 当前文件类型
    } `json:"context,optional"` // 搜索上下文
    Timestamp string `json:"timestamp"`      // 搜索时间
}

// 获取搜索历史请求
type GetSearchHistoryReq {
    Query       string `form:"query,optional"`                             // 过滤关键词
    Success     *bool  `form:"success,optional"`                           // 成功状态过滤
    Limit       int    `form:"limit,optional" validate:"min=1,max=100"`    // 限制数量
    TimeRange   string `form:"time_range,optional"`                        // 时间范围: 1d/7d/30d
    GroupBy     string `form:"group_by,optional"`                          // 分组方式: query/day
}

// 获取搜索历史响应
type GetSearchHistoryResp {
    BaseResp
    Data []SearchHistoryItem `json:"data"`                                 // 搜索历史列表
    Stats struct {
        Total           int64   `json:"total"`            // 总搜索次数
        UniqueQueries   int64   `json:"unique_queries"`   // 唯一查询数
        SuccessRate     float64 `json:"success_rate"`     // 成功率
        AvgResponseTime float64 `json:"avg_response_time"` // 平均响应时间
        MostSearched    string  `json:"most_searched"`    // 最常搜索的关键词
        RecentTrend     string  `json:"recent_trend"`     // 最近趋势: up/down/stable
    } `json:"stats"`
}

// 清空搜索历史请求
type ClearSearchHistoryReq {
    OlderThan string `json:"older_than,optional"`                          // 清理多久之前的记录: 1d/7d/30d/all
    Query     string `json:"query,optional"`                               // 清理特定查询的记录
    Confirm   bool   `json:"confirm" validate:"required"`                  // 确认清理
}

// 热门搜索请求
type GetPopularSearchesReq {
    TimeRange  string `form:"time_range,optional"`                         // 时间范围: 1d/7d/30d
    Category   string `form:"category,optional"`                           // 分类过滤
    Limit      int    `form:"limit,optional" validate:"min=1,max=50"`      // 限制数量
    MinCount   int    `form:"min_count,optional" validate:"min=1"`         // 最小搜索次数
    ExcludeSelf bool  `form:"exclude_self,optional"`                       // 是否排除自己的搜索
}

// 热门搜索响应
type GetPopularSearchesResp {
    BaseResp
    Data []struct {
        SearchQuery     string  `json:"search_query"`      // 搜索关键词
        SearchCount     int64   `json:"search_count"`      // 搜索次数
        UniqueUsers     int64   `json:"unique_users"`      // 唯一用户数
        SuccessRate     float64 `json:"success_rate"`      // 成功率
        AvgResults      float64 `json:"avg_results"`       // 平均结果数
        AvgResponseTime float64 `json:"avg_response_time"` // 平均响应时间
        TrendDirection  string  `json:"trend_direction"`   // 趋势方向: up/down/stable
        LastSearched    string  `json:"last_searched"`     // 最后搜索时间
        RelatedQueries  []string `json:"related_queries,optional"` // 相关查询
    } `json:"data"`
    Meta struct {
        TimeRange   string `json:"time_range"`    // 统计时间范围
        UpdatedAt   string `json:"updated_at"`    // 数据更新时间
        TotalUsers  int64  `json:"total_users"`   // 总用户数
        TotalSearches int64 `json:"total_searches"` // 总搜索次数
    } `json:"meta"`
}

// 搜索统计请求
type GetSearchStatsReq {
    TimeRange   string `form:"time_range,optional"`                        // 时间范围: 1h/1d/7d/30d
    GroupBy     string `form:"group_by,optional"`                          // 分组方式: hour/day/week/query
    MetricType  string `form:"metric_type,optional"`                       // 指标类型: count/response_time/success_rate
    Category    string `form:"category,optional"`                          // 分类过滤
}

// 搜索统计响应
type GetSearchStatsResp {
    BaseResp
    Data struct {
        Timeline []TimeSeriesPoint `json:"timeline"`                       // 时间线数据
        Summary struct {
            TotalSearches       int64   `json:"total_searches"`        // 总搜索次数
            UniqueQueries       int64   `json:"unique_queries"`        // 唯一查询数
            AvgSearchesPerDay   float64 `json:"avg_searches_per_day"`  // 日均搜索次数
            SuccessRate         float64 `json:"success_rate"`          // 成功率
            AvgResponseTime     float64 `json:"avg_response_time"`     // 平均响应时间
            MostActiveHour      int     `json:"most_active_hour"`      // 最活跃时间
            EmptyResultRate     float64 `json:"empty_result_rate"`     // 空结果率
            RetryRate           float64 `json:"retry_rate"`            // 重试率
        } `json:"summary"`
        TopQueries []struct {
            Query       string  `json:"query"`        // 查询关键词
            Count       int64   `json:"count"`        // 搜索次数
            Percentage  float64 `json:"percentage"`   // 占比
            SuccessRate float64 `json:"success_rate"` // 成功率
            AvgResults  float64 `json:"avg_results"`  // 平均结果数
        } `json:"top_queries"` // 热门查询
        FailedQueries []struct {
            Query       string `json:"query"`        // 查询关键词
            Count       int64  `json:"count"`        // 失败次数
            LastFailed  string `json:"last_failed"`  // 最后失败时间
            Suggestions []string `json:"suggestions,optional"` // 建议查询
        } `json:"failed_queries"` // 失败查询
        ResponseTimeDistribution []struct {
            Range      string  `json:"range"`       // 时间范围: <100ms, 100-300ms等
            Count      int64   `json:"count"`       // 数量
            Percentage float64 `json:"percentage"`  // 占比
        } `json:"response_time_distribution"` // 响应时间分布
        HourlyDistribution []struct {
            Hour  int   `json:"hour"`  // 小时(0-23)
            Count int64 `json:"count"` // 搜索次数
        } `json:"hourly_distribution"` // 小时分布
    } `json:"data"`
}

// 搜索索引管理
type SearchIndex {
    Name        string `json:"name"`         // 索引名称
    Type        string `json:"type"`         // 索引类型: launch_items/files/apps
    Status      string `json:"status"`       // 状态: building/ready/error
    DocumentCount int64 `json:"document_count"` // 文档数量
    IndexSize   int64  `json:"index_size"`   // 索引大小(字节)
    LastUpdated string `json:"last_updated"` // 最后更新时间
    Settings    struct {
        Analyzer    string `json:"analyzer"`     // 分析器
        MaxResults  int    `json:"max_results"`  // 最大结果数
        FuzzyEnabled bool  `json:"fuzzy_enabled"` // 是否启用模糊搜索
        Boost       map[string]float64 `json:"boost"` // 字段权重
    } `json:"settings"`
}

// 重建搜索索引请求
type RebuildSearchIndexReq {
    IndexName string `json:"index_name" validate:"required"`               // 索引名称
    Force     bool   `json:"force,optional"`                               // 是否强制重建
    Async     bool   `json:"async,optional"`                               // 是否异步执行
}

// 重建搜索索引响应
type RebuildSearchIndexResp {
    BaseResp
    Data struct {
        TaskId      string `json:"task_id,optional"`     // 任务ID(异步模式)
        Status      string `json:"status"`               // 状态: started/completed/failed
        Progress    int    `json:"progress"`             // 进度百分比
        EstimatedTime int  `json:"estimated_time"`       // 预计完成时间(秒)
        Message     string `json:"message"`              // 状态消息
    } `json:"data"`
}

// 搜索配置
type SearchConfig {
    MaxResults      int     `json:"max_results"`       // 最大结果数
    DefaultFuzzy    bool    `json:"default_fuzzy"`     // 默认模糊搜索
    MinScore        float64 `json:"min_score"`         // 最小分数阈值
    HighlightEnabled bool   `json:"highlight_enabled"` // 是否启用高亮
    SuggestionEnabled bool  `json:"suggestion_enabled"` // 是否启用建议
    HistoryEnabled  bool    `json:"history_enabled"`   // 是否启用历史记录
    IndexUpdateInterval int `json:"index_update_interval"` // 索引更新间隔(分钟)
    FieldWeights    map[string]float64 `json:"field_weights"` // 字段权重
    StopWords       []string `json:"stop_words"`        // 停用词列表
    Synonyms        map[string][]string `json:"synonyms"` // 同义词映射
}

// 获取搜索配置请求
type GetSearchConfigReq {
    // 无参数
}

// 获取搜索配置响应
type GetSearchConfigResp {
    BaseResp
    Data SearchConfig `json:"data"`                                        // 搜索配置
}

// 更新搜索配置请求
type UpdateSearchConfigReq {
    Config SearchConfig `json:"config" validate:"required"`               // 新的搜索配置
}

// 搜索性能监控
type SearchPerformanceMetrics {
    AvgResponseTime     float64 `json:"avg_response_time"`      // 平均响应时间(毫秒)
    P95ResponseTime     float64 `json:"p95_response_time"`      // 95分位响应时间
    P99ResponseTime     float64 `json:"p99_response_time"`      // 99分位响应时间
    SearchesPerSecond   float64 `json:"searches_per_second"`    // 每秒搜索次数
    CacheHitRate        float64 `json:"cache_hit_rate"`         // 缓存命中率
    IndexSize           int64   `json:"index_size"`             // 索引大小
    MemoryUsage         int64   `json:"memory_usage"`           // 内存使用量
    ErrorRate           float64 `json:"error_rate"`             // 错误率
    SlowQueryCount      int64   `json:"slow_query_count"`       // 慢查询数量
    LastUpdated         string  `json:"last_updated"`           // 最后更新时间
}

// 获取搜索性能指标请求
type GetSearchPerformanceReq {
    TimeRange string `form:"time_range,optional"`                         // 时间范围: 1h/1d/7d
}

// 获取搜索性能指标响应
type GetSearchPerformanceResp {
    BaseResp
    Data SearchPerformanceMetrics `json:"data"`                           // 性能指标
}
