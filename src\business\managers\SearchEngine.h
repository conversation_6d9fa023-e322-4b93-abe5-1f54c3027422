#pragma once

#include <QObject>
#include <QTimer>
#include <QReadWriteLock>
#include <QFuture>
#include <QFutureWatcher>
#include <memory>

#include "business/entities/LaunchItem.h"

// 前置声明
class LaunchItemRepository;
class ConfigManager;

/**
 * @brief 搜索结果项
 */
struct SearchResult {
    LaunchItem item;                // 启动项
    double relevanceScore = 0.0;    // 相关性分数 (0-1)
    QString highlightedName;        // 高亮显示的名称
    QString matchedText;            // 匹配的文本
    QStringList matchedFields;      // 匹配的字段列表
    int matchPosition = -1;         // 匹配位置
    int matchLength = 0;            // 匹配长度
};

/**
 * @brief 搜索选项
 */
struct SearchOptions {
    bool fuzzySearch = true;        // 模糊搜索
    bool caseSensitive = false;     // 大小写敏感
    bool searchInPath = true;       // 搜索路径
    bool searchInDescription = true; // 搜索描述
    bool searchInTags = true;       // 搜索标签
    bool searchInKeywords = true;   // 搜索关键词
    bool enabledOnly = true;        // 仅搜索启用的项目
    bool visibleOnly = true;        // 仅搜索可见的项目
    int maxResults = 50;            // 最大结果数
    double minRelevanceScore = 0.1; // 最小相关性分数
    QList<LaunchItemType> typeFilter; // 类型过滤
    QList<int> categoryFilter;      // 分类过滤
};

/**
 * @brief 搜索统计信息
 */
struct SearchStats {
    int totalSearches = 0;          // 总搜索次数
    int successfulSearches = 0;     // 成功搜索次数
    double avgSearchTime = 0.0;     // 平均搜索时间（毫秒）
    double avgResultCount = 0.0;    // 平均结果数量
    QDateTime lastSearch;           // 最后搜索时间
    QString mostSearchedTerm;       // 最常搜索的词
    QHash<QString, int> searchTerms; // 搜索词统计
};

/**
 * @brief 搜索引擎
 * 
 * 提供智能搜索功能，包括：
 * - 模糊搜索和精确搜索
 * - 相关性评分和排序
 * - 搜索建议和自动完成
 * - 搜索历史和统计
 * - 异步搜索处理
 */
class SearchEngine : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit SearchEngine(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~SearchEngine();

    /**
     * @brief 初始化搜索引擎
     * @return 是否成功
     */
    bool initialize();

    // 搜索功能
    
    /**
     * @brief 同步搜索
     * @param query 搜索查询
     * @param options 搜索选项
     * @return 搜索结果列表
     */
    QList<SearchResult> search(const QString &query, const SearchOptions &options = {});
    
    /**
     * @brief 异步搜索
     * @param query 搜索查询
     * @param options 搜索选项
     */
    void searchAsync(const QString &query, const SearchOptions &options = {});
    
    /**
     * @brief 取消当前搜索
     */
    void cancelSearch();
    
    /**
     * @brief 检查是否正在搜索
     * @return 是否正在搜索
     */
    bool isSearching() const;

    // 搜索建议
    
    /**
     * @brief 获取搜索建议
     * @param partialQuery 部分查询
     * @param maxSuggestions 最大建议数
     * @return 建议列表
     */
    QStringList getSuggestions(const QString &partialQuery, int maxSuggestions = 10);
    
    /**
     * @brief 获取自动完成
     * @param partialQuery 部分查询
     * @param maxCompletions 最大完成数
     * @return 完成列表
     */
    QStringList getAutoCompletions(const QString &partialQuery, int maxCompletions = 5);

    // 搜索历史
    
    /**
     * @brief 添加搜索历史
     * @param query 搜索查询
     * @param resultCount 结果数量
     */
    void addSearchHistory(const QString &query, int resultCount);
    
    /**
     * @brief 获取搜索历史
     * @param maxCount 最大数量
     * @return 历史列表
     */
    QStringList getSearchHistory(int maxCount = 20) const;
    
    /**
     * @brief 清空搜索历史
     */
    void clearSearchHistory();

    // 索引管理
    
    /**
     * @brief 重建搜索索引
     * @return 是否成功
     */
    bool rebuildIndex();
    
    /**
     * @brief 更新索引项
     * @param item 启动项
     */
    void updateIndexItem(const LaunchItem &item);
    
    /**
     * @brief 移除索引项
     * @param itemId 启动项ID
     */
    void removeIndexItem(int itemId);

    // 统计和配置
    
    /**
     * @brief 获取搜索统计
     * @return 统计信息
     */
    SearchStats getStatistics() const;
    
    /**
     * @brief 重置统计信息
     */
    void resetStatistics();
    
    /**
     * @brief 设置默认搜索选项
     * @param options 搜索选项
     */
    void setDefaultSearchOptions(const SearchOptions &options);
    
    /**
     * @brief 获取默认搜索选项
     * @return 搜索选项
     */
    SearchOptions getDefaultSearchOptions() const;

signals:
    /**
     * @brief 搜索完成信号
     * @param query 搜索查询
     * @param results 搜索结果
     * @param searchTime 搜索时间（毫秒）
     */
    void searchCompleted(const QString &query, const QList<SearchResult> &results, int searchTime);
    
    /**
     * @brief 搜索开始信号
     * @param query 搜索查询
     */
    void searchStarted(const QString &query);
    
    /**
     * @brief 搜索取消信号
     * @param query 搜索查询
     */
    void searchCancelled(const QString &query);
    
    /**
     * @brief 索引更新信号
     * @param itemCount 项目数量
     */
    void indexUpdated(int itemCount);

private slots:
    /**
     * @brief 异步搜索完成处理
     */
    void onAsyncSearchFinished();
    
    /**
     * @brief 配置变更处理
     * @param key 配置键
     * @param value 配置值
     */
    void onConfigChanged(const QString &key, const QVariant &value);

private:
    /**
     * @brief 执行搜索逻辑
     * @param query 搜索查询
     * @param options 搜索选项
     * @return 搜索结果
     */
    QList<SearchResult> performSearch(const QString &query, const SearchOptions &options);
    
    /**
     * @brief 计算相关性分数
     * @param item 启动项
     * @param query 搜索查询
     * @param options 搜索选项
     * @return 搜索结果
     */
    SearchResult calculateRelevance(const LaunchItem &item, const QString &query, const SearchOptions &options);
    
    /**
     * @brief 模糊匹配
     * @param text 文本
     * @param query 查询
     * @return 匹配分数 (0-1)
     */
    double fuzzyMatch(const QString &text, const QString &query) const;
    
    /**
     * @brief 精确匹配
     * @param text 文本
     * @param query 查询
     * @param caseSensitive 大小写敏感
     * @return 匹配分数 (0-1)
     */
    double exactMatch(const QString &text, const QString &query, bool caseSensitive) const;
    
    /**
     * @brief 高亮匹配文本
     * @param text 原始文本
     * @param query 查询
     * @param caseSensitive 大小写敏感
     * @return 高亮文本
     */
    QString highlightMatch(const QString &text, const QString &query, bool caseSensitive) const;
    
    /**
     * @brief 构建搜索索引
     */
    void buildSearchIndex();
    
    /**
     * @brief 更新统计信息
     * @param query 搜索查询
     * @param resultCount 结果数量
     * @param searchTime 搜索时间
     */
    void updateStatistics(const QString &query, int resultCount, int searchTime);
    
    /**
     * @brief 加载配置
     */
    void loadConfig();

private:
    // 服务引用
    std::shared_ptr<LaunchItemRepository> m_repository;
    std::shared_ptr<ConfigManager> m_configManager;
    
    // 搜索状态
    mutable QReadWriteLock m_lock;
    bool m_searching = false;
    QString m_currentQuery;
    
    // 异步搜索
    QFutureWatcher<QList<SearchResult>> *m_searchWatcher;
    QFuture<QList<SearchResult>> m_searchFuture;
    
    // 搜索配置
    SearchOptions m_defaultOptions;
    
    // 搜索历史和统计
    QStringList m_searchHistory;
    SearchStats m_statistics;
    
    // 搜索索引（简化实现）
    QHash<int, LaunchItem> m_searchIndex;
    
    // 常量
    static const int MAX_SEARCH_HISTORY = 100;
    static const QString HIGHLIGHT_START_TAG;
    static const QString HIGHLIGHT_END_TAG;
};
