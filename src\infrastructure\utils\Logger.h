#pragma once

#include <QObject>
#include <QLoggingCategory>
#include <QMutex>
#include <QFile>
#include <QTextStream>
#include <QTimer>
#include <QDateTime>
#include <memory>

/**
 * @brief 日志条目结构
 */
struct LogEntry {
    QDateTime timestamp;        // 时间戳
    QtMsgType type;            // 日志类型
    QString category;          // 日志分类
    QString message;           // 日志消息
    QString file;              // 源文件
    int line;                  // 行号
    QString function;          // 函数名
    qint64 threadId;          // 线程ID
};

/**
 * @brief 日志管理器
 * 
 * 提供统一的日志记录功能，支持：
 * - 文件日志输出
 * - 控制台日志输出
 * - 日志级别过滤
 * - 日志文件轮转
 * - 异步日志写入
 */
class Logger : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取日志管理器单例
     * @return 日志管理器实例
     */
    static Logger& instance();

    /**
     * @brief 初始化日志系统
     * @param logFilePath 日志文件路径
     * @param maxFileSize 最大文件大小（字节）
     * @param maxFileCount 最大文件数量
     * @return 是否初始化成功
     */
    bool initialize(const QString &logFilePath = QString(),
                   qint64 maxFileSize = 10 * 1024 * 1024,  // 10MB
                   int maxFileCount = 5);

    /**
     * @brief 设置日志文件路径
     * @param filePath 文件路径
     */
    void setLogFile(const QString &filePath);

    /**
     * @brief 设置最大文件大小
     * @param maxSize 最大大小（字节）
     */
    void setMaxFileSize(qint64 maxSize);

    /**
     * @brief 设置最大文件数量
     * @param maxCount 最大数量
     */
    void setMaxFileCount(int maxCount);

    /**
     * @brief 启用/禁用控制台输出
     * @param enabled 是否启用
     */
    void setConsoleOutputEnabled(bool enabled);

    /**
     * @brief 启用/禁用文件输出
     * @param enabled 是否启用
     */
    void setFileOutputEnabled(bool enabled);

    /**
     * @brief 设置日志级别过滤
     * @param category 分类名称
     * @param type 最低日志级别
     */
    void setCategoryFilter(const QString &category, QtMsgType type);

    /**
     * @brief 移除分类过滤
     * @param category 分类名称
     */
    void removeCategoryFilter(const QString &category);

    /**
     * @brief 清除所有过滤器
     */
    void clearFilters();

    /**
     * @brief 获取最近的日志条目
     * @param count 数量限制
     * @return 日志条目列表
     */
    QList<LogEntry> getRecentEntries(int count = 100) const;

    /**
     * @brief 获取指定类型的日志条目
     * @param type 日志类型
     * @param count 数量限制
     * @return 日志条目列表
     */
    QList<LogEntry> getEntriesByType(QtMsgType type, int count = 100) const;

    /**
     * @brief 获取指定分类的日志条目
     * @param category 分类名称
     * @param count 数量限制
     * @return 日志条目列表
     */
    QList<LogEntry> getEntriesByCategory(const QString &category, int count = 100) const;

    /**
     * @brief 清空内存中的日志条目
     */
    void clearMemoryLog();

    /**
     * @brief 强制刷新日志到文件
     */
    void flush();

    /**
     * @brief 关闭日志系统
     */
    void shutdown();

signals:
    /**
     * @brief 新日志条目信号
     * @param entry 日志条目
     */
    void logEntryAdded(const LogEntry &entry);

    /**
     * @brief 日志文件轮转信号
     * @param newFilePath 新文件路径
     */
    void logFileRotated(const QString &newFilePath);

private slots:
    /**
     * @brief 检查日志文件轮转
     */
    void checkLogRotation();

    /**
     * @brief 异步写入日志
     */
    void writeLogAsync();

private:
    /**
     * @brief 构造函数（私有）
     */
    explicit Logger(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~Logger();

    /**
     * @brief Qt消息处理函数
     * @param type 消息类型
     * @param context 消息上下文
     * @param msg 消息内容
     */
    static void messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg);

    /**
     * @brief 处理日志消息
     * @param type 消息类型
     * @param context 消息上下文
     * @param msg 消息内容
     */
    void handleMessage(QtMsgType type, const QMessageLogContext &context, const QString &msg);

    /**
     * @brief 写入日志到文件
     * @param entry 日志条目
     */
    void writeToFile(const LogEntry &entry);

    /**
     * @brief 写入日志到控制台
     * @param entry 日志条目
     */
    void writeToConsole(const LogEntry &entry);

    /**
     * @brief 格式化日志条目
     * @param entry 日志条目
     * @return 格式化后的字符串
     */
    QString formatLogEntry(const LogEntry &entry) const;

    /**
     * @brief 执行日志文件轮转
     */
    void rotateLogFile();

    /**
     * @brief 获取日志类型字符串
     * @param type 日志类型
     * @return 类型字符串
     */
    QString getLogTypeString(QtMsgType type) const;

    /**
     * @brief 检查是否应该过滤日志
     * @param category 分类
     * @param type 类型
     * @return 是否过滤
     */
    bool shouldFilter(const QString &category, QtMsgType type) const;

private:
    mutable QMutex m_mutex;                    // 线程安全锁
    QString m_logFilePath;                     // 日志文件路径
    qint64 m_maxFileSize;                      // 最大文件大小
    int m_maxFileCount;                        // 最大文件数量
    bool m_consoleOutputEnabled;               // 控制台输出开关
    bool m_fileOutputEnabled;                  // 文件输出开关
    bool m_initialized;                        // 初始化状态

    std::unique_ptr<QFile> m_logFile;          // 日志文件
    std::unique_ptr<QTextStream> m_logStream;  // 日志流
    QTimer *m_rotationTimer;                   // 轮转检查定时器
    QTimer *m_writeTimer;                      // 异步写入定时器

    QHash<QString, QtMsgType> m_categoryFilters; // 分类过滤器
    QList<LogEntry> m_memoryLog;               // 内存日志缓存
    QList<LogEntry> m_pendingWrites;           // 待写入日志队列

    static Logger* s_instance;                 // 单例实例
    static const int MAX_MEMORY_LOG_SIZE = 1000; // 最大内存日志数量
    static const int ROTATION_CHECK_INTERVAL = 60000; // 轮转检查间隔（毫秒）
    static const int WRITE_INTERVAL = 1000;    // 写入间隔（毫秒）
};

// 便利宏定义
#define LOG_DEBUG(category, message) \
    qCDebug(category) << message

#define LOG_INFO(category, message) \
    qCInfo(category) << message

#define LOG_WARNING(category, message) \
    qCWarning(category) << message

#define LOG_ERROR(category, message) \
    qCCritical(category) << message

#define LOG_FATAL(category, message) \
    qCCritical(category) << message
