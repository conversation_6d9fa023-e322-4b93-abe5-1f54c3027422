#include "Application.h"
#include "ServiceContainer.h"

#include <QApplication>
#include <QStandardPaths>
#include <QDir>
#include <QLocalSocket>
#include <QLoggingCategory>
#include <QMessageBox>
#include <QSettings>

// 业务组件
#include "business/managers/LaunchManager.h"
#include "business/managers/SearchEngine.h"
#include "business/managers/RecommendationSystem.h"
#include "business/managers/ConfigManager.h"
#include "business/managers/PluginManager.h"
#include "business/managers/HotkeyManager.h"

// 服务组件
#include "services/DatabaseService.h"
#include "services/LoggingService.h"
#include "services/FileService.h"
#include "services/SystemService.h"
#include "services/CacheService.h"
#include "services/NetworkService.h"

// 数据访问组件
#include "data/repositories/LaunchItemRepository.h"
#include "data/repositories/CategoryRepository.h"
#include "data/repositories/UserActionRepository.h"
#include "data/repositories/SettingsRepository.h"

// 基础设施组件
#include "infrastructure/database/DatabaseManager.h"
#include "infrastructure/utils/Logger.h"

// UI组件
#include "presentation/views/MainWindow.h"
#include "presentation/views/TrayIcon.h"

Q_LOGGING_CATEGORY(lcApp, "application")

// 静态成员初始化
Application* Application::s_instance = nullptr;
const QString Application::SINGLE_INSTANCE_SERVER_NAME = "KKQuickLaunch_SingleInstance";

Application::Application(QObject *parent)
    : QObject(parent)
    , m_serviceContainer(std::make_unique<ServiceContainer>())
    , m_initialized(false)
    , m_shuttingDown(false)
{
    // 设置单例
    s_instance = this;
    
    // 设置应用程序数据路径
    m_applicationDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    
    qCDebug(lcApp) << "Application created, data path:" << m_applicationDataPath;
}

Application::~Application()
{
    qCDebug(lcApp) << "Application destructor called";
    cleanup();
    s_instance = nullptr;
}

bool Application::initialize()
{
    if (m_initialized) {
        qCWarning(lcApp) << "Application already initialized";
        return true;
    }
    
    qCInfo(lcApp) << "Initializing application...";
    
    try {
        // 1. 设置单实例服务器
        if (!setupSingleInstanceServer()) {
            qCCritical(lcApp) << "Failed to setup single instance server";
            return false;
        }
        
        // 2. 设置日志系统
        if (!setupLogging()) {
            qCCritical(lcApp) << "Failed to setup logging";
            return false;
        }
        
        // 3. 设置数据库
        if (!setupDatabase()) {
            qCCritical(lcApp) << "Failed to setup database";
            return false;
        }
        
        // 4. 设置服务容器
        if (!setupServices()) {
            qCCritical(lcApp) << "Failed to setup services";
            return false;
        }
        
        // 5. 设置全局热键
        if (!setupHotkeys()) {
            qCWarning(lcApp) << "Failed to setup hotkeys (non-critical)";
        }
        
        // 6. 设置系统托盘
        if (!setupTrayIcon()) {
            qCWarning(lcApp) << "Failed to setup tray icon (non-critical)";
        }
        
        // 7. 加载插件
        if (!loadPlugins()) {
            qCWarning(lcApp) << "Failed to load plugins (non-critical)";
        }
        
        // 8. 启动后台任务
        startBackgroundTasks();
        
        // 9. 注册系统事件处理
        registerSystemEventHandlers();
        
        // 10. 恢复应用程序状态
        restoreApplicationState();
        
        m_initialized = true;
        qCInfo(lcApp) << "Application initialized successfully";
        
        return true;
        
    } catch (const std::exception& e) {
        qCCritical(lcApp) << "Exception during initialization:" << e.what();
        return false;
    }
}

ServiceContainer* Application::serviceContainer() const
{
    return m_serviceContainer.get();
}

ConfigManager* Application::configManager() const
{
    return m_configManager.get();
}

Application* Application::instance()
{
    return s_instance;
}

void Application::showMainWindow()
{
    if (!m_mainWindow) {
        // 延迟创建主窗口
        m_mainWindow = std::make_unique<MainWindow>();
        
        // 连接信号
        connect(m_mainWindow.get(), &MainWindow::windowClosed,
                this, &Application::hideMainWindow);
    }
    
    m_mainWindow->show();
    m_mainWindow->raise();
    m_mainWindow->activateWindow();
    
    emit mainWindowVisibilityChanged(true);
    qCDebug(lcApp) << "Main window shown";
}

void Application::hideMainWindow()
{
    if (m_mainWindow) {
        m_mainWindow->hide();
        emit mainWindowVisibilityChanged(false);
        qCDebug(lcApp) << "Main window hidden";
    }
}

void Application::toggleMainWindow()
{
    if (m_mainWindow && m_mainWindow->isVisible()) {
        hideMainWindow();
    } else {
        showMainWindow();
    }
}

void Application::quit()
{
    if (m_shuttingDown) {
        return;
    }
    
    qCInfo(lcApp) << "Application quit requested";
    m_shuttingDown = true;
    
    emit aboutToQuit();
    
    // 保存应用程序状态
    saveApplicationState();
    
    // 清理资源
    cleanup();
    
    // 退出应用程序
    QApplication::quit();
}

void Application::onSystemShutdown()
{
    qCInfo(lcApp) << "System shutdown detected";
    quit();
}

void Application::onSessionChanged()
{
    qCDebug(lcApp) << "Session changed";
    // 处理会话变更逻辑
}

void Application::onSingleInstanceActivated()
{
    qCDebug(lcApp) << "Single instance activation requested";
    showMainWindow();
}

void Application::onTrayIconActivated(QSystemTrayIcon::ActivationReason reason)
{
    switch (reason) {
    case QSystemTrayIcon::Trigger:
    case QSystemTrayIcon::DoubleClick:
        toggleMainWindow();
        break;
    case QSystemTrayIcon::MiddleClick:
        // 中键点击可以执行其他操作
        break;
    default:
        break;
    }
}

void Application::onGlobalHotkeyActivated(const QString &hotkeyId)
{
    qCDebug(lcApp) << "Global hotkey activated:" << hotkeyId;
    
    if (hotkeyId == "show_main_window") {
        toggleMainWindow();
    } else if (hotkeyId == "quick_search") {
        showMainWindow();
        // 聚焦到搜索框
        if (m_mainWindow) {
            m_mainWindow->focusSearchBox();
        }
    }
    // 可以添加更多热键处理
}

void Application::onNewConnection()
{
    if (!m_singleInstanceServer) {
        return;
    }
    
    QLocalSocket *socket = m_singleInstanceServer->nextPendingConnection();
    if (!socket) {
        return;
    }
    
    connect(socket, &QLocalSocket::readyRead, [this, socket]() {
        QTextStream stream(socket);
        QString command = stream.readLine();
        
        qCDebug(lcApp) << "Received single instance command:" << command;
        
        if (command == "ACTIVATE") {
            onSingleInstanceActivated();
        }
        
        socket->deleteLater();
    });
    
    connect(socket, &QLocalSocket::disconnected, socket, &QLocalSocket::deleteLater);
}

void Application::onConfigChanged(const QString &key, const QVariant &value)
{
    qCDebug(lcApp) << "Config changed:" << key << "=" << value;
    
    // 处理配置变更
    if (key == "ui.theme") {
        // 更新主题
    } else if (key == "behavior.auto_start") {
        // 更新开机自启动设置
    }
    // 可以添加更多配置处理
}

void Application::onBackgroundTask()
{
    if (m_shuttingDown) {
        return;
    }
    
    // 执行后台维护任务
    try {
        // 1. 清理缓存
        if (auto cacheService = m_serviceContainer->getService<CacheService>()) {
            // cacheService->cleanup();
        }
        
        // 2. 数据库维护
        if (m_databaseManager) {
            // m_databaseManager->performMaintenance();
        }
        
        // 3. 更新推荐模型
        if (m_recommendationSystem) {
            // m_recommendationSystem->updateModel();
        }
        
        qCDebug(lcApp) << "Background task completed";
        
    } catch (const std::exception& e) {
        qCWarning(lcApp) << "Background task failed:" << e.what();
    }
}

bool Application::setupLogging()
{
    try {
        // 初始化日志服务
        auto loggingService = std::make_shared<LoggingService>();

        // 设置日志文件路径
        QString logPath = m_applicationDataPath + "/logs/app.log";
        loggingService->setLogFile(logPath);
        loggingService->setMaxFileSize(10 * 1024 * 1024); // 10MB
        loggingService->setMaxFileCount(5);

        // 注册到服务容器
        m_serviceContainer->registerService<LoggingService>(loggingService);

        qCDebug(lcApp) << "Logging setup completed";
        return true;

    } catch (const std::exception& e) {
        qCCritical(lcApp) << "Failed to setup logging:" << e.what();
        return false;
    }
}

bool Application::setupDatabase()
{
    try {
        // 创建数据库管理器
        m_databaseManager = std::make_unique<DatabaseManager>();

        // 初始化数据库
        QString dbPath = m_applicationDataPath + "/quicklaunch.db";
        if (!m_databaseManager->initialize(dbPath)) {
            qCCritical(lcApp) << "Failed to initialize database";
            return false;
        }

        // 注册数据库服务
        auto databaseService = std::make_shared<DatabaseService>(m_databaseManager.get());
        m_serviceContainer->registerService<DatabaseService>(databaseService);

        qCDebug(lcApp) << "Database setup completed";
        return true;

    } catch (const std::exception& e) {
        qCCritical(lcApp) << "Failed to setup database:" << e.what();
        return false;
    }
}

bool Application::setupServices()
{
    try {
        // 注册基础服务
        m_serviceContainer->registerService<FileService, FileService>();
        m_serviceContainer->registerService<SystemService, SystemService>();
        m_serviceContainer->registerService<CacheService, CacheService>();
        m_serviceContainer->registerService<NetworkService, NetworkService>();

        // 注册数据访问层
        m_serviceContainer->registerService<LaunchItemRepository, LaunchItemRepository>();
        m_serviceContainer->registerService<CategoryRepository, CategoryRepository>();
        m_serviceContainer->registerService<UserActionRepository, UserActionRepository>();
        m_serviceContainer->registerService<SettingsRepository, SettingsRepository>();

        // 创建配置管理器
        m_configManager = std::make_unique<ConfigManager>();
        connect(m_configManager.get(), &ConfigManager::valueChanged,
                this, &Application::onConfigChanged);

        // 创建业务管理器
        m_launchManager = std::make_shared<LaunchManager>();
        m_searchEngine = std::make_shared<SearchEngine>();
        m_recommendationSystem = std::make_shared<RecommendationSystem>();

        // 注册业务服务
        m_serviceContainer->registerService<LaunchManager>(m_launchManager);
        m_serviceContainer->registerService<SearchEngine>(m_searchEngine);
        m_serviceContainer->registerService<RecommendationSystem>(m_recommendationSystem);
        m_serviceContainer->registerService<ConfigManager>(
            std::shared_ptr<ConfigManager>(m_configManager.get(), [](ConfigManager*){}));

        // 初始化所有服务
        m_serviceContainer->initializeServices();

        qCDebug(lcApp) << "Services setup completed";
        return true;

    } catch (const std::exception& e) {
        qCCritical(lcApp) << "Failed to setup services:" << e.what();
        return false;
    }
}

bool Application::setupHotkeys()
{
    try {
        // 创建热键管理器
        m_hotkeyManager = std::make_unique<HotkeyManager>();

        // 连接热键激活信号
        connect(m_hotkeyManager.get(), &HotkeyManager::hotkeyActivated,
                this, &Application::onGlobalHotkeyActivated);

        // 注册默认热键
        m_hotkeyManager->registerHotkey("show_main_window", QKeySequence("Ctrl+Alt+Q"), true);
        m_hotkeyManager->registerHotkey("quick_search", QKeySequence("Ctrl+Alt+S"), true);

        qCDebug(lcApp) << "Hotkeys setup completed";
        return true;

    } catch (const std::exception& e) {
        qCWarning(lcApp) << "Failed to setup hotkeys:" << e.what();
        return false;
    }
}

bool Application::setupTrayIcon()
{
    try {
        // 检查系统托盘是否可用
        if (!QSystemTrayIcon::isSystemTrayAvailable()) {
            qCWarning(lcApp) << "System tray is not available";
            return false;
        }

        // 创建托盘图标
        m_trayIcon = std::make_unique<TrayIcon>();

        // 连接托盘图标信号
        connect(m_trayIcon.get(), &TrayIcon::activated,
                this, &Application::onTrayIconActivated);
        connect(m_trayIcon.get(), &TrayIcon::showMainWindowRequested,
                this, &Application::showMainWindow);
        connect(m_trayIcon.get(), &TrayIcon::quitRequested,
                this, &Application::quit);

        // 显示托盘图标
        m_trayIcon->show();

        qCDebug(lcApp) << "Tray icon setup completed";
        return true;

    } catch (const std::exception& e) {
        qCWarning(lcApp) << "Failed to setup tray icon:" << e.what();
        return false;
    }
}

bool Application::loadPlugins()
{
    try {
        // 创建插件管理器
        m_pluginManager = std::make_unique<PluginManager>();

        // 设置插件目录
        QString pluginDir = m_applicationDataPath + "/plugins";
        QDir().mkpath(pluginDir);

        // 扫描并加载插件
        // m_pluginManager->scanPluginDirectory(pluginDir);

        qCDebug(lcApp) << "Plugins loaded";
        return true;

    } catch (const std::exception& e) {
        qCWarning(lcApp) << "Failed to load plugins:" << e.what();
        return false;
    }
}

void Application::startBackgroundTasks()
{
    // 创建后台任务定时器
    m_backgroundTaskTimer = std::make_unique<QTimer>();
    m_backgroundTaskTimer->setInterval(BACKGROUND_TASK_INTERVAL);

    connect(m_backgroundTaskTimer.get(), &QTimer::timeout,
            this, &Application::onBackgroundTask);

    // 启动定时器
    m_backgroundTaskTimer->start();

    qCDebug(lcApp) << "Background tasks started";
}

bool Application::setupSingleInstanceServer()
{
    try {
        // 创建本地服务器
        m_singleInstanceServer = std::make_unique<QLocalServer>();

        // 移除可能存在的旧服务器
        QLocalServer::removeServer(SINGLE_INSTANCE_SERVER_NAME);

        // 监听连接
        if (!m_singleInstanceServer->listen(SINGLE_INSTANCE_SERVER_NAME)) {
            qCWarning(lcApp) << "Failed to start single instance server:"
                            << m_singleInstanceServer->errorString();
            return false;
        }

        // 连接新连接信号
        connect(m_singleInstanceServer.get(), &QLocalServer::newConnection,
                this, &Application::onNewConnection);

        qCDebug(lcApp) << "Single instance server setup completed";
        return true;

    } catch (const std::exception& e) {
        qCWarning(lcApp) << "Failed to setup single instance server:" << e.what();
        return false;
    }
}

void Application::registerSystemEventHandlers()
{
    // 注册应用程序退出处理
    connect(QApplication::instance(), &QApplication::aboutToQuit,
            this, &Application::saveApplicationState);

    // 注册系统关机处理
    // 在Windows上可以通过WM_QUERYENDSESSION消息处理
    // 在Linux上可以通过信号处理

    qCDebug(lcApp) << "System event handlers registered";
}

void Application::cleanup()
{
    if (m_shuttingDown) {
        return;
    }

    qCDebug(lcApp) << "Cleaning up application resources";

    // 停止后台任务
    if (m_backgroundTaskTimer) {
        m_backgroundTaskTimer->stop();
    }

    // 隐藏托盘图标
    if (m_trayIcon) {
        m_trayIcon->hide();
    }

    // 注销全局热键
    if (m_hotkeyManager) {
        // m_hotkeyManager->unregisterAllHotkeys();
    }

    // 卸载插件
    if (m_pluginManager) {
        // m_pluginManager->unloadAllPlugins();
    }

    // 关闭数据库
    if (m_databaseManager) {
        m_databaseManager->close();
    }

    // 停止单实例服务器
    if (m_singleInstanceServer) {
        m_singleInstanceServer->close();
        QLocalServer::removeServer(SINGLE_INSTANCE_SERVER_NAME);
    }

    qCDebug(lcApp) << "Application cleanup completed";
}

void Application::saveApplicationState()
{
    if (!m_configManager) {
        return;
    }

    qCDebug(lcApp) << "Saving application state";

    try {
        // 保存主窗口状态
        if (m_mainWindow) {
            m_configManager->setValue("ui.main_window.geometry", m_mainWindow->saveGeometry());
            m_configManager->setValue("ui.main_window.state", m_mainWindow->saveState());
            m_configManager->setValue("ui.main_window.visible", m_mainWindow->isVisible());
        }

        // 保存其他应用程序状态
        m_configManager->setValue("app.last_shutdown_time", QDateTime::currentDateTime());
        m_configManager->setValue("app.clean_shutdown", true);

        // 同步配置到磁盘
        m_configManager->sync();

        qCDebug(lcApp) << "Application state saved";

    } catch (const std::exception& e) {
        qCWarning(lcApp) << "Failed to save application state:" << e.what();
    }
}

void Application::restoreApplicationState()
{
    if (!m_configManager) {
        return;
    }

    qCDebug(lcApp) << "Restoring application state";

    try {
        // 检查上次是否正常关闭
        bool cleanShutdown = m_configManager->getValue("app.clean_shutdown", true).toBool();
        if (!cleanShutdown) {
            qCWarning(lcApp) << "Previous shutdown was not clean";
            // 可以执行恢复操作或显示恢复对话框
        }

        // 标记为非正常关闭，在正常退出时会重新设置为true
        m_configManager->setValue("app.clean_shutdown", false);

        // 根据配置决定是否显示主窗口
        bool showOnStartup = m_configManager->getValue("ui.show_on_startup", true).toBool();
        bool wasVisible = m_configManager->getValue("ui.main_window.visible", true).toBool();

        if (showOnStartup || wasVisible) {
            // 延迟显示主窗口，避免启动时的闪烁
            QTimer::singleShot(100, this, &Application::showMainWindow);
        }

        qCDebug(lcApp) << "Application state restored";

    } catch (const std::exception& e) {
        qCWarning(lcApp) << "Failed to restore application state:" << e.what();
    }
}
