# 智能启动助手 - 数据库SQL文件说明

## 📁 文件结构

```
sql/
├── create_tables.sql           # 数据库表结构创建
├── init_data.sql              # 初始化数据插入
├── maintenance.sql            # 数据库维护脚本
├── migrations/                # 数据库迁移脚本
│   └── 001_initial_schema.sql
├── queries/                   # 常用查询SQL
│   └── common_queries.sql
└── README.md                  # 本说明文件
```

## 🚀 快速开始

### 1. 创建新数据库

```cpp
// 在Qt应用程序中执行
QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE");
db.setDatabaseName("quicklaunch.db");

if (db.open()) {
    // 1. 创建表结构
    executeScriptFile("sql/create_tables.sql");
    
    // 2. 插入初始化数据
    executeScriptFile("sql/init_data.sql");
    
    qDebug() << "数据库初始化完成";
}
```

### 2. 执行SQL脚本的C++代码示例

```cpp
bool DatabaseManager::executeScriptFile(const QString &scriptPath)
{
    QFile file(scriptPath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qCritical() << "无法打开SQL脚本文件:" << scriptPath;
        return false;
    }
    
    QString script = file.readAll();
    file.close();
    
    // 分割SQL语句(以分号分隔)
    QStringList statements = script.split(';', Qt::SkipEmptyParts);
    
    QSqlDatabase db = QSqlDatabase::database();
    db.transaction(); // 开始事务
    
    for (const QString &statement : statements) {
        QString trimmed = statement.trimmed();
        if (trimmed.isEmpty() || trimmed.startsWith("--")) {
            continue; // 跳过空行和注释
        }
        
        QSqlQuery query(db);
        if (!query.exec(trimmed)) {
            qCritical() << "SQL执行失败:" << query.lastError().text();
            qCritical() << "SQL语句:" << trimmed;
            db.rollback(); // 回滚事务
            return false;
        }
    }
    
    db.commit(); // 提交事务
    return true;
}
```

## 📋 文件详细说明

### create_tables.sql
**用途**: 创建数据库的所有表结构、索引、触发器和视图

**主要内容**:
- 10个核心数据表
- 完整的索引设计
- 数据完整性约束
- 自动更新触发器
- 统计分析视图

**使用时机**: 
- 首次安装应用程序
- 数据库损坏后重建
- 开发环境初始化

### init_data.sql
**用途**: 插入系统默认配置和初始化数据

**主要内容**:
- 默认分类数据(开发工具、办公效率等)
- 应用程序配置项
- 默认快捷键设置
- 推荐规则配置
- 系统初始化日志

**使用时机**:
- 数据库首次创建后
- 恢复默认设置时
- 测试环境准备

### maintenance.sql
**用途**: 数据库维护和优化操作

**主要功能**:
- 数据库健康检查
- 过期数据清理
- 统计信息更新
- 索引重建优化
- 性能监控分析

**使用时机**:
- 定期维护(建议每周)
- 性能下降时
- 数据库体积过大时

### migrations/001_initial_schema.sql
**用途**: 数据库版本迁移脚本

**功能特点**:
- 版本控制管理
- 增量更新支持
- 数据备份保护
- 回滚机制支持

**使用时机**:
- 应用程序版本升级
- 数据库结构变更
- 功能更新部署

### queries/common_queries.sql
**用途**: 常用的数据查询SQL语句集合

**查询类别**:
- 启动项管理查询
- 统计分析查询
- 推荐系统查询
- 搜索历史分析
- 系统维护查询

**使用方式**:
- 复制需要的查询到代码中
- 作为开发参考
- 数据分析工具

## 🔧 在Qt中的集成示例

### 1. 数据库管理器类

```cpp
class DatabaseManager : public QObject
{
    Q_OBJECT
    
public:
    explicit DatabaseManager(QObject *parent = nullptr);
    
    // 初始化数据库
    bool initializeDatabase(const QString &dbPath);
    
    // 执行维护操作
    bool performMaintenance();
    
    // 执行迁移
    bool runMigrations();
    
    // 常用查询方法
    QList<LaunchItem> searchLaunchItems(const QString &query);
    QList<LaunchItem> getRecentItems(int limit = 10);
    QList<AppRecommendation> getRecommendations(const QString &context);
    
private:
    bool executeScriptFile(const QString &scriptPath);
    bool checkDatabaseVersion();
    void logDatabaseOperation(const QString &operation, bool success);
    
    QSqlDatabase m_database;
    QString m_databasePath;
};
```

### 2. 初始化流程

```cpp
bool DatabaseManager::initializeDatabase(const QString &dbPath)
{
    m_databasePath = dbPath;
    
    // 1. 打开数据库连接
    m_database = QSqlDatabase::addDatabase("QSQLITE");
    m_database.setDatabaseName(dbPath);
    
    if (!m_database.open()) {
        qCritical() << "无法打开数据库:" << m_database.lastError();
        return false;
    }
    
    // 2. 启用外键约束
    QSqlQuery query(m_database);
    query.exec("PRAGMA foreign_keys = ON");
    
    // 3. 检查是否需要初始化
    bool needsInit = !query.exec("SELECT COUNT(*) FROM sqlite_master WHERE type='table'") 
                     || query.next() && query.value(0).toInt() == 0;
    
    if (needsInit) {
        // 4. 创建表结构
        if (!executeScriptFile(":/sql/create_tables.sql")) {
            return false;
        }
        
        // 5. 插入初始数据
        if (!executeScriptFile(":/sql/init_data.sql")) {
            return false;
        }
        
        qInfo() << "数据库初始化完成";
    } else {
        // 6. 检查并执行迁移
        runMigrations();
    }
    
    return true;
}
```

### 3. 查询示例

```cpp
QList<LaunchItem> DatabaseManager::searchLaunchItems(const QString &searchTerm)
{
    QList<LaunchItem> items;
    
    // 使用common_queries.sql中的搜索查询
    QString sql = R"(
        SELECT 
            li.id, li.name, li.path, li.icon_data,
            c.display_name as category, li.use_count,
            (CASE WHEN li.name LIKE ? THEN 10 ELSE 0 END +
             CASE WHEN li.description LIKE ? THEN 5 ELSE 0 END +
             CASE WHEN li.tags LIKE ? THEN 3 ELSE 0 END) as relevance_score
        FROM launch_items li
        LEFT JOIN categories c ON li.category_id = c.id
        WHERE li.is_enabled = 1
          AND (li.name LIKE ? OR li.description LIKE ? OR li.tags LIKE ?)
        ORDER BY relevance_score DESC, li.use_count DESC
        LIMIT 20
    )";
    
    QSqlQuery query(m_database);
    query.prepare(sql);
    
    QString searchPattern = "%" + searchTerm + "%";
    for (int i = 0; i < 6; ++i) {
        query.addBindValue(searchPattern);
    }
    
    if (query.exec()) {
        while (query.next()) {
            LaunchItem item;
            item.id = query.value("id").toInt();
            item.name = query.value("name").toString();
            item.path = query.value("path").toString();
            item.iconData = query.value("icon_data").toByteArray();
            item.category = query.value("category").toString();
            item.useCount = query.value("use_count").toInt();
            
            items.append(item);
        }
    }
    
    return items;
}
```

## 📊 性能优化建议

### 1. 索引使用
- 所有常用查询字段都已建立索引
- 复合索引优化多条件查询
- 定期使用`ANALYZE`更新统计信息

### 2. 查询优化
```cpp
// 使用参数化查询避免SQL注入
QSqlQuery query;
query.prepare("SELECT * FROM launch_items WHERE name LIKE ?");
query.addBindValue("%" + searchTerm + "%");

// 使用事务批量操作
db.transaction();
for (const auto& item : items) {
    // 批量插入操作
}
db.commit();
```

### 3. 内存管理
```cpp
// 设置合适的缓存大小
query.exec("PRAGMA cache_size = 10000");

// 使用WAL模式提高并发性能
query.exec("PRAGMA journal_mode = WAL");
```

## 🛠️ 维护建议

### 1. 定期维护
```cpp
// 建议每周执行一次维护
QTimer *maintenanceTimer = new QTimer(this);
connect(maintenanceTimer, &QTimer::timeout, this, &DatabaseManager::performMaintenance);
maintenanceTimer->start(7 * 24 * 60 * 60 * 1000); // 7天
```

### 2. 数据备份
```cpp
bool DatabaseManager::createBackup(const QString &backupPath)
{
    // 使用SQLite的备份API
    sqlite3 *source = nullptr;
    sqlite3 *backup = nullptr;
    
    sqlite3_open(m_databasePath.toUtf8().data(), &source);
    sqlite3_open(backupPath.toUtf8().data(), &backup);
    
    sqlite3_backup *pBackup = sqlite3_backup_init(backup, "main", source, "main");
    if (pBackup) {
        sqlite3_backup_step(pBackup, -1);
        sqlite3_backup_finish(pBackup);
    }
    
    sqlite3_close(source);
    sqlite3_close(backup);
    
    return pBackup != nullptr;
}
```

### 3. 错误处理
```cpp
void DatabaseManager::handleDatabaseError(const QSqlError &error)
{
    qCritical() << "数据库错误:" << error.text();
    
    // 记录到系统日志
    QSqlQuery logQuery;
    logQuery.prepare("INSERT INTO system_logs (log_level, category, message, details) VALUES (?, ?, ?, ?)");
    logQuery.addBindValue("ERROR");
    logQuery.addBindValue("database");
    logQuery.addBindValue("数据库操作失败");
    logQuery.addBindValue(error.text());
    logQuery.exec();
    
    // 通知用户
    emit databaseError(error.text());
}
```

## 📝 注意事项

1. **兼容性**: 所有SQL语句兼容SQLite 3.x和Qt 6.x
2. **编码**: 使用UTF-8编码确保中文支持
3. **事务**: 重要操作使用事务保证数据一致性
4. **备份**: 执行维护操作前建议备份数据库
5. **测试**: 在生产环境使用前充分测试所有SQL脚本

## 🔍 故障排除

### 常见问题
1. **外键约束错误**: 确保启用了`PRAGMA foreign_keys = ON`
2. **中文乱码**: 检查数据库连接的编码设置
3. **性能问题**: 执行`ANALYZE`和`VACUUM`优化
4. **锁定问题**: 使用WAL模式减少锁定冲突

### 调试方法
```cpp
// 启用SQL查询日志
QLoggingCategory::setFilterRules("qt.sql.driver.debug=true");

// 检查查询执行计划
QSqlQuery query;
query.exec("EXPLAIN QUERY PLAN SELECT * FROM launch_items WHERE name LIKE '%test%'");
while (query.next()) {
    qDebug() << query.value(0).toString();
}
```

---

**版本**: 1.0.0  
**最后更新**: 2024-12-17  
**维护者**: 开发团队
