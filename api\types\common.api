syntax = "v1"

// ============================================================================
// 通用类型定义
// ============================================================================

// 基础响应结构
type BaseResp {
    Code    int    `json:"code"`              // 响应码: 200=成功, 400=客户端错误, 500=服务器错误
    Message string `json:"message"`           // 响应消息
    Success bool   `json:"success"`           // 是否成功
    TraceId string `json:"trace_id"`          // 链路追踪ID
    Timestamp int64 `json:"timestamp"`        // 响应时间戳
}

// 分页请求参数
type PageReq {
    Page     int `form:"page,default=1"`      // 页码，从1开始
    PageSize int `form:"page_size,default=20"` // 每页大小，默认20
}

// 分页响应信息
type PageInfo {
    Page      int   `json:"page"`             // 当前页码
    PageSize  int   `json:"page_size"`        // 每页大小
    Total     int64 `json:"total"`            // 总记录数
    TotalPage int   `json:"total_page"`       // 总页数
    HasNext   bool  `json:"has_next"`         // 是否有下一页
    HasPrev   bool  `json:"has_prev"`         // 是否有上一页
}

// 时间范围查询参数
type TimeRangeReq {
    StartTime string `form:"start_time,optional"` // 开始时间 RFC3339格式
    EndTime   string `form:"end_time,optional"`   // 结束时间 RFC3339格式
    TimeRange string `form:"time_range,optional"` // 时间范围: 1h/1d/7d/30d/90d
}

// 排序参数
type SortReq {
    SortBy    string `form:"sort_by,optional"`    // 排序字段
    SortOrder string `form:"sort_order,optional"` // 排序方向: asc/desc
}

// 设备信息
type DeviceInfo {
    DeviceId      string `json:"device_id"`       // 设备唯一标识
    DeviceName    string `json:"device_name"`     // 设备名称
    Platform      string `json:"platform"`        // 操作系统平台
    ClientVersion string `json:"client_version"`  // 客户端版本
    UserAgent     string `json:"user_agent"`      // 用户代理字符串
    IpAddress     string `json:"ip_address"`      // IP地址
    Location      *LocationInfo `json:"location,optional"` // 地理位置信息
}

// 地理位置信息
type LocationInfo {
    Country     string  `json:"country"`          // 国家
    Province    string  `json:"province"`         // 省份
    City        string  `json:"city"`             // 城市
    Latitude    float64 `json:"latitude"`         // 纬度
    Longitude   float64 `json:"longitude"`        // 经度
    Timezone    string  `json:"timezone"`         // 时区
}

// 用户上下文信息
type UserContext {
    UserId        int64     `json:"user_id"`        // 用户ID
    SessionId     string    `json:"session_id"`     // 会话ID
    DeviceInfo    DeviceInfo `json:"device_info"`   // 设备信息
    CurrentTime   string    `json:"current_time"`   // 当前时间
    TimeOfDay     string    `json:"time_of_day"`    // 时间段: morning/afternoon/evening/night
    DayOfWeek     int       `json:"day_of_week"`    // 星期几 (1-7)
    IsWorkingTime bool      `json:"is_working_time"` // 是否工作时间
}

// 文件信息
type FileInfo {
    FileName      string `json:"file_name"`        // 文件名
    FilePath      string `json:"file_path"`        // 文件路径
    FileExtension string `json:"file_extension"`   // 文件扩展名
    FileSize      int64  `json:"file_size"`        // 文件大小(字节)
    MimeType      string `json:"mime_type"`        // MIME类型
    FileHash      string `json:"file_hash"`        // 文件哈希值
    CreatedAt     string `json:"created_at"`       // 创建时间
    ModifiedAt    string `json:"modified_at"`      // 修改时间
}

// 统计信息基础结构
type StatInfo {
    Name        string  `json:"name"`             // 统计项名称
    Value       int64   `json:"value"`            // 统计值
    Percentage  float64 `json:"percentage"`       // 百分比
    Change      float64 `json:"change"`           // 变化量
    ChangeRate  float64 `json:"change_rate"`      // 变化率
    Trend       string  `json:"trend"`            // 趋势: up/down/stable
}

// 时间序列数据点
type TimeSeriesPoint {
    Timestamp string  `json:"timestamp"`         // 时间戳
    Value     float64 `json:"value"`             // 数值
    Label     string  `json:"label,optional"`    // 标签
}

// 键值对
type KeyValue {
    Key   string `json:"key"`                   // 键
    Value string `json:"value"`                 // 值
    Type  string `json:"type,optional"`         // 值类型: string/int/bool/json
}

// 标签信息
type TagInfo {
    Id    int64  `json:"id"`                    // 标签ID
    Name  string `json:"name"`                  // 标签名称
    Color string `json:"color,optional"`        // 标签颜色
    Count int    `json:"count,optional"`        // 使用次数
}

// 错误详情
type ErrorDetail {
    Code      string `json:"code"`              // 错误码
    Message   string `json:"message"`           // 错误消息
    Field     string `json:"field,optional"`    // 错误字段
    Value     string `json:"value,optional"`    // 错误值
    Timestamp string `json:"timestamp"`         // 错误时间
}

// 操作结果
type OperationResult {
    Success      bool          `json:"success"`       // 是否成功
    Message      string        `json:"message"`       // 结果消息
    AffectedRows int64         `json:"affected_rows"` // 影响行数
    Data         interface{}   `json:"data,optional"` // 返回数据
    Errors       []ErrorDetail `json:"errors,optional"` // 错误详情列表
}

// 批量操作请求
type BatchOperationReq {
    Operation string  `json:"operation"`         // 操作类型: create/update/delete
    Ids       []int64 `json:"ids"`               // 操作的ID列表
    Data      interface{} `json:"data,optional"` // 操作数据
}

// 批量操作响应
type BatchOperationResp {
    BaseResp
    Results []OperationResult `json:"results"`   // 操作结果列表
    Summary struct {
        Total   int `json:"total"`             // 总数
        Success int `json:"success"`           // 成功数
        Failed  int `json:"failed"`            // 失败数
    } `json:"summary"`
}

// 导入导出相关
type ImportReq {
    Format string `json:"format"`              // 导入格式: json/csv/xml
    Data   string `json:"data"`                // 导入数据
    Options map[string]interface{} `json:"options,optional"` // 导入选项
}

type ExportReq {
    Format  string   `json:"format"`           // 导出格式: json/csv/xml
    Fields  []string `json:"fields,optional"`  // 导出字段
    Filter  interface{} `json:"filter,optional"` // 过滤条件
    Options map[string]interface{} `json:"options,optional"` // 导出选项
}

type ExportResp {
    BaseResp
    Data     string `json:"data"`              // 导出数据
    FileName string `json:"file_name"`         // 文件名
    FileSize int64  `json:"file_size"`         // 文件大小
    Format   string `json:"format"`            // 文件格式
}

// 搜索相关通用类型
type SearchFilter {
    Field    string      `json:"field"`         // 搜索字段
    Operator string      `json:"operator"`      // 操作符: eq/ne/gt/lt/like/in
    Value    interface{} `json:"value"`         // 搜索值
}

type SearchReq {
    Query     string         `json:"query"`           // 搜索关键词
    Filters   []SearchFilter `json:"filters,optional"` // 搜索过滤条件
    Highlight bool           `json:"highlight,optional"` // 是否高亮
    PageReq
    SortReq
}

// 配置相关通用类型
type ConfigItem {
    Key         string `json:"key"`             // 配置键
    Value       string `json:"value"`           // 配置值
    Type        string `json:"type"`            // 值类型
    Category    string `json:"category"`        // 配置分类
    Description string `json:"description"`     // 配置描述
    Required    bool   `json:"required"`        // 是否必需
    Editable    bool   `json:"editable"`        // 是否可编辑
    Options     []string `json:"options,optional"` // 可选值列表
    Validation  string `json:"validation,optional"` // 验证规则
}

// 权限相关
type Permission {
    Resource string   `json:"resource"`        // 资源
    Actions  []string `json:"actions"`         // 允许的操作
}

type Role {
    Id          int64        `json:"id"`        // 角色ID
    Name        string       `json:"name"`      // 角色名称
    Description string       `json:"description"` // 角色描述
    Permissions []Permission `json:"permissions"` // 权限列表
}

// 通知相关
type NotificationInfo {
    Id       string `json:"id"`                // 通知ID
    Type     string `json:"type"`              // 通知类型
    Title    string `json:"title"`             // 通知标题
    Content  string `json:"content"`           // 通知内容
    Priority string `json:"priority"`          // 优先级: low/normal/high/urgent
    Read     bool   `json:"read"`              // 是否已读
    Data     interface{} `json:"data,optional"` // 附加数据
    CreatedAt string `json:"created_at"`       // 创建时间
    ExpiresAt string `json:"expires_at,optional"` // 过期时间
}

// 健康检查相关
type HealthStatus {
    Status    string `json:"status"`           // 状态: healthy/unhealthy/degraded
    Component string `json:"component"`        // 组件名称
    Message   string `json:"message"`          // 状态消息
    Timestamp string `json:"timestamp"`        // 检查时间
    Details   map[string]interface{} `json:"details,optional"` // 详细信息
}

// 版本信息
type VersionInfo {
    Version     string `json:"version"`        // 版本号
    BuildTime   string `json:"build_time"`     // 构建时间
    GitCommit   string `json:"git_commit"`     // Git提交哈希
    GoVersion   string `json:"go_version"`     // Go版本
    Environment string `json:"environment"`    // 运行环境
}
