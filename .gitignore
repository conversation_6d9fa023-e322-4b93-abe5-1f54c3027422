# ============================================================================
# KK QuickLaunch - Git忽略文件
# ============================================================================

# 构建目录
build/
build-*/
debug/
release/
Debug/
Release/
x64/
x86/
Win32/

# CMake生成的文件
CMakeCache.txt
CMakeFiles/
CMakeScripts/
cmake_install.cmake
Makefile
*.cmake
!CMakeLists.txt

# Qt相关文件
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qm
.qmake.cache
.qmake.stash

# 编译产物
*.o
*.obj
*.exe
*.dll
*.so
*.dylib
*.a
*.lib
*.exp
*.pdb
*.ilk
*.map
*.dSYM/

# 临时文件
*.tmp
*.temp
*~
*.swp
*.swo
.DS_Store
Thumbs.db
desktop.ini

# IDE文件
.vscode/
.idea/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates

# Visual Studio Code
.vscode/
*.code-workspace

# Qt Creator
*.autosave
*.orig

# Conan
conandata.yml
conanfile.py
conaninfo.txt
conanbuildinfo.*
conan.lock
.conan/

# 包管理器
vcpkg_installed/
packages/
node_modules/

# 测试结果
Testing/
test_results/
*.gcov
*.gcda
*.gcno
coverage/
coverage.info
*.coverage

# 日志文件
*.log
logs/
*.log.*

# 数据库文件
*.db
*.sqlite
*.sqlite3
*.db-journal

# 配置文件（包含敏感信息）
config/local.json
config/production.json
.env
.env.local
.env.production

# 备份文件
*.bak
*.backup
backup/
backups/

# 缓存目录
cache/
.cache/
tmp/
temp/

# 文档生成
docs/html/
docs/latex/
docs/xml/
doxygen_warnings.txt

# 安装包
*.msi
*.dmg
*.pkg
*.deb
*.rpm
*.tar.gz
*.zip
*.7z
*.rar
dist/
installer/

# 性能分析
*.prof
*.perf
callgrind.out.*
massif.out.*

# 内存检查
valgrind-*.xml
*.memcheck

# 静态分析
cppcheck-*.xml
*.sarif

# 本地开发文件
local/
.local/
dev/
.dev/

# 用户数据
user_data/
app_data/
*.user_settings

# 插件开发
plugins/dev/
plugins/test/

# 资源文件（大文件）
resources/large/
assets/videos/
assets/audio/

# 平台特定
# Windows
*.lnk
*.url
*.bat.bak
*.cmd.bak

# macOS
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 编辑器备份
*.kate-swp
.*.swp
.*.swo

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws
out/

# Eclipse
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.project
.externalToolBuilders/
*.launch
.pydevproject
.cproject
.autotools
.factorypath
.buildpath
.target

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# 自定义忽略
# 添加项目特定的忽略规则
custom/
experimental/
sandbox/

# 第三方库（如果不使用包管理器）
third_party/
external/
vendor/

# 生成的文档
api_docs/
user_docs/

# 本地配置覆盖
local.cmake
local.pri

# 调试符号
*.pdb
*.dbg

# 崩溃转储
*.dmp
core.*

# 性能测试结果
benchmark_results/
perf_data/

# 国际化临时文件
*.ts.bak
lupdate.log

# 自动生成的版本文件
version.h
version.cpp
build_info.h

# 部署相关
deploy/
staging/
production/

# 证书和密钥（安全）
*.p12
*.pfx
*.key
*.pem
*.crt
*.cer
certificates/
keys/

# 最后添加：确保某些重要文件不被忽略
!.gitkeep
!README.md
!LICENSE
!CHANGELOG.md
