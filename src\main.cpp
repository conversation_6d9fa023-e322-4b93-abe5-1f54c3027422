#include <QApplication>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QStyleFactory>
#include <QTranslator>
#include <QLibraryInfo>
#include <QCommandLineParser>
#include <QLoggingCategory>

#include "application/Application.h"
#include "infrastructure/utils/Logger.h"

// 日志分类定义
Q_LOGGING_CATEGORY(lcMain, "main")

/**
 * @brief 设置应用程序基本信息
 */
void setupApplicationInfo() {
    QApplication::setApplicationName("KK QuickLaunch");
    QApplication::setApplicationDisplayName("智能启动助手");
    QApplication::setApplicationVersion(APP_VERSION);
    QApplication::setOrganizationName("KK Software");
    QApplication::setOrganizationDomain("kkquicklaunch.com");
    
    // 设置应用程序图标
    QApplication::setWindowIcon(QIcon(":/icons/app.png"));
}

/**
 * @brief 设置应用程序样式
 */
void setupApplicationStyle() {
    // 设置高DPI支持
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    // 设置样式
    QApplication::setStyle(QStyleFactory::create("Fusion"));
    
    // 设置调色板（可选的暗色主题）
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
    // 根据系统设置决定是否使用暗色主题
    // QApplication::setPalette(darkPalette);
}

/**
 * @brief 设置国际化
 */
void setupInternationalization() {
    // 加载Qt自带的翻译文件
    QTranslator *qtTranslator = new QTranslator(QApplication::instance());
    if (qtTranslator->load("qt_" + QLocale::system().name(),
                          QLibraryInfo::path(QLibraryInfo::TranslationsPath))) {
        QApplication::installTranslator(qtTranslator);
    }
    
    // 加载应用程序翻译文件
    QTranslator *appTranslator = new QTranslator(QApplication::instance());
    if (appTranslator->load("kkquicklaunch_" + QLocale::system().name(),
                           ":/translations")) {
        QApplication::installTranslator(appTranslator);
    }
}

/**
 * @brief 检查单实例运行
 */
bool checkSingleInstance() {
    // 使用QSharedMemory或QSystemSemaphore实现单实例检查
    // 这里简化实现，实际项目中需要更完善的单实例检查
    
    QString serverName = QApplication::applicationName() + "_SingleInstance";
    QLocalSocket socket;
    socket.connectToServer(serverName);
    
    if (socket.waitForConnected(500)) {
        // 已有实例在运行，发送激活信号
        QTextStream stream(&socket);
        stream << "ACTIVATE" << Qt::endl;
        socket.waitForBytesWritten(1000);
        return false;
    }
    
    return true;
}

/**
 * @brief 创建应用程序数据目录
 */
void createAppDirectories() {
    QStringList dirs = {
        QStandardPaths::writableLocation(QStandardPaths::AppDataLocation),
        QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs",
        QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/cache",
        QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/plugins",
        QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/backups"
    };
    
    for (const QString &dir : dirs) {
        QDir().mkpath(dir);
    }
}

/**
 * @brief 解析命令行参数
 */
void parseCommandLine(QApplication &app) {
    QCommandLineParser parser;
    parser.setApplicationDescription("智能启动助手 - 快速启动您的应用程序");
    parser.addHelpOption();
    parser.addVersionOption();
    
    // 添加自定义选项
    QCommandLineOption debugOption(QStringList() << "d" << "debug",
                                  "启用调试模式");
    parser.addOption(debugOption);
    
    QCommandLineOption configOption(QStringList() << "c" << "config",
                                   "指定配置文件路径",
                                   "config-file");
    parser.addOption(configOption);
    
    QCommandLineOption minimizedOption(QStringList() << "m" << "minimized",
                                      "启动时最小化到系统托盘");
    parser.addOption(minimizedOption);
    
    QCommandLineOption resetOption("reset",
                                  "重置所有设置到默认值");
    parser.addOption(resetOption);
    
    // 解析命令行
    parser.process(app);
    
    // 处理选项
    if (parser.isSet(debugOption)) {
        QLoggingCategory::setFilterRules("*.debug=true");
        qCDebug(lcMain) << "Debug mode enabled";
    }
    
    if (parser.isSet(configOption)) {
        QString configFile = parser.value(configOption);
        qCDebug(lcMain) << "Using config file:" << configFile;
        // 设置配置文件路径
    }
    
    if (parser.isSet(minimizedOption)) {
        qCDebug(lcMain) << "Starting minimized";
        // 设置启动时最小化标志
    }
    
    if (parser.isSet(resetOption)) {
        qCDebug(lcMain) << "Resetting settings";
        // 重置设置
        QSettings settings;
        settings.clear();
        settings.sync();
    }
}

/**
 * @brief 异常处理函数
 */
void handleException(const std::exception& e) {
    QString errorMsg = QString("未处理的异常: %1").arg(e.what());
    qCCritical(lcMain) << errorMsg;
    
    QMessageBox::critical(nullptr, 
                         "严重错误", 
                         errorMsg + "\n\n应用程序将退出。");
}

/**
 * @brief 主函数
 */
int main(int argc, char *argv[])
{
    // 创建QApplication实例
    QApplication app(argc, argv);
    
    try {
        // 设置应用程序基本信息
        setupApplicationInfo();
        
        // 解析命令行参数
        parseCommandLine(app);
        
        // 检查单实例运行
        if (!checkSingleInstance()) {
            qCInfo(lcMain) << "Application is already running";
            return 0;
        }
        
        // 创建应用程序目录
        createAppDirectories();
        
        // 初始化日志系统
        Logger::instance().initialize();
        qCInfo(lcMain) << "Application starting..." << "Version:" << APP_VERSION;
        
        // 设置样式和国际化
        setupApplicationStyle();
        setupInternationalization();
        
        // 创建并初始化应用程序
        Application application;
        if (!application.initialize()) {
            qCCritical(lcMain) << "Failed to initialize application";
            QMessageBox::critical(nullptr, 
                                 "初始化失败", 
                                 "应用程序初始化失败，请检查日志文件。");
            return -1;
        }
        
        qCInfo(lcMain) << "Application initialized successfully";
        
        // 运行应用程序
        int result = app.exec();
        
        qCInfo(lcMain) << "Application exiting with code:" << result;
        return result;
        
    } catch (const std::exception& e) {
        handleException(e);
        return -1;
    } catch (...) {
        qCCritical(lcMain) << "Unknown exception occurred";
        QMessageBox::critical(nullptr, 
                             "未知错误", 
                             "发生了未知错误，应用程序将退出。");
        return -1;
    }
}
