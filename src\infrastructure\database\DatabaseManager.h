#pragma once

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QMutex>
#include <QReadWriteLock>
#include <QTimer>
#include <QVariantList>
#include <memory>

/**
 * @brief 数据库查询日志
 */
struct QueryLog {
    QString sql;                    // SQL语句
    QVariantList parameters;        // 参数列表
    qint64 executionTime;          // 执行时间（毫秒）
    QDateTime timestamp;           // 执行时间戳
    bool success;                  // 是否成功
    QString error;                 // 错误信息
};

/**
 * @brief 数据库统计信息
 */
struct DatabaseStats {
    qint64 totalQueries = 0;       // 总查询次数
    qint64 successfulQueries = 0;  // 成功查询次数
    qint64 failedQueries = 0;      // 失败查询次数
    double avgExecutionTime = 0.0; // 平均执行时间
    qint64 totalExecutionTime = 0; // 总执行时间
    qint64 slowQueries = 0;        // 慢查询次数
    QDateTime lastQuery;           // 最后查询时间
    QString databasePath;          // 数据库路径
    qint64 databaseSize = 0;       // 数据库大小
    int connectionCount = 0;       // 连接数
};

/**
 * @brief 数据库迁移信息
 */
struct Migration {
    QString version;               // 版本号
    QString description;           // 描述
    QString upSql;                // 升级SQL
    QString downSql;              // 降级SQL
    QDateTime createdAt;          // 创建时间
};

/**
 * @brief 数据库管理器
 * 
 * 提供数据库连接管理、查询执行、事务处理、迁移管理等功能
 */
class DatabaseManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit DatabaseManager(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~DatabaseManager();

    /**
     * @brief 初始化数据库
     * @param databasePath 数据库文件路径
     * @return 是否成功
     */
    bool initialize(const QString &databasePath);
    
    /**
     * @brief 关闭数据库
     */
    void close();
    
    /**
     * @brief 检查数据库是否已打开
     * @return 是否已打开
     */
    bool isOpen() const;
    
    /**
     * @brief 获取数据库路径
     * @return 数据库路径
     */
    QString getDatabasePath() const;

    // 查询执行
    
    /**
     * @brief 执行查询
     * @param sql SQL语句
     * @param parameters 参数列表
     * @return 查询结果
     */
    QSqlQuery executeQuery(const QString &sql, const QVariantList &parameters = {});
    
    /**
     * @brief 执行非查询语句（INSERT、UPDATE、DELETE）
     * @param sql SQL语句
     * @param parameters 参数列表
     * @return 是否成功
     */
    bool executeNonQuery(const QString &sql, const QVariantList &parameters = {});
    
    /**
     * @brief 批量执行
     * @param sql SQL语句
     * @param parametersList 参数列表的列表
     * @return 是否成功
     */
    bool executeBatch(const QString &sql, const QList<QVariantList> &parametersList);

    // 事务管理
    
    /**
     * @brief 开始事务
     * @return 是否成功
     */
    bool beginTransaction();
    
    /**
     * @brief 提交事务
     * @return 是否成功
     */
    bool commitTransaction();
    
    /**
     * @brief 回滚事务
     * @return 是否成功
     */
    bool rollbackTransaction();
    
    /**
     * @brief 检查是否在事务中
     * @return 是否在事务中
     */
    bool isInTransaction() const;

    // 数据库维护
    
    /**
     * @brief 执行VACUUM操作
     * @return 是否成功
     */
    bool vacuum();
    
    /**
     * @brief 执行ANALYZE操作
     * @return 是否成功
     */
    bool analyze();
    
    /**
     * @brief 检查数据库完整性
     * @return 是否通过检查
     */
    bool checkIntegrity();
    
    /**
     * @brief 获取完整性检查错误
     * @return 错误列表
     */
    QStringList getIntegrityErrors();

    // 备份和恢复
    
    /**
     * @brief 备份数据库
     * @param backupPath 备份文件路径
     * @return 是否成功
     */
    bool backup(const QString &backupPath);
    
    /**
     * @brief 恢复数据库
     * @param backupPath 备份文件路径
     * @return 是否成功
     */
    bool restore(const QString &backupPath);

    // 迁移管理
    
    /**
     * @brief 运行数据库迁移
     * @return 是否成功
     */
    bool runMigrations();
    
    /**
     * @brief 获取当前数据库版本
     * @return 版本号
     */
    QString getCurrentVersion() const;
    
    /**
     * @brief 获取待执行的迁移
     * @return 迁移列表
     */
    QStringList getPendingMigrations() const;
    
    /**
     * @brief 添加迁移
     * @param migration 迁移信息
     */
    void addMigration(const Migration &migration);

    // 性能监控
    
    /**
     * @brief 获取数据库统计信息
     * @return 统计信息
     */
    DatabaseStats getStats() const;
    
    /**
     * @brief 启用查询日志
     * @param enabled 是否启用
     */
    void enableQueryLogging(bool enabled);
    
    /**
     * @brief 获取慢查询列表
     * @param thresholdMs 慢查询阈值（毫秒）
     * @return 慢查询列表
     */
    QList<QueryLog> getSlowQueries(int thresholdMs = 1000) const;
    
    /**
     * @brief 清空查询日志
     */
    void clearQueryLog();

    // 工具方法
    
    /**
     * @brief 获取表是否存在
     * @param tableName 表名
     * @return 是否存在
     */
    bool tableExists(const QString &tableName) const;
    
    /**
     * @brief 获取表的列信息
     * @param tableName 表名
     * @return 列信息列表
     */
    QStringList getTableColumns(const QString &tableName) const;
    
    /**
     * @brief 获取数据库大小
     * @return 数据库大小（字节）
     */
    qint64 getDatabaseSize() const;
    
    /**
     * @brief 获取最后插入的行ID
     * @return 行ID
     */
    qint64 getLastInsertId() const;

signals:
    /**
     * @brief 数据库连接成功信号
     */
    void connected();
    
    /**
     * @brief 数据库断开连接信号
     */
    void disconnected();
    
    /**
     * @brief 数据库错误信号
     * @param error 错误信息
     */
    void error(const QString &error);
    
    /**
     * @brief 迁移完成信号
     * @param version 版本号
     */
    void migrationCompleted(const QString &version);
    
    /**
     * @brief 慢查询检测信号
     * @param query 查询日志
     */
    void slowQueryDetected(const QueryLog &query);

private slots:
    /**
     * @brief 执行定期维护
     */
    void performMaintenance();

private:
    /**
     * @brief 创建数据库表
     * @return 是否成功
     */
    bool createTables();
    
    /**
     * @brief 初始化数据库设置
     * @return 是否成功
     */
    bool initializeSettings();
    
    /**
     * @brief 加载迁移文件
     */
    void loadMigrations();
    
    /**
     * @brief 执行单个迁移
     * @param migration 迁移信息
     * @return 是否成功
     */
    bool executeMigration(const Migration &migration);
    
    /**
     * @brief 记录查询日志
     * @param sql SQL语句
     * @param parameters 参数列表
     * @param executionTime 执行时间
     * @param success 是否成功
     * @param error 错误信息
     */
    void logQuery(const QString &sql, const QVariantList &parameters, 
                  qint64 executionTime, bool success, const QString &error = QString());
    
    /**
     * @brief 更新统计信息
     * @param executionTime 执行时间
     * @param success 是否成功
     */
    void updateStats(qint64 executionTime, bool success);

private:
    QSqlDatabase m_database;               // 数据库连接
    QString m_databasePath;                // 数据库路径
    mutable QReadWriteLock m_lock;         // 读写锁
    bool m_inTransaction = false;          // 是否在事务中
    
    // 迁移管理
    QList<Migration> m_migrations;         // 迁移列表
    QString m_currentVersion;              // 当前版本
    
    // 性能监控
    mutable DatabaseStats m_stats;         // 统计信息
    bool m_queryLoggingEnabled = false;    // 查询日志开关
    QList<QueryLog> m_queryLog;            // 查询日志
    mutable QMutex m_logMutex;             // 日志锁
    
    // 维护定时器
    QTimer *m_maintenanceTimer;            // 维护定时器
    
    // 常量
    static const QString CONNECTION_NAME;   // 连接名称
    static const int SLOW_QUERY_THRESHOLD; // 慢查询阈值（毫秒）
    static const int MAX_QUERY_LOG_SIZE;   // 最大查询日志数量
    static const int MAINTENANCE_INTERVAL; // 维护间隔（毫秒）
};
