#include <QtTest>
#include <QObject>
#include <memory>

#include "application/ServiceContainer.h"

// 测试用的接口和实现类
class ITestService {
public:
    virtual ~ITestService() = default;
    virtual QString getName() const = 0;
    virtual int getValue() const = 0;
};

class TestServiceImpl : public ITestService {
public:
    TestServiceImpl(const QString &name = "TestService", int value = 42)
        : m_name(name), m_value(value) {}
    
    QString getName() const override { return m_name; }
    int getValue() const override { return m_value; }
    
private:
    QString m_name;
    int m_value;
};

class AnotherTestService : public ITestService {
public:
    AnotherTestService() : m_name("AnotherService"), m_value(100) {}
    
    QString getName() const override { return m_name; }
    int getValue() const override { return m_value; }
    
private:
    QString m_name;
    int m_value;
};

/**
 * @brief ServiceContainer单元测试类
 */
class TestServiceContainer : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();
    
    // 基本功能测试
    void testRegisterAndGetService();
    void testRegisterServiceInstance();
    void testRegisterFactory();
    void testHasService();
    void testRemoveService();
    
    // 高级功能测试
    void testServiceInitialization();
    void testServiceCount();
    void testGetRegisteredServiceNames();
    void testClear();
    
    // 边界条件测试
    void testGetNonExistentService();
    void testRegisterSameServiceTwice();
    void testThreadSafety();
    
    // 性能测试
    void testPerformance();

private:
    std::unique_ptr<ServiceContainer> m_container;
};

void TestServiceContainer::initTestCase()
{
    // 测试套件初始化
    qDebug() << "Starting ServiceContainer tests";
}

void TestServiceContainer::cleanupTestCase()
{
    // 测试套件清理
    qDebug() << "ServiceContainer tests completed";
}

void TestServiceContainer::init()
{
    // 每个测试方法前的初始化
    m_container = std::make_unique<ServiceContainer>();
}

void TestServiceContainer::cleanup()
{
    // 每个测试方法后的清理
    m_container.reset();
}

void TestServiceContainer::testRegisterAndGetService()
{
    // 测试注册和获取服务
    m_container->registerService<ITestService, TestServiceImpl>();
    
    QVERIFY(m_container->hasService<ITestService>());
    
    auto service = m_container->getService<ITestService>();
    QVERIFY(service != nullptr);
    QCOMPARE(service->getName(), QString("TestService"));
    QCOMPARE(service->getValue(), 42);
    
    // 测试多次获取同一服务（工厂模式应该创建新实例）
    auto service2 = m_container->getService<ITestService>();
    QVERIFY(service2 != nullptr);
    QVERIFY(service.get() != service2.get()); // 不同的实例
}

void TestServiceContainer::testRegisterServiceInstance()
{
    // 测试注册服务实例（单例模式）
    auto instance = std::make_shared<TestServiceImpl>("SingletonService", 123);
    m_container->registerService<ITestService>(instance);
    
    QVERIFY(m_container->hasService<ITestService>());
    
    auto service1 = m_container->getService<ITestService>();
    auto service2 = m_container->getService<ITestService>();
    
    QVERIFY(service1 != nullptr);
    QVERIFY(service2 != nullptr);
    QVERIFY(service1.get() == service2.get()); // 同一个实例
    QCOMPARE(service1->getName(), QString("SingletonService"));
    QCOMPARE(service1->getValue(), 123);
}

void TestServiceContainer::testRegisterFactory()
{
    // 测试注册工厂函数
    int counter = 0;
    auto factory = [&counter]() -> std::shared_ptr<ITestService> {
        return std::make_shared<TestServiceImpl>(QString("Factory_%1").arg(++counter), counter * 10);
    };
    
    m_container->registerFactory<ITestService>(factory);
    
    QVERIFY(m_container->hasService<ITestService>());
    
    auto service1 = m_container->getService<ITestService>();
    auto service2 = m_container->getService<ITestService>();
    
    QVERIFY(service1 != nullptr);
    QVERIFY(service2 != nullptr);
    QVERIFY(service1.get() != service2.get()); // 不同的实例
    
    QCOMPARE(service1->getName(), QString("Factory_1"));
    QCOMPARE(service1->getValue(), 10);
    QCOMPARE(service2->getName(), QString("Factory_2"));
    QCOMPARE(service2->getValue(), 20);
}

void TestServiceContainer::testHasService()
{
    // 测试服务存在检查
    QVERIFY(!m_container->hasService<ITestService>());
    
    m_container->registerService<ITestService, TestServiceImpl>();
    QVERIFY(m_container->hasService<ITestService>());
    
    m_container->removeService<ITestService>();
    QVERIFY(!m_container->hasService<ITestService>());
}

void TestServiceContainer::testRemoveService()
{
    // 测试移除服务
    m_container->registerService<ITestService, TestServiceImpl>();
    QVERIFY(m_container->hasService<ITestService>());
    
    m_container->removeService<ITestService>();
    QVERIFY(!m_container->hasService<ITestService>());
    
    auto service = m_container->getService<ITestService>();
    QVERIFY(service == nullptr);
}

void TestServiceContainer::testServiceInitialization()
{
    // 测试服务初始化
    m_container->registerService<ITestService, TestServiceImpl>();
    
    QSignalSpy spy(m_container.get(), &ServiceContainer::servicesInitialized);
    m_container->initializeServices();
    
    QCOMPARE(spy.count(), 1);
}

void TestServiceContainer::testServiceCount()
{
    // 测试服务数量
    QCOMPARE(m_container->getServiceCount(), 0u);
    
    m_container->registerService<ITestService, TestServiceImpl>();
    QCOMPARE(m_container->getServiceCount(), 1u);
    
    m_container->removeService<ITestService>();
    QCOMPARE(m_container->getServiceCount(), 0u);
}

void TestServiceContainer::testGetRegisteredServiceNames()
{
    // 测试获取已注册服务名称
    auto names = m_container->getRegisteredServiceNames();
    QVERIFY(names.isEmpty());
    
    m_container->registerService<ITestService, TestServiceImpl>();
    names = m_container->getRegisteredServiceNames();
    QCOMPARE(names.size(), 1);
    QVERIFY(!names.first().isEmpty());
}

void TestServiceContainer::testClear()
{
    // 测试清空所有服务
    m_container->registerService<ITestService, TestServiceImpl>();
    QCOMPARE(m_container->getServiceCount(), 1u);
    
    m_container->clear();
    QCOMPARE(m_container->getServiceCount(), 0u);
    QVERIFY(!m_container->hasService<ITestService>());
}

void TestServiceContainer::testGetNonExistentService()
{
    // 测试获取不存在的服务
    auto service = m_container->getService<ITestService>();
    QVERIFY(service == nullptr);
}

void TestServiceContainer::testRegisterSameServiceTwice()
{
    // 测试重复注册同一服务
    m_container->registerService<ITestService, TestServiceImpl>();
    m_container->registerService<ITestService, AnotherTestService>();
    
    // 后注册的应该覆盖前面的
    auto service = m_container->getService<ITestService>();
    QVERIFY(service != nullptr);
    QCOMPARE(service->getName(), QString("AnotherService"));
    QCOMPARE(service->getValue(), 100);
}

void TestServiceContainer::testThreadSafety()
{
    // 测试线程安全性
    m_container->registerService<ITestService, TestServiceImpl>();
    
    const int threadCount = 10;
    const int operationsPerThread = 100;
    
    QList<QFuture<void>> futures;
    
    for (int i = 0; i < threadCount; ++i) {
        auto future = QtConcurrent::run([this, operationsPerThread]() {
            for (int j = 0; j < operationsPerThread; ++j) {
                auto service = m_container->getService<ITestService>();
                QVERIFY(service != nullptr);
                QCOMPARE(service->getName(), QString("TestService"));
            }
        });
        futures.append(future);
    }
    
    // 等待所有线程完成
    for (auto &future : futures) {
        future.waitForFinished();
    }
}

void TestServiceContainer::testPerformance()
{
    // 性能测试
    m_container->registerService<ITestService, TestServiceImpl>();
    
    const int iterations = 10000;
    
    QElapsedTimer timer;
    timer.start();
    
    for (int i = 0; i < iterations; ++i) {
        auto service = m_container->getService<ITestService>();
        Q_UNUSED(service)
    }
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "Performance test:" << iterations << "service retrievals in" << elapsed << "ms";
    
    // 平均每次获取服务应该在合理时间内（这里设为1ms）
    QVERIFY(elapsed < iterations); // 平均每次小于1ms
}

QTEST_MAIN(TestServiceContainer)
#include "test_service_container.moc"
