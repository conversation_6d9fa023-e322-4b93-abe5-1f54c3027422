#include "MainWindow.h"
#include "business/managers/LaunchManager.h"
#include "business/managers/ConfigManager.h"
#include "presentation/viewmodels/LaunchItemViewModel.h"
#include <QApplication>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLineEdit>
#include <QListView>
#include <QPushButton>
#include <QLabel>
#include <QSplitter>
#include <QMenuBar>
#include <QStatusBar>
#include <QShortcut>
#include <QKeyEvent>
#include <QCloseEvent>
#include <QTimer>
#include <QLoggingCategory>

Q_LOGGING_CATEGORY(lcMainWindow, "mainwindow")

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_searchDelayTimer(new QTimer(this))
    , m_autoHideTimer(new QTimer(this))
{
    // 设置窗口属性
    setWindowTitle("KK QuickLaunch");
    setMinimumSize(MIN_WINDOW_WIDTH, MIN_WINDOW_HEIGHT);
    resize(1000, 700);
    
    // 设置定时器
    m_searchDelayTimer->setSingleShot(true);
    m_searchDelayTimer->setInterval(SEARCH_DELAY);
    connect(m_searchDelayTimer, &QTimer::timeout, this, &MainWindow::onSearchDelayTimeout);
    
    m_autoHideTimer->setSingleShot(true);
    m_autoHideTimer->setInterval(m_autoHideDelay);
    connect(m_autoHideTimer, &QTimer::timeout, this, &MainWindow::onAutoHideTimeout);
    
    // 初始化UI
    setupUI();
    setupConnections();
    setupShortcuts();
    setupStyles();
    
    // 加载设置
    loadSettings();
    
    qCDebug(lcMainWindow) << "MainWindow created";
}

MainWindow::~MainWindow()
{
    saveSettings();
    qCDebug(lcMainWindow) << "MainWindow destroyed";
}

void MainWindow::focusSearchBox()
{
    if (m_searchEdit) {
        m_searchEdit->setFocus();
        m_searchEdit->selectAll();
    }
}

void MainWindow::showAndActivate()
{
    show();
    raise();
    activateWindow();
    focusSearchBox();
    
    if (m_autoHideEnabled) {
        m_autoHideTimer->start();
    }
    
    emit visibilityChanged(true);
}

void MainWindow::toggleVisibility()
{
    if (isVisible() && !isMinimized()) {
        hide();
    } else {
        showAndActivate();
    }
}

void MainWindow::showSettings()
{
    // TODO: 实现设置对话框
    qCDebug(lcMainWindow) << "Show settings requested";
}

void MainWindow::showStatistics()
{
    // TODO: 实现统计窗口
    qCDebug(lcMainWindow) << "Show statistics requested";
}

void MainWindow::showAbout()
{
    // TODO: 实现关于对话框
    qCDebug(lcMainWindow) << "Show about requested";
}

void MainWindow::quitApplication()
{
    QApplication::quit();
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    // 根据配置决定是否最小化到托盘
    if (m_configManager && m_configManager->getBool("ui.close_to_tray", true)) {
        hide();
        event->ignore();
        emit visibilityChanged(false);
    } else {
        event->accept();
        emit windowClosed();
    }
}

void MainWindow::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
    case Qt::Key_Escape:
        if (m_autoHideEnabled) {
            hide();
        } else {
            resetSearch();
        }
        break;
        
    case Qt::Key_Return:
    case Qt::Key_Enter:
        launchSelectedItem();
        break;
        
    default:
        QMainWindow::keyPressEvent(event);
        break;
    }
}

void MainWindow::changeEvent(QEvent *event)
{
    if (event->type() == QEvent::WindowStateChange) {
        if (isMinimized() && m_configManager && m_configManager->getBool("ui.minimize_to_tray", true)) {
            hide();
            emit visibilityChanged(false);
        }
    }
    
    QMainWindow::changeEvent(event);
}

void MainWindow::showEvent(QShowEvent *event)
{
    QMainWindow::showEvent(event);
    
    // 设置窗口位置
    setWindowPosition();
    
    // 开始淡入动画
    startFadeInAnimation();
    
    emit visibilityChanged(true);
}

void MainWindow::hideEvent(QHideEvent *event)
{
    QMainWindow::hideEvent(event);
    
    // 停止自动隐藏定时器
    m_autoHideTimer->stop();
    
    emit visibilityChanged(false);
}

void MainWindow::onSearchTextChanged(const QString &text)
{
    qCDebug(lcMainWindow) << "Search text changed:" << text;
    
    // 重启搜索延迟定时器
    m_searchDelayTimer->stop();
    m_searchDelayTimer->start();
    
    // 更新搜索状态
    updateSearchStatus(true);
    
    // 重启自动隐藏定时器
    if (m_autoHideEnabled && isVisible()) {
        m_autoHideTimer->start();
    }
}

void MainWindow::onSearchCompleted()
{
    updateSearchStatus(false);
    qCDebug(lcMainWindow) << "Search completed";
}

void MainWindow::onItemActivated(const QModelIndex &index)
{
    if (!index.isValid() || !m_itemViewModel) {
        return;
    }
    
    // 启动选中的项目
    bool success = m_itemViewModel->launchItem(index.row());
    
    if (success) {
        // 如果启动成功，隐藏窗口
        if (m_configManager && m_configManager->getBool("behavior.hide_after_launch", true)) {
            hide();
        }
    }
    
    qCDebug(lcMainWindow) << "Item activated, launch success:" << success;
}

void MainWindow::onItemSelectionChanged(const QModelIndex &current, const QModelIndex &previous)
{
    Q_UNUSED(previous)
    
    updateSelectionStatus(current.isValid());
    
    // 更新详情显示
    if (current.isValid() && m_itemViewModel) {
        LaunchItem item = m_itemViewModel->getItem(current.row());
        updateItemDetails(item);
    } else {
        clearItemDetails();
    }
}

void MainWindow::onAddItem()
{
    // TODO: 实现添加项目对话框
    qCDebug(lcMainWindow) << "Add item requested";
}

void MainWindow::onEditItem()
{
    int itemId = getSelectedItemId();
    if (itemId > 0) {
        // TODO: 实现编辑项目对话框
        qCDebug(lcMainWindow) << "Edit item requested:" << itemId;
    }
}

void MainWindow::onDeleteItem()
{
    int itemId = getSelectedItemId();
    if (itemId > 0 && m_itemViewModel) {
        // TODO: 添加确认对话框
        QModelIndex index = m_itemListView->currentIndex();
        if (index.isValid()) {
            m_itemViewModel->removeItem(index.row());
            qCDebug(lcMainWindow) << "Item deleted:" << itemId;
        }
    }
}

void MainWindow::onRefreshItems()
{
    if (m_itemViewModel) {
        m_itemViewModel->refresh();
        qCDebug(lcMainWindow) << "Items refreshed";
    }
}

void MainWindow::onSearchDelayTimeout()
{
    if (m_itemViewModel && m_searchEdit) {
        QString searchText = m_searchEdit->text();
        m_itemViewModel->setSearchText(searchText);
        qCDebug(lcMainWindow) << "Search executed:" << searchText;
    }
}

void MainWindow::onAutoHideTimeout()
{
    if (m_autoHideEnabled && isVisible()) {
        hide();
        qCDebug(lcMainWindow) << "Auto hide triggered";
    }
}

void MainWindow::onConfigChanged(const QString &key, const QVariant &value)
{
    qCDebug(lcMainWindow) << "Config changed:" << key << "=" << value;
    
    if (key == "ui.theme") {
        applyTheme(value.toString());
    } else if (key == "ui.window_opacity") {
        setWindowOpacity(value.toDouble());
    } else if (key == "behavior.auto_hide_delay") {
        m_autoHideDelay = value.toInt();
        m_autoHideTimer->setInterval(m_autoHideDelay);
    }
}

void MainWindow::onThemeChanged()
{
    if (m_configManager) {
        QString theme = m_configManager->getString("ui.theme", "default");
        applyTheme(theme);
    }
}

void MainWindow::setupUI()
{
    // 创建中央窗口部件
    createCentralWidget();
    
    // 创建菜单栏
    createMenuBar();
    
    // 创建工具栏
    createToolBar();
    
    // 创建状态栏
    createStatusBar();
    
    qCDebug(lcMainWindow) << "UI setup completed";
}

void MainWindow::createCentralWidget()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    // 创建主分割器
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);

    // 创建搜索区域
    createSearchWidget();

    // 创建列表区域
    createListWidget();

    // 创建详情区域
    createDetailWidget();

    // 设置分割器
    m_mainSplitter->addWidget(m_itemListView);
    m_mainSplitter->addWidget(m_detailWidget);
    m_mainSplitter->setSizes({600, 400});

    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(m_centralWidget);
    mainLayout->addWidget(m_searchWidget);
    mainLayout->addWidget(m_mainSplitter);
    mainLayout->setContentsMargins(10, 10, 10, 10);
    mainLayout->setSpacing(10);
}

void MainWindow::createSearchWidget()
{
    m_searchWidget = new QWidget(this);

    // 创建搜索框
    m_searchEdit = new QLineEdit(this);
    m_searchEdit->setPlaceholderText("搜索应用程序...");
    m_searchEdit->setClearButtonEnabled(true);

    // 创建搜索按钮
    m_searchButton = new QPushButton("搜索", this);
    m_searchButton->setEnabled(false); // 暂时禁用

    // 创建状态标签
    m_searchStatusLabel = new QLabel(this);
    m_searchStatusLabel->setVisible(false);

    // 创建布局
    QHBoxLayout *searchLayout = new QHBoxLayout(m_searchWidget);
    searchLayout->addWidget(m_searchEdit);
    searchLayout->addWidget(m_searchButton);
    searchLayout->addWidget(m_searchStatusLabel);
    searchLayout->setContentsMargins(0, 0, 0, 0);
}

void MainWindow::createListWidget()
{
    m_itemListView = new QListView(this);
    m_itemListView->setAlternatingRowColors(true);
    m_itemListView->setSelectionMode(QAbstractItemView::SingleSelection);
    m_itemListView->setEditTriggers(QAbstractItemView::NoEditTriggers);

    // 创建视图模型
    m_itemViewModel = std::make_unique<LaunchItemViewModel>(this);
    m_itemListView->setModel(m_itemViewModel.get());
}

void MainWindow::createDetailWidget()
{
    m_detailWidget = new QWidget(this);

    // 创建详情标签
    m_itemNameLabel = new QLabel("名称: ", this);
    m_itemPathLabel = new QLabel("路径: ", this);
    m_itemDescriptionLabel = new QLabel("描述: ", this);
    m_itemStatsLabel = new QLabel("统计: ", this);

    // 设置标签属性
    m_itemNameLabel->setWordWrap(true);
    m_itemPathLabel->setWordWrap(true);
    m_itemDescriptionLabel->setWordWrap(true);
    m_itemStatsLabel->setWordWrap(true);

    // 创建布局
    QVBoxLayout *detailLayout = new QVBoxLayout(m_detailWidget);
    detailLayout->addWidget(m_itemNameLabel);
    detailLayout->addWidget(m_itemPathLabel);
    detailLayout->addWidget(m_itemDescriptionLabel);
    detailLayout->addWidget(m_itemStatsLabel);
    detailLayout->addStretch();
    detailLayout->setContentsMargins(10, 10, 10, 10);
}

void MainWindow::createMenuBar()
{
    // 文件菜单
    QMenu *fileMenu = menuBar()->addMenu("文件(&F)");
    fileMenu->addAction("添加项目(&A)", this, &MainWindow::onAddItem, QKeySequence::New);
    fileMenu->addAction("导入(&I)", this, &MainWindow::onImportItems);
    fileMenu->addAction("导出(&E)", this, &MainWindow::onExportItems);
    fileMenu->addSeparator();
    fileMenu->addAction("退出(&X)", this, &MainWindow::quitApplication, QKeySequence::Quit);

    // 编辑菜单
    QMenu *editMenu = menuBar()->addMenu("编辑(&E)");
    editMenu->addAction("编辑项目(&E)", this, &MainWindow::onEditItem);
    editMenu->addAction("删除项目(&D)", this, &MainWindow::onDeleteItem, QKeySequence::Delete);
    editMenu->addSeparator();
    editMenu->addAction("刷新(&R)", this, &MainWindow::onRefreshItems, QKeySequence::Refresh);

    // 视图菜单
    QMenu *viewMenu = menuBar()->addMenu("视图(&V)");
    viewMenu->addAction("设置(&S)", this, &MainWindow::showSettings);
    viewMenu->addAction("统计(&T)", this, &MainWindow::showStatistics);

    // 帮助菜单
    QMenu *helpMenu = menuBar()->addMenu("帮助(&H)");
    helpMenu->addAction("关于(&A)", this, &MainWindow::showAbout);
}

void MainWindow::createToolBar()
{
    QToolBar *toolBar = addToolBar("主工具栏");

    m_addButton = new QPushButton("添加", this);
    m_editButton = new QPushButton("编辑", this);
    m_deleteButton = new QPushButton("删除", this);
    m_refreshButton = new QPushButton("刷新", this);
    m_settingsButton = new QPushButton("设置", this);

    toolBar->addWidget(m_addButton);
    toolBar->addWidget(m_editButton);
    toolBar->addWidget(m_deleteButton);
    toolBar->addSeparator();
    toolBar->addWidget(m_refreshButton);
    toolBar->addSeparator();
    toolBar->addWidget(m_settingsButton);

    // 初始状态
    m_editButton->setEnabled(false);
    m_deleteButton->setEnabled(false);
}

void MainWindow::createStatusBar()
{
    m_statusLabel = new QLabel("就绪", this);
    m_itemCountLabel = new QLabel("0 个项目", this);

    statusBar()->addWidget(m_statusLabel);
    statusBar()->addPermanentWidget(m_itemCountLabel);
}

void MainWindow::setupConnections()
{
    // 搜索相关
    connect(m_searchEdit, &QLineEdit::textChanged, this, &MainWindow::onSearchTextChanged);

    // 列表相关
    connect(m_itemListView, &QListView::activated, this, &MainWindow::onItemActivated);
    connect(m_itemListView->selectionModel(), &QItemSelectionModel::currentChanged,
            this, &MainWindow::onItemSelectionChanged);

    // 工具栏按钮
    connect(m_addButton, &QPushButton::clicked, this, &MainWindow::onAddItem);
    connect(m_editButton, &QPushButton::clicked, this, &MainWindow::onEditItem);
    connect(m_deleteButton, &QPushButton::clicked, this, &MainWindow::onDeleteItem);
    connect(m_refreshButton, &QPushButton::clicked, this, &MainWindow::onRefreshItems);
    connect(m_settingsButton, &QPushButton::clicked, this, &MainWindow::showSettings);

    // 视图模型信号
    if (m_itemViewModel) {
        connect(m_itemViewModel.get(), &LaunchItemViewModel::searchCompleted,
                this, &MainWindow::onSearchCompleted);
        connect(m_itemViewModel.get(), &LaunchItemViewModel::countChanged,
                this, [this](int count) {
                    m_itemCountLabel->setText(QString("%1 个项目").arg(count));
                });
    }
}

void MainWindow::setupShortcuts()
{
    m_escapeShortcut = new QShortcut(QKeySequence(Qt::Key_Escape), this);
    connect(m_escapeShortcut, &QShortcut::activated, this, [this]() {
        if (m_autoHideEnabled) {
            hide();
        } else {
            resetSearch();
        }
    });

    m_enterShortcut = new QShortcut(QKeySequence(Qt::Key_Return), this);
    connect(m_enterShortcut, &QShortcut::activated, this, &MainWindow::launchSelectedItem);

    m_ctrlFShortcut = new QShortcut(QKeySequence::Find, this);
    connect(m_ctrlFShortcut, &QShortcut::activated, this, &MainWindow::focusSearchBox);

    m_deleteShortcut = new QShortcut(QKeySequence::Delete, this);
    connect(m_deleteShortcut, &QShortcut::activated, this, &MainWindow::onDeleteItem);

    m_f5Shortcut = new QShortcut(QKeySequence::Refresh, this);
    connect(m_f5Shortcut, &QShortcut::activated, this, &MainWindow::onRefreshItems);
}

void MainWindow::setupStyles()
{
    // 应用默认样式
    applyTheme(m_currentTheme);
}

void MainWindow::loadSettings()
{
    // TODO: 从配置管理器加载设置
    if (m_configManager) {
        m_autoHideEnabled = m_configManager->getBool("behavior.auto_hide_enabled", false);
        m_autoHideDelay = m_configManager->getInt("behavior.auto_hide_delay", 5000);
        m_currentTheme = m_configManager->getString("ui.theme", "default");

        // 应用设置
        m_autoHideTimer->setInterval(m_autoHideDelay);
        applyTheme(m_currentTheme);

        // 连接配置变更信号
        connect(m_configManager.get(), &ConfigManager::valueChanged,
                this, &MainWindow::onConfigChanged);
    }
}

void MainWindow::saveSettings()
{
    // TODO: 保存设置到配置管理器
    if (m_configManager) {
        m_configManager->setValue("ui.window_geometry", saveGeometry());
        m_configManager->setValue("ui.window_state", saveState());
        m_configManager->sync();
    }
}

void MainWindow::resetSearch()
{
    if (m_searchEdit) {
        m_searchEdit->clear();
        m_searchEdit->setFocus();
    }

    if (m_itemViewModel) {
        m_itemViewModel->resetFilters();
    }
}

void MainWindow::launchSelectedItem()
{
    QModelIndex current = m_itemListView->currentIndex();
    if (current.isValid()) {
        onItemActivated(current);
    }
}

void MainWindow::updateSearchStatus(bool searching)
{
    if (m_searchStatusLabel) {
        if (searching) {
            m_searchStatusLabel->setText("搜索中...");
            m_searchStatusLabel->setVisible(true);
        } else {
            m_searchStatusLabel->setVisible(false);
        }
    }
}

void MainWindow::updateSelectionStatus(bool hasSelection)
{
    if (m_editButton) {
        m_editButton->setEnabled(hasSelection);
    }
    if (m_deleteButton) {
        m_deleteButton->setEnabled(hasSelection);
    }
}

void MainWindow::updateItemDetails(const LaunchItem &item)
{
    if (m_itemNameLabel) {
        m_itemNameLabel->setText(QString("名称: %1").arg(item.name));
    }
    if (m_itemPathLabel) {
        m_itemPathLabel->setText(QString("路径: %1").arg(item.path));
    }
    if (m_itemDescriptionLabel) {
        m_itemDescriptionLabel->setText(QString("描述: %1").arg(item.description));
    }
    if (m_itemStatsLabel) {
        QString stats = QString("使用次数: %1 | 最后使用: %2")
                       .arg(item.useCount)
                       .arg(item.lastUsed.toString("yyyy-MM-dd hh:mm"));
        m_itemStatsLabel->setText(stats);
    }
}

void MainWindow::clearItemDetails()
{
    if (m_itemNameLabel) {
        m_itemNameLabel->setText("名称: ");
    }
    if (m_itemPathLabel) {
        m_itemPathLabel->setText("路径: ");
    }
    if (m_itemDescriptionLabel) {
        m_itemDescriptionLabel->setText("描述: ");
    }
    if (m_itemStatsLabel) {
        m_itemStatsLabel->setText("统计: ");
    }
}

int MainWindow::getSelectedItemId() const
{
    QModelIndex current = m_itemListView->currentIndex();
    if (current.isValid() && m_itemViewModel) {
        LaunchItem item = m_itemViewModel->getItem(current.row());
        return item.id;
    }
    return -1;
}

void MainWindow::setWindowPosition()
{
    // 将窗口居中显示
    QRect screenGeometry = QApplication::primaryScreen()->geometry();
    int x = (screenGeometry.width() - width()) / 2;
    int y = (screenGeometry.height() - height()) / 2;
    move(x, y);
}

void MainWindow::startFadeInAnimation()
{
    // TODO: 实现淡入动画
    setWindowOpacity(1.0);
}

void MainWindow::applyTheme(const QString &themeName)
{
    m_currentTheme = themeName;

    if (themeName == "dark") {
        // 应用暗色主题
        setStyleSheet(R"(
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLineEdit {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                padding: 5px;
                color: #ffffff;
            }
            QListView {
                background-color: #3c3c3c;
                alternate-background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
            }
            QPushButton {
                background-color: #4a4a4a;
                border: 1px solid #666666;
                padding: 5px 10px;
                color: #ffffff;
            }
            QPushButton:hover {
                background-color: #5a5a5a;
            }
            QPushButton:pressed {
                background-color: #3a3a3a;
            }
        )");
    } else {
        // 应用默认主题
        setStyleSheet("");
    }

    qCDebug(lcMainWindow) << "Theme applied:" << themeName;
}

void MainWindow::onImportItems()
{
    // TODO: 实现导入对话框
    qCDebug(lcMainWindow) << "Import items requested";
}

void MainWindow::onExportItems()
{
    // TODO: 实现导出对话框
    qCDebug(lcMainWindow) << "Export items requested";
}
