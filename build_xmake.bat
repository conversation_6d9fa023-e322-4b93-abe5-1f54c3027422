@echo off
setlocal enabledelayedexpansion

:: KK QuickLaunch XMake 构建脚本
:: 支持 Windows 平台的自动化构建

echo.
echo ========================================
echo   KK QuickLaunch XMake Build Script
echo ========================================
echo.

:: 检查xmake是否安装
where xmake >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] XMake not found! Please install xmake first.
    echo Download from: https://xmake.io
    pause
    exit /b 1
)

:: 显示xmake版本
echo [INFO] XMake version:
xmake --version
echo.

:: 解析命令行参数
set BUILD_MODE=release
set BUILD_TARGET=kkquicklaunch
set RUN_TESTS=false
set CLEAN_BUILD=false
set PACKAGE_BUILD=false
set INSTALL_BUILD=false

:parse_args
if "%~1"=="" goto :args_done
if /i "%~1"=="debug" set BUILD_MODE=debug
if /i "%~1"=="release" set BUILD_MODE=release
if /i "%~1"=="test" set RUN_TESTS=true
if /i "%~1"=="tests" set RUN_TESTS=true
if /i "%~1"=="clean" set CLEAN_BUILD=true
if /i "%~1"=="package" set PACKAGE_BUILD=true
if /i "%~1"=="install" set INSTALL_BUILD=true
if /i "%~1"=="help" goto :show_help
if /i "%~1"=="-h" goto :show_help
if /i "%~1"=="--help" goto :show_help
shift
goto :parse_args

:args_done

:: 显示构建配置
echo [INFO] Build Configuration:
echo   Mode: %BUILD_MODE%
echo   Target: %BUILD_TARGET%
echo   Run Tests: %RUN_TESTS%
echo   Clean Build: %CLEAN_BUILD%
echo   Package: %PACKAGE_BUILD%
echo   Install: %INSTALL_BUILD%
echo.

:: 清理构建（如果需要）
if "%CLEAN_BUILD%"=="true" (
    echo [INFO] Cleaning previous build...
    xmake clean-all
    if !errorlevel! neq 0 (
        echo [ERROR] Clean failed!
        pause
        exit /b 1
    )
    echo [SUCCESS] Clean completed.
    echo.
)

:: 配置构建
echo [INFO] Configuring build...
xmake config -m %BUILD_MODE% -p windows -a x64
if %errorlevel% neq 0 (
    echo [ERROR] Configuration failed!
    pause
    exit /b 1
)
echo [SUCCESS] Configuration completed.
echo.

:: 构建主程序
echo [INFO] Building %BUILD_TARGET%...
xmake build %BUILD_TARGET%
if %errorlevel% neq 0 (
    echo [ERROR] Build failed!
    pause
    exit /b 1
)
echo [SUCCESS] Build completed.
echo.

:: 运行测试（如果需要）
if "%RUN_TESTS%"=="true" (
    echo [INFO] Building and running tests...
    xmake build kkquicklaunch_tests
    if !errorlevel! neq 0 (
        echo [ERROR] Test build failed!
        pause
        exit /b 1
    )
    
    echo [INFO] Running tests...
    xmake run kkquicklaunch_tests
    if !errorlevel! neq 0 (
        echo [WARNING] Some tests failed!
    ) else (
        echo [SUCCESS] All tests passed.
    )
    echo.
)

:: 打包（如果需要）
if "%PACKAGE_BUILD%"=="true" (
    echo [INFO] Creating package...
    xmake package -f zip
    if !errorlevel! neq 0 (
        echo [ERROR] Package creation failed!
        pause
        exit /b 1
    )
    echo [SUCCESS] Package created.
    echo.
)

:: 安装（如果需要）
if "%INSTALL_BUILD%"=="true" (
    echo [INFO] Installing application...
    xmake install
    if !errorlevel! neq 0 (
        echo [ERROR] Installation failed!
        pause
        exit /b 1
    )
    echo [SUCCESS] Installation completed.
    echo.
)

:: 显示构建结果
echo [INFO] Build artifacts:
for /f "delims=" %%i in ('xmake show -t %BUILD_TARGET%') do (
    echo   %%i
)
echo.

:: 显示构建统计
echo [INFO] Build Statistics:
echo   Build Mode: %BUILD_MODE%
echo   Platform: Windows x64
echo   Compiler: MSVC/MinGW
echo   Qt Version: 6.x
echo   Build Time: %date% %time%
echo.

echo [SUCCESS] Build process completed successfully!
echo.

:: 询问是否运行程序
if "%RUN_TESTS%"=="false" (
    set /p run_app="Do you want to run the application? (y/n): "
    if /i "!run_app!"=="y" (
        echo [INFO] Starting KK QuickLaunch...
        xmake run %BUILD_TARGET%
    )
)

goto :end

:show_help
echo.
echo Usage: build_xmake.bat [options]
echo.
echo Options:
echo   debug       Build in debug mode
echo   release     Build in release mode (default)
echo   test        Build and run tests
echo   clean       Clean previous build
echo   package     Create distribution package
echo   install     Install to system
echo   help        Show this help message
echo.
echo Examples:
echo   build_xmake.bat                    # Release build
echo   build_xmake.bat debug              # Debug build
echo   build_xmake.bat release test       # Release build with tests
echo   build_xmake.bat clean release      # Clean and release build
echo   build_xmake.bat release package    # Release build and package
echo.

:end
pause
