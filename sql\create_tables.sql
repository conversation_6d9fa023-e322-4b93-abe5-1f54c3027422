-- ============================================================================
-- 智能启动助手 - 本地SQLite数据库建表语句
-- 版本: 1.0.0
-- 创建时间: 2024-12-17
-- 说明: 兼容API 28+，支持Qt 6.x SQLite驱动
-- ============================================================================

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- ============================================================================
-- 1. 启动项表 (launch_items)
-- 存储用户添加的应用程序启动项信息
-- ============================================================================
CREATE TABLE IF NOT EXISTS launch_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                         -- 应用显示名称
    path TEXT NOT NULL,                         -- 应用程序完整路径
    category_id INTEGER DEFAULT 1,              -- 分类ID，关联categories表
    icon_data BLOB,                             -- 图标二进制数据(PNG/ICO格式)
    icon_path TEXT,                             -- 图标文件路径(备用)
    description TEXT DEFAULT '',                -- 应用描述信息
    tags TEXT DEFAULT '[]',                     -- 标签数组(JSON格式)
    arguments TEXT DEFAULT '',                  -- 启动参数
    working_directory TEXT DEFAULT '',          -- 工作目录
    run_as_admin INTEGER DEFAULT 0,            -- 是否以管理员身份运行(0/1)
    priority INTEGER DEFAULT 0,                -- 优先级(数值越大优先级越高)
    is_enabled INTEGER DEFAULT 1,              -- 是否启用(0/1)
    use_count INTEGER DEFAULT 0,               -- 使用次数统计
    last_used DATETIME,                        -- 最后使用时间
    avg_response_time INTEGER DEFAULT 0,       -- 平均响应时间(毫秒)
    file_size INTEGER DEFAULT 0,               -- 文件大小(字节)
    file_version TEXT DEFAULT '',              -- 文件版本号
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    UNIQUE(path),                              -- 路径唯一
    CHECK(priority >= 0),                      -- 优先级非负
    CHECK(is_enabled IN (0, 1)),              -- 布尔值检查
    CHECK(run_as_admin IN (0, 1)),            -- 布尔值检查
    CHECK(use_count >= 0),                     -- 使用次数非负
    
    -- 外键约束
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET DEFAULT
);

-- 启动项表索引
CREATE INDEX IF NOT EXISTS idx_launch_items_category ON launch_items(category_id);
CREATE INDEX IF NOT EXISTS idx_launch_items_enabled ON launch_items(is_enabled);
CREATE INDEX IF NOT EXISTS idx_launch_items_priority ON launch_items(priority DESC);
CREATE INDEX IF NOT EXISTS idx_launch_items_use_count ON launch_items(use_count DESC);
CREATE INDEX IF NOT EXISTS idx_launch_items_last_used ON launch_items(last_used DESC);
CREATE INDEX IF NOT EXISTS idx_launch_items_name ON launch_items(name COLLATE NOCASE);

-- ============================================================================
-- 2. 分类表 (categories)
-- 存储启动项的分类信息
-- ============================================================================
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,                 -- 分类名称(唯一)
    display_name TEXT NOT NULL,                -- 显示名称(支持多语言)
    icon_name TEXT DEFAULT 'folder',           -- 图标名称
    color TEXT DEFAULT '#007ACC',              -- 分类颜色(十六进制)
    description TEXT DEFAULT '',               -- 分类描述
    sort_order INTEGER DEFAULT 0,              -- 排序顺序
    is_system INTEGER DEFAULT 0,               -- 是否系统分类(0/1)
    is_visible INTEGER DEFAULT 1,              -- 是否可见(0/1)
    parent_id INTEGER,                         -- 父分类ID(支持层级分类)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CHECK(is_system IN (0, 1)),
    CHECK(is_visible IN (0, 1)),
    CHECK(sort_order >= 0),
    
    -- 外键约束(自引用)
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- 分类表索引
CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);
CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_visible ON categories(is_visible);

-- ============================================================================
-- 3. 用户行为记录表 (user_actions)
-- 记录用户的操作行为，用于推荐算法优化
-- ============================================================================
CREATE TABLE IF NOT EXISTS user_actions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    launch_item_id INTEGER,                    -- 关联的启动项ID
    action_type TEXT NOT NULL,                 -- 操作类型: launch/search/add/delete/edit
    action_method TEXT DEFAULT '',             -- 操作方式: click/hotkey/search/drag_drop
    search_query TEXT DEFAULT '',              -- 搜索关键词(如果是搜索操作)
    context_data TEXT DEFAULT '{}',            -- 上下文数据(JSON格式)
    response_time INTEGER DEFAULT 0,           -- 响应时间(毫秒)
    success INTEGER DEFAULT 1,                 -- 操作是否成功(0/1)
    error_message TEXT DEFAULT '',             -- 错误信息(如果失败)
    session_id TEXT DEFAULT '',                -- 会话ID
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CHECK(success IN (0, 1)),
    CHECK(response_time >= 0),
    CHECK(action_type IN ('launch', 'search', 'add', 'delete', 'edit', 'view', 'recommend')),
    
    -- 外键约束
    FOREIGN KEY (launch_item_id) REFERENCES launch_items(id) ON DELETE CASCADE
);

-- 用户行为表索引
CREATE INDEX IF NOT EXISTS idx_user_actions_item ON user_actions(launch_item_id);
CREATE INDEX IF NOT EXISTS idx_user_actions_type ON user_actions(action_type);
CREATE INDEX IF NOT EXISTS idx_user_actions_timestamp ON user_actions(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_user_actions_session ON user_actions(session_id);

-- ============================================================================
-- 4. 推荐规则表 (recommendation_rules)
-- 存储自定义推荐规则配置
-- ============================================================================
CREATE TABLE IF NOT EXISTS recommendation_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                        -- 规则名称
    description TEXT DEFAULT '',               -- 规则描述
    rule_type TEXT NOT NULL,                   -- 规则类型: file_type/time/context/keyword
    condition_data TEXT NOT NULL,              -- 条件数据(JSON格式)
    action_data TEXT NOT NULL,                 -- 动作数据(JSON格式)
    priority INTEGER DEFAULT 0,                -- 规则优先级
    is_enabled INTEGER DEFAULT 1,              -- 是否启用(0/1)
    match_count INTEGER DEFAULT 0,             -- 匹配次数统计
    success_count INTEGER DEFAULT 0,           -- 成功推荐次数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CHECK(is_enabled IN (0, 1)),
    CHECK(priority >= 0),
    CHECK(match_count >= 0),
    CHECK(success_count >= 0),
    CHECK(rule_type IN ('file_type', 'time', 'context', 'keyword', 'clipboard', 'window'))
);

-- 推荐规则表索引
CREATE INDEX IF NOT EXISTS idx_recommendation_rules_type ON recommendation_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_recommendation_rules_enabled ON recommendation_rules(is_enabled);
CREATE INDEX IF NOT EXISTS idx_recommendation_rules_priority ON recommendation_rules(priority DESC);

-- ============================================================================
-- 5. 应用配置表 (app_settings)
-- 存储应用程序的各种配置信息
-- ============================================================================
CREATE TABLE IF NOT EXISTS app_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT NOT NULL UNIQUE,          -- 配置键名
    setting_value TEXT NOT NULL,               -- 配置值
    value_type TEXT DEFAULT 'string',          -- 值类型: string/int/bool/json
    category TEXT DEFAULT 'general',           -- 配置分类
    description TEXT DEFAULT '',               -- 配置描述
    is_user_configurable INTEGER DEFAULT 1,    -- 用户是否可配置(0/1)
    requires_restart INTEGER DEFAULT 0,        -- 是否需要重启生效(0/1)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CHECK(is_user_configurable IN (0, 1)),
    CHECK(requires_restart IN (0, 1)),
    CHECK(value_type IN ('string', 'int', 'bool', 'json', 'float'))
);

-- 应用配置表索引
CREATE INDEX IF NOT EXISTS idx_app_settings_category ON app_settings(category);
CREATE INDEX IF NOT EXISTS idx_app_settings_configurable ON app_settings(is_user_configurable);

-- ============================================================================
-- 6. 快捷键配置表 (hotkey_settings)
-- 存储全局快捷键配置
-- ============================================================================
CREATE TABLE IF NOT EXISTS hotkey_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    action_name TEXT NOT NULL UNIQUE,          -- 动作名称
    display_name TEXT NOT NULL,                -- 显示名称
    key_sequence TEXT NOT NULL,                -- 快捷键序列(如: Ctrl+Space)
    description TEXT DEFAULT '',               -- 描述信息
    is_global INTEGER DEFAULT 1,               -- 是否全局快捷键(0/1)
    is_enabled INTEGER DEFAULT 1,              -- 是否启用(0/1)
    conflict_resolution TEXT DEFAULT 'warn',   -- 冲突解决策略: warn/override/disable
    use_count INTEGER DEFAULT 0,               -- 使用次数
    last_used DATETIME,                        -- 最后使用时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CHECK(is_global IN (0, 1)),
    CHECK(is_enabled IN (0, 1)),
    CHECK(use_count >= 0),
    CHECK(conflict_resolution IN ('warn', 'override', 'disable'))
);

-- 快捷键配置表索引
CREATE INDEX IF NOT EXISTS idx_hotkey_settings_enabled ON hotkey_settings(is_enabled);
CREATE INDEX IF NOT EXISTS idx_hotkey_settings_global ON hotkey_settings(is_global);
CREATE INDEX IF NOT EXISTS idx_hotkey_settings_sequence ON hotkey_settings(key_sequence);

-- ============================================================================
-- 7. 文件类型关联表 (file_associations)
-- 存储文件类型与应用程序的关联关系
-- ============================================================================
CREATE TABLE IF NOT EXISTS file_associations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_extension TEXT NOT NULL,              -- 文件扩展名(如: .json, .txt)
    mime_type TEXT DEFAULT '',                 -- MIME类型
    launch_item_id INTEGER NOT NULL,           -- 关联的启动项ID
    association_type TEXT DEFAULT 'user',      -- 关联类型: system/user/auto
    confidence_score REAL DEFAULT 1.0,        -- 置信度分数(0.0-1.0)
    use_count INTEGER DEFAULT 0,               -- 使用次数
    last_used DATETIME,                        -- 最后使用时间
    is_default INTEGER DEFAULT 0,              -- 是否默认关联(0/1)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CHECK(confidence_score >= 0.0 AND confidence_score <= 1.0),
    CHECK(use_count >= 0),
    CHECK(is_default IN (0, 1)),
    CHECK(association_type IN ('system', 'user', 'auto')),
    
    -- 外键约束
    FOREIGN KEY (launch_item_id) REFERENCES launch_items(id) ON DELETE CASCADE,
    
    -- 唯一约束
    UNIQUE(file_extension, launch_item_id)
);

-- 文件关联表索引
CREATE INDEX IF NOT EXISTS idx_file_associations_ext ON file_associations(file_extension);
CREATE INDEX IF NOT EXISTS idx_file_associations_item ON file_associations(launch_item_id);
CREATE INDEX IF NOT EXISTS idx_file_associations_default ON file_associations(is_default);
CREATE INDEX IF NOT EXISTS idx_file_associations_confidence ON file_associations(confidence_score DESC);

-- ============================================================================
-- 8. 搜索历史表 (search_history)
-- 存储用户搜索历史记录
-- ============================================================================
CREATE TABLE IF NOT EXISTS search_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    search_query TEXT NOT NULL,                -- 搜索关键词
    result_count INTEGER DEFAULT 0,            -- 搜索结果数量
    selected_item_id INTEGER,                  -- 用户选择的启动项ID
    search_method TEXT DEFAULT 'typing',       -- 搜索方式: typing/voice/paste
    response_time INTEGER DEFAULT 0,           -- 搜索响应时间(毫秒)
    context_data TEXT DEFAULT '{}',            -- 搜索上下文(JSON格式)
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CHECK(result_count >= 0),
    CHECK(response_time >= 0),
    CHECK(search_method IN ('typing', 'voice', 'paste', 'hotkey')),
    
    -- 外键约束
    FOREIGN KEY (selected_item_id) REFERENCES launch_items(id) ON DELETE SET NULL
);

-- 搜索历史表索引
CREATE INDEX IF NOT EXISTS idx_search_history_query ON search_history(search_query COLLATE NOCASE);
CREATE INDEX IF NOT EXISTS idx_search_history_timestamp ON search_history(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_search_history_selected ON search_history(selected_item_id);

-- ============================================================================
-- 9. 插件信息表 (plugins)
-- 存储已安装插件的信息
-- ============================================================================
CREATE TABLE IF NOT EXISTS plugins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plugin_id TEXT NOT NULL UNIQUE,            -- 插件唯一标识
    name TEXT NOT NULL,                        -- 插件名称
    version TEXT NOT NULL,                     -- 插件版本
    description TEXT DEFAULT '',               -- 插件描述
    author TEXT DEFAULT '',                    -- 插件作者
    file_path TEXT NOT NULL,                   -- 插件文件路径
    config_data TEXT DEFAULT '{}',             -- 插件配置(JSON格式)
    is_enabled INTEGER DEFAULT 1,              -- 是否启用(0/1)
    is_system INTEGER DEFAULT 0,               -- 是否系统插件(0/1)
    load_priority INTEGER DEFAULT 0,           -- 加载优先级
    supported_extensions TEXT DEFAULT '[]',    -- 支持的文件扩展名(JSON数组)
    api_version TEXT DEFAULT '1.0',            -- API版本
    install_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_loaded DATETIME,                      -- 最后加载时间
    error_count INTEGER DEFAULT 0,             -- 错误次数
    last_error TEXT DEFAULT '',                -- 最后错误信息
    
    -- 约束
    CHECK(is_enabled IN (0, 1)),
    CHECK(is_system IN (0, 1)),
    CHECK(load_priority >= 0),
    CHECK(error_count >= 0)
);

-- 插件信息表索引
CREATE INDEX IF NOT EXISTS idx_plugins_enabled ON plugins(is_enabled);
CREATE INDEX IF NOT EXISTS idx_plugins_priority ON plugins(load_priority DESC);
CREATE INDEX IF NOT EXISTS idx_plugins_system ON plugins(is_system);

-- ============================================================================
-- 10. 系统日志表 (system_logs)
-- 存储系统运行日志
-- ============================================================================
CREATE TABLE IF NOT EXISTS system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    log_level TEXT NOT NULL,                   -- 日志级别: DEBUG/INFO/WARN/ERROR/FATAL
    category TEXT DEFAULT 'general',           -- 日志分类
    message TEXT NOT NULL,                     -- 日志消息
    details TEXT DEFAULT '',                   -- 详细信息
    source_file TEXT DEFAULT '',               -- 源文件名
    source_line INTEGER DEFAULT 0,             -- 源代码行号
    thread_id TEXT DEFAULT '',                 -- 线程ID
    session_id TEXT DEFAULT '',                -- 会话ID
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CHECK(log_level IN ('DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL')),
    CHECK(source_line >= 0)
);

-- 系统日志表索引
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(log_level);
CREATE INDEX IF NOT EXISTS idx_system_logs_category ON system_logs(category);
CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_system_logs_session ON system_logs(session_id);

-- ============================================================================
-- 触发器定义
-- ============================================================================

-- 启动项更新时间触发器
CREATE TRIGGER IF NOT EXISTS trigger_launch_items_updated
    AFTER UPDATE ON launch_items
    FOR EACH ROW
BEGIN
    UPDATE launch_items 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- 分类更新时间触发器
CREATE TRIGGER IF NOT EXISTS trigger_categories_updated
    AFTER UPDATE ON categories
    FOR EACH ROW
BEGIN
    UPDATE categories 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- 应用配置更新时间触发器
CREATE TRIGGER IF NOT EXISTS trigger_app_settings_updated
    AFTER UPDATE ON app_settings
    FOR EACH ROW
BEGIN
    UPDATE app_settings 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- 快捷键配置更新时间触发器
CREATE TRIGGER IF NOT EXISTS trigger_hotkey_settings_updated
    AFTER UPDATE ON hotkey_settings
    FOR EACH ROW
BEGIN
    UPDATE hotkey_settings 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- ============================================================================
-- 视图定义
-- ============================================================================

-- 启动项统计视图
CREATE VIEW IF NOT EXISTS view_launch_items_stats AS
SELECT 
    li.id,
    li.name,
    li.path,
    c.display_name as category_name,
    li.use_count,
    li.last_used,
    li.avg_response_time,
    COUNT(ua.id) as total_actions,
    MAX(ua.timestamp) as last_action_time
FROM launch_items li
LEFT JOIN categories c ON li.category_id = c.id
LEFT JOIN user_actions ua ON li.id = ua.launch_item_id
WHERE li.is_enabled = 1
GROUP BY li.id, li.name, li.path, c.display_name, li.use_count, li.last_used, li.avg_response_time;

-- 分类统计视图
CREATE VIEW IF NOT EXISTS view_category_stats AS
SELECT 
    c.id,
    c.name,
    c.display_name,
    COUNT(li.id) as item_count,
    SUM(li.use_count) as total_usage,
    AVG(li.avg_response_time) as avg_response_time
FROM categories c
LEFT JOIN launch_items li ON c.id = li.category_id AND li.is_enabled = 1
WHERE c.is_visible = 1
GROUP BY c.id, c.name, c.display_name
ORDER BY c.sort_order;

-- 热门搜索视图
CREATE VIEW IF NOT EXISTS view_popular_searches AS
SELECT 
    search_query,
    COUNT(*) as search_count,
    AVG(result_count) as avg_results,
    AVG(response_time) as avg_response_time,
    MAX(timestamp) as last_searched
FROM search_history
WHERE timestamp >= datetime('now', '-30 days')
GROUP BY search_query
HAVING search_count > 1
ORDER BY search_count DESC, last_searched DESC;
