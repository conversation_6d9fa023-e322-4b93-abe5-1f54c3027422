# 智能启动助手 - 后端服务设计方案

## 1. 总体设计

### 1.1 服务概述
**服务名称**：KK QuickLaunch Backend Service  
**服务目标**：为智能启动助手提供配置同步、软件推荐、用户数据管理等云端服务  
**技术栈**：Node.js + Express + MongoDB + Redis + Docker  
**部署方式**：微服务架构，容器化部署，支持水平扩展  
**API风格**：RESTful API + WebSocket实时通信

### 1.2 系统架构
```
┌─────────────────────────────────────────────────┐
│                 API网关层                        │
│         (Nginx + API Gateway)                   │
├─────────────────────────────────────────────────┤
│                微服务层                          │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐         │
│  │用户服务  │ │配置服务  │ │推荐服务  │         │
│  │(User)    │ │(Config)  │ │(Recommend)│        │
│  └──────────┘ └──────────┘ └──────────┘         │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐         │
│  │统计服务  │ │文件服务  │ │通知服务  │         │
│  │(Analytics)│ │(File)    │ │(Notify)  │         │
│  └──────────┘ └──────────┘ └──────────┘         │
├─────────────────────────────────────────────────┤
│                数据存储层                        │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐         │
│  │ MongoDB  │ │  Redis   │ │MinIO/S3  │         │
│  │(主数据库)│ │(缓存)    │ │(文件存储)│         │
│  └──────────┘ └──────────┘ └──────────┘         │
├─────────────────────────────────────────────────┤
│                基础设施层                        │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐         │
│  │ Docker   │ │Kubernetes│ │ ELK Stack│         │
│  │(容器化)  │ │(编排)    │ │(日志监控)│         │
│  └──────────┘ └──────────┘ └──────────┘         │
└─────────────────────────────────────────────────┘
```

### 1.3 核心设计原则
- **微服务架构**：服务解耦，独立部署和扩展
- **高可用性**：多实例部署，故障自动恢复
- **数据安全**：加密传输，敏感数据加密存储
- **性能优化**：缓存策略，数据库优化，CDN加速
- **可扩展性**：水平扩展，负载均衡

## 2. 微服务设计

### 2.1 用户服务 (User Service)

#### 2.1.1 服务职责
- 用户注册、登录、认证
- 用户信息管理
- 设备管理和授权
- 用户权限控制

#### 2.1.2 API设计
```javascript
// 用户认证相关API
const userRoutes = {
    // 用户注册 - 支持邮箱和手机号注册
    'POST /api/v1/auth/register': {
        body: {
            email: '<EMAIL>',      // 邮箱地址
            password: 'password123',        // 密码(客户端已加密)
            username: 'username',           // 用户名
            deviceInfo: {                   // 设备信息
                deviceId: 'device-uuid',
                deviceName: 'My Computer',
                platform: 'Windows 11',
                clientVersion: '1.0.0'
            }
        },
        response: {
            success: true,
            data: {
                userId: 'user-id',
                token: 'jwt-token',
                refreshToken: 'refresh-token',
                expiresIn: 3600
            }
        }
    },

    // 用户登录 - 支持多种登录方式
    'POST /api/v1/auth/login': {
        body: {
            email: '<EMAIL>',
            password: 'password123',
            deviceInfo: { /* 设备信息 */ },
            rememberMe: true                // 是否记住登录状态
        }
    },

    // 刷新Token - 延长登录状态
    'POST /api/v1/auth/refresh': {
        headers: {
            'Authorization': 'Bearer refresh-token'
        }
    },

    // 获取用户信息
    'GET /api/v1/user/profile': {
        headers: {
            'Authorization': 'Bearer jwt-token'
        },
        response: {
            userId: 'user-id',
            username: 'username',
            email: '<EMAIL>',
            avatar: 'avatar-url',
            createdAt: '2024-01-01T00:00:00Z',
            settings: {
                theme: 'dark',
                language: 'zh-CN',
                notifications: true
            }
        }
    },

    // 设备管理 - 查看已授权设备
    'GET /api/v1/user/devices': {
        response: {
            devices: [{
                deviceId: 'device-uuid',
                deviceName: 'My Computer',
                platform: 'Windows 11',
                lastActive: '2024-01-01T12:00:00Z',
                isCurrentDevice: true
            }]
        }
    },

    // 撤销设备授权
    'DELETE /api/v1/user/devices/:deviceId': {}
};
```

#### 2.1.3 数据模型
```javascript
// 用户数据模型
const UserSchema = {
    _id: ObjectId,
    email: String,                          // 邮箱(唯一)
    username: String,                       // 用户名
    passwordHash: String,                   // 密码哈希
    avatar: String,                         // 头像URL
    status: String,                         // 账户状态: active/suspended/deleted
    settings: {
        theme: String,                      // 主题偏好
        language: String,                   // 语言偏好
        notifications: Boolean,             // 通知开关
        syncEnabled: Boolean,               // 同步开关
        privacyLevel: String               // 隐私级别: public/private
    },
    subscription: {                         // 订阅信息
        plan: String,                       // 订阅计划: free/premium/pro
        expiresAt: Date,                   // 过期时间
        features: [String]                 // 可用功能列表
    },
    createdAt: Date,
    updatedAt: Date,
    lastLoginAt: Date
};

// 设备信息模型
const DeviceSchema = {
    _id: ObjectId,
    userId: ObjectId,                       // 关联用户ID
    deviceId: String,                       // 设备唯一标识
    deviceName: String,                     // 设备名称
    platform: String,                      // 操作系统
    clientVersion: String,                  // 客户端版本
    isActive: Boolean,                      // 是否活跃
    lastActive: Date,                       // 最后活跃时间
    ipAddress: String,                      // IP地址
    location: {                            // 地理位置
        country: String,
        city: String,
        coordinates: [Number]              // [经度, 纬度]
    },
    createdAt: Date
};
```

### 2.2 配置服务 (Config Service)

#### 2.2.1 服务职责
- 用户配置数据同步
- 启动项配置管理
- 快捷键配置同步
- 主题和界面配置
- 配置版本控制和冲突解决

#### 2.2.2 API设计
```javascript
const configRoutes = {
    // 获取用户完整配置 - 支持增量同步
    'GET /api/v1/config/sync': {
        query: {
            lastSyncTime: '2024-01-01T12:00:00Z',  // 上次同步时间
            deviceId: 'device-uuid',                // 设备ID
            configVersion: '1.2.3'                 // 配置版本
        },
        response: {
            success: true,
            data: {
                configVersion: '1.2.4',
                lastModified: '2024-01-01T13:00:00Z',
                changes: {                          // 增量变更
                    launchItems: {
                        added: [/* 新增启动项 */],
                        updated: [/* 更新启动项 */],
                        deleted: ['item-id-1']     // 删除的启动项ID
                    },
                    settings: {
                        hotkeys: { /* 快捷键配置 */ },
                        theme: { /* 主题配置 */ }
                    }
                },
                fullConfig: {                       // 完整配置(首次同步)
                    launchItems: [/* 所有启动项 */],
                    categories: [/* 分类配置 */],
                    settings: {/* 所有设置 */}
                }
            }
        }
    },

    // 上传配置更改 - 支持批量更新
    'POST /api/v1/config/sync': {
        body: {
            deviceId: 'device-uuid',
            configVersion: '1.2.3',
            timestamp: '2024-01-01T13:00:00Z',
            changes: {
                launchItems: {
                    added: [{
                        id: 'item-uuid',
                        name: 'Visual Studio Code',
                        path: 'C:\\Program Files\\Microsoft VS Code\\Code.exe',
                        category: 'development',
                        icon: 'base64-icon-data',
                        tags: ['editor', 'development'],
                        frequency: 10,              // 使用频率
                        lastUsed: '2024-01-01T12:30:00Z',
                        customSettings: {           // 自定义设置
                            arguments: '--new-window',
                            workingDirectory: 'C:\\Projects'
                        }
                    }],
                    updated: [/* 更新的启动项 */],
                    deleted: ['old-item-id']
                },
                settings: {
                    hotkeys: {
                        'show_main_window': 'Ctrl+Space',
                        'quick_search': 'Ctrl+Shift+F'
                    },
                    theme: {
                        current: 'dark',
                        customColors: {
                            primary: '#007ACC',
                            background: '#1E1E1E'
                        }
                    }
                }
            }
        }
    },

    // 配置冲突解决 - 处理多设备同步冲突
    'POST /api/v1/config/resolve-conflict': {
        body: {
            conflictId: 'conflict-uuid',
            resolution: 'merge',                    // 解决策略: merge/client/server
            mergedConfig: {/* 合并后的配置 */}
        }
    },

    // 配置历史版本 - 支持回滚
    'GET /api/v1/config/history': {
        query: {
            limit: 10,
            offset: 0
        },
        response: {
            versions: [{
                version: '1.2.3',
                timestamp: '2024-01-01T12:00:00Z',
                deviceId: 'device-uuid',
                changesSummary: '添加了3个启动项，更新了主题配置',
                size: 1024                          // 配置大小(字节)
            }]
        }
    },

    // 恢复历史版本
    'POST /api/v1/config/restore/:version': {}
};
```

#### 2.2.3 配置数据模型
```javascript
// 用户配置主文档
const UserConfigSchema = {
    _id: ObjectId,
    userId: ObjectId,                       // 关联用户ID
    version: String,                        // 配置版本号
    lastModified: Date,                     // 最后修改时间
    lastSyncDeviceId: String,              // 最后同步的设备ID
    
    // 启动项配置
    launchItems: [{
        id: String,                         // 启动项唯一ID
        name: String,                       // 显示名称
        path: String,                       // 程序路径
        category: String,                   // 分类
        icon: String,                       // 图标数据(Base64)
        tags: [String],                     // 标签
        description: String,                // 描述
        frequency: Number,                  // 使用频率
        lastUsed: Date,                     // 最后使用时间
        isEnabled: Boolean,                 // 是否启用
        customSettings: {                   // 自定义设置
            arguments: String,              // 启动参数
            workingDirectory: String,       // 工作目录
            runAsAdmin: Boolean,           // 以管理员身份运行
            environment: Object            // 环境变量
        },
        createdAt: Date,
        updatedAt: Date
    }],
    
    // 分类配置
    categories: [{
        id: String,
        name: String,                       // 分类名称
        icon: String,                       // 分类图标
        color: String,                      // 分类颜色
        order: Number,                      // 排序
        isDefault: Boolean                  // 是否默认分类
    }],
    
    // 应用设置
    settings: {
        // 快捷键配置
        hotkeys: {
            showMainWindow: String,         // 显示主窗口
            quickSearch: String,            // 快速搜索
            toggleTheme: String,            // 切换主题
            customActions: [{               // 自定义快捷键
                name: String,
                key: String,
                action: String
            }]
        },
        
        // 界面设置
        ui: {
            theme: String,                  // 主题名称
            customTheme: {                  // 自定义主题
                colors: Object,
                fonts: Object
            },
            layout: String,                 // 布局模式: grid/list
            windowSize: {                   // 窗口大小
                width: Number,
                height: Number
            },
            showCategories: Boolean,        // 显示分类
            showFrequency: Boolean,         // 显示使用频率
            animationEnabled: Boolean       // 启用动画
        },
        
        // 行为设置
        behavior: {
            autoStart: Boolean,             // 开机自启
            minimizeToTray: Boolean,        // 最小化到托盘
            closeToTray: Boolean,           // 关闭到托盘
            searchDelay: Number,            // 搜索延迟(ms)
            maxRecentItems: Number,         // 最大最近项目数
            enableAnalytics: Boolean,       // 启用数据分析
            enableRecommendations: Boolean  // 启用推荐功能
        },
        
        // 同步设置
        sync: {
            enabled: Boolean,               // 启用同步
            autoSync: Boolean,              // 自动同步
            syncInterval: Number,           // 同步间隔(分钟)
            conflictResolution: String,     // 冲突解决策略
            excludeCategories: [String],    // 排除同步的分类
            encryptionEnabled: Boolean      // 启用加密
        }
    },
    
    createdAt: Date,
    updatedAt: Date
};

// 配置变更历史
const ConfigHistorySchema = {
    _id: ObjectId,
    userId: ObjectId,
    configId: ObjectId,                     // 关联配置ID
    version: String,                        // 版本号
    deviceId: String,                       // 操作设备
    changeType: String,                     // 变更类型: create/update/delete
    changes: {                              // 具体变更内容
        path: String,                       // 变更路径
        oldValue: Mixed,                    // 旧值
        newValue: Mixed,                    // 新值
        operation: String                   // 操作类型: add/modify/remove
    },
    metadata: {
        userAgent: String,                  // 客户端信息
        ipAddress: String,                  // IP地址
        changesSummary: String,             // 变更摘要
        size: Number                        // 变更大小
    },
    timestamp: Date
};
```

### 2.3 推荐服务 (Recommendation Service)

#### 2.3.1 服务职责
- 软件推荐算法
- 用户行为分析
- 个性化推荐
- 推荐效果统计
- 软件库管理

#### 2.3.2 API设计
```javascript
const recommendationRoutes = {
    // 获取个性化推荐 - 基于用户行为和偏好
    'POST /api/v1/recommend/apps': {
        body: {
            context: {
                fileType: 'json',           // 当前文件类型
                clipboardContent: '{"name": "test"}', // 剪贴板内容
                activeWindow: 'chrome.exe', // 当前活动窗口
                timeOfDay: 'morning',       // 时间段
                workingDirectory: 'C:\\Projects', // 工作目录
                recentFiles: [              // 最近文件
                    'config.json',
                    'README.md'
                ]
            },
            userPreferences: {
                categories: ['development', 'productivity'], // 偏好分类
                excludeApps: ['notepad.exe'], // 排除应用
                maxResults: 10,             // 最大结果数
                includeReasons: true        // 包含推荐理由
            }
        },
        response: {
            recommendations: [{
                appId: 'vscode',
                name: 'Visual Studio Code',
                description: '强大的代码编辑器',
                category: 'development',
                icon: 'icon-url',
                downloadUrl: 'https://code.visualstudio.com',
                score: 0.95,                // 推荐分数
                confidence: 0.88,           // 置信度
                reasons: [                  // 推荐理由
                    '支持JSON语法高亮',
                    '您经常在此时间段使用开发工具',
                    '与您的工作目录匹配'
                ],
                metadata: {
                    size: '85MB',
                    version: '1.85.0',
                    rating: 4.8,
                    downloads: 1000000,
                    tags: ['editor', 'development', 'microsoft'],
                    supportedFormats: ['json', 'js', 'ts', 'py'],
                    systemRequirements: {
                        os: 'Windows 10+',
                        memory: '1GB',
                        disk: '200MB'
                    }
                }
            }]
        }
    },

    // 记录用户反馈 - 用于改进推荐算法
    'POST /api/v1/recommend/feedback': {
        body: {
            recommendationId: 'rec-uuid',
            action: 'installed',            // 用户行为: viewed/clicked/installed/dismissed
            rating: 5,                      // 评分 1-5
            feedback: '很好用的编辑器',     // 文字反馈
            context: {                      // 使用上下文
                sessionId: 'session-uuid',
                timestamp: '2024-01-01T12:00:00Z'
            }
        }
    },

    // 获取热门软件 - 基于全局统计
    'GET /api/v1/recommend/trending': {
        query: {
            category: 'development',        // 分类过滤
            timeRange: '7d',               // 时间范围: 1d/7d/30d
            platform: 'windows',          // 平台过滤
            limit: 20
        },
        response: {
            trending: [{
                appId: 'app-id',
                name: 'App Name',
                trendScore: 0.85,           // 趋势分数
                growthRate: 0.15,           // 增长率
                installCount: 50000,        // 安装数量
                category: 'development'
            }]
        }
    },

    // 相似软件推荐 - 基于软件特征
    'GET /api/v1/recommend/similar/:appId': {
        query: {
            limit: 10,
            includeAlternatives: true       // 包含替代品
        },
        response: {
            similar: [{
                appId: 'similar-app-id',
                similarity: 0.78,           // 相似度
                comparisonFeatures: [       // 对比特征
                    '都支持语法高亮',
                    '都有插件系统',
                    '界面风格相似'
                ]
            }]
        }
    },

    // 软件搜索 - 智能搜索和过滤
    'GET /api/v1/apps/search': {
        query: {
            q: 'json editor',              // 搜索关键词
            category: 'development',       // 分类过滤
            platform: 'windows',          // 平台过滤
            free: true,                    // 只显示免费软件
            rating: 4.0,                   // 最低评分
            sort: 'relevance',             // 排序: relevance/rating/downloads/name
            limit: 20,
            offset: 0
        }
    }
};
```

#### 2.3.3 推荐算法实现
```javascript
// 推荐引擎核心类
class RecommendationEngine {
    constructor() {
        this.algorithms = {
            contentBased: new ContentBasedRecommender(),    // 基于内容的推荐
            collaborative: new CollaborativeRecommender(),  // 协同过滤推荐
            contextAware: new ContextAwareRecommender(),    // 上下文感知推荐
            hybrid: new HybridRecommender()                 // 混合推荐
        };
        
        this.weights = {                    // 算法权重配置
            contentBased: 0.3,
            collaborative: 0.3,
            contextAware: 0.25,
            hybrid: 0.15
        };
    }
    
    // 生成个性化推荐
    async generateRecommendations(userId, context, preferences) {
        try {
            // 1. 获取用户画像
            const userProfile = await this.getUserProfile(userId);
            
            // 2. 并行执行多种推荐算法
            const [
                contentRecommendations,
                collaborativeRecommendations,
                contextRecommendations,
                hybridRecommendations
            ] = await Promise.all([
                this.algorithms.contentBased.recommend(userProfile, context),
                this.algorithms.collaborative.recommend(userId, preferences),
                this.algorithms.contextAware.recommend(context, userProfile),
                this.algorithms.hybrid.recommend(userId, context, userProfile)
            ]);
            
            // 3. 合并和加权计算
            const mergedRecommendations = this.mergeRecommendations([
                { recommendations: contentRecommendations, weight: this.weights.contentBased },
                { recommendations: collaborativeRecommendations, weight: this.weights.collaborative },
                { recommendations: contextRecommendations, weight: this.weights.contextAware },
                { recommendations: hybridRecommendations, weight: this.weights.hybrid }
            ]);
            
            // 4. 过滤和排序
            const filteredRecommendations = this.applyFilters(
                mergedRecommendations, 
                preferences
            );
            
            // 5. 多样性优化 - 避免推荐过于相似的软件
            const diversifiedRecommendations = this.diversifyRecommendations(
                filteredRecommendations
            );
            
            // 6. 生成推荐理由
            const recommendationsWithReasons = await this.generateReasons(
                diversifiedRecommendations,
                context,
                userProfile
            );
            
            return recommendationsWithReasons;
            
        } catch (error) {
            console.error('推荐生成失败:', error);
            // 降级到基础推荐
            return await this.getFallbackRecommendations(context);
        }
    }
    
    // 基于内容的推荐算法
    async contentBasedRecommend(userProfile, context) {
        const { fileType, clipboardContent, activeWindow } = context;
        
        // 分析当前上下文特征
        const contextFeatures = await this.extractContextFeatures(context);
        
        // 获取候选软件
        const candidateApps = await this.getCandidateApps(contextFeatures);
        
        // 计算相似度分数
        const recommendations = candidateApps.map(app => {
            const similarity = this.calculateSimilarity(
                contextFeatures, 
                app.features
            );
            
            return {
                ...app,
                score: similarity,
                algorithm: 'content-based'
            };
        });
        
        return recommendations.sort((a, b) => b.score - a.score);
    }
    
    // 协同过滤推荐算法
    async collaborativeRecommend(userId, preferences) {
        // 1. 找到相似用户
        const similarUsers = await this.findSimilarUsers(userId);
        
        // 2. 获取相似用户喜欢的软件
        const candidateApps = await this.getAppsFromSimilarUsers(similarUsers);
        
        // 3. 计算推荐分数
        const recommendations = candidateApps.map(app => {
            const score = this.calculateCollaborativeScore(
                app, 
                similarUsers, 
                userId
            );
            
            return {
                ...app,
                score,
                algorithm: 'collaborative'
            };
        });
        
        return recommendations.filter(rec => rec.score > 0.1);
    }
    
    // 上下文感知推荐
    async contextAwareRecommend(context, userProfile) {
        const { timeOfDay, workingDirectory, recentFiles } = context;
        
        // 时间段偏好分析
        const timePreferences = this.analyzeTimePreferences(
            userProfile.behaviorHistory, 
            timeOfDay
        );
        
        // 工作场景识别
        const workContext = this.identifyWorkContext(
            workingDirectory, 
            recentFiles
        );
        
        // 获取上下文相关的软件
        const contextualApps = await this.getContextualApps(
            timePreferences, 
            workContext
        );
        
        return contextualApps.map(app => ({
            ...app,
            score: this.calculateContextScore(app, context, userProfile),
            algorithm: 'context-aware'
        }));
    }
    
    // 推荐结果合并
    mergeRecommendations(algorithmResults) {
        const appScores = new Map();
        
        // 加权合并各算法结果
        algorithmResults.forEach(({ recommendations, weight }) => {
            recommendations.forEach(rec => {
                const currentScore = appScores.get(rec.appId) || 0;
                appScores.set(rec.appId, currentScore + rec.score * weight);
            });
        });
        
        // 转换为推荐列表
        const mergedRecommendations = [];
        for (const [appId, score] of appScores) {
            const appInfo = await this.getAppInfo(appId);
            mergedRecommendations.push({
                ...appInfo,
                score,
                confidence: this.calculateConfidence(score, appId)
            });
        }
        
        return mergedRecommendations.sort((a, b) => b.score - a.score);
    }
    
    // 生成推荐理由
    async generateReasons(recommendations, context, userProfile) {
        return Promise.all(recommendations.map(async rec => {
            const reasons = [];
            
            // 基于文件类型的理由
            if (context.fileType && rec.supportedFormats?.includes(context.fileType)) {
                reasons.push(`支持${context.fileType.toUpperCase()}文件格式`);
            }
            
            // 基于使用时间的理由
            if (this.isFrequentlyUsedAtTime(rec.appId, context.timeOfDay, userProfile)) {
                reasons.push(`您经常在${this.getTimeDescription(context.timeOfDay)}使用此类工具`);
            }
            
            // 基于相似用户的理由
            const similarUserCount = await this.getSimilarUserCount(rec.appId, userProfile.userId);
            if (similarUserCount > 10) {
                reasons.push(`${similarUserCount}位相似用户也在使用`);
            }
            
            // 基于软件特性的理由
            if (rec.rating > 4.5) {
                reasons.push(`高评分软件(${rec.rating}★)`);
            }
            
            return {
                ...rec,
                reasons: reasons.slice(0, 3) // 最多显示3个理由
            };
        }));
    }
}
```

### 2.4 统计分析服务 (Analytics Service)

#### 2.4.1 服务职责
- 用户行为数据收集
- 软件使用统计分析
- 推荐效果评估
- 系统性能监控
- 数据报表生成

#### 2.4.2 API设计
```javascript
const analyticsRoutes = {
    // 上报用户行为数据 - 批量上报提高效率
    'POST /api/v1/analytics/events': {
        body: {
            deviceId: 'device-uuid',
            sessionId: 'session-uuid',
            events: [{
                eventType: 'app_launch',        // 事件类型
                timestamp: '2024-01-01T12:00:00Z',
                data: {
                    appId: 'vscode',
                    appName: 'Visual Studio Code',
                    launchMethod: 'search',     // 启动方式: search/hotkey/click
                    searchQuery: 'code',        // 搜索关键词
                    responseTime: 150,          // 响应时间(ms)
                    context: {
                        fileType: 'json',
                        timeOfDay: 'morning',
                        workingDirectory: 'C:\\Projects'
                    }
                }
            }, {
                eventType: 'recommendation_shown',
                timestamp: '2024-01-01T12:01:00Z',
                data: {
                    recommendationId: 'rec-uuid',
                    position: 1,                // 推荐位置
                    algorithm: 'hybrid',        // 推荐算法
                    score: 0.95,               // 推荐分数
                    context: {/* 上下文信息 */}
                }
            }]
        }
    },

    // 获取用户使用统计 - 个人数据分析
    'GET /api/v1/analytics/user/stats': {
        query: {
            timeRange: '30d',               // 时间范围
            granularity: 'day'              // 粒度: hour/day/week/month
        },
        response: {
            summary: {
                totalLaunches: 1250,        // 总启动次数
                uniqueApps: 45,             // 使用的不同应用数
                avgLaunchesPerDay: 41.7,    // 日均启动次数
                mostActiveHour: 14,         // 最活跃时间
                topCategories: [            // 最常用分类
                    { category: 'development', count: 580 },
                    { category: 'productivity', count: 320 }
                ]
            },
            timeline: [{                    // 时间线数据
                date: '2024-01-01',
                launches: 42,
                uniqueApps: 12,
                categories: {
                    'development': 25,
                    'productivity': 17
                }
            }],
            topApps: [{                     // 最常用应用
                appId: 'vscode',
                name: 'Visual Studio Code',
                launches: 180,
                percentage: 14.4,
                avgSessionTime: 3600        // 平均使用时长(秒)
            }]
        }
    },

    // 获取推荐效果统计
    'GET /api/v1/analytics/recommendations/performance': {
        response: {
            overall: {
                totalRecommendations: 5000,
                clickThroughRate: 0.25,     // 点击率
                installationRate: 0.08,     // 安装率
                avgRating: 4.2,             // 平均评分
                userSatisfaction: 0.78      // 用户满意度
            },
            byAlgorithm: [{
                algorithm: 'hybrid',
                recommendations: 2000,
                ctr: 0.32,                  // 点击率
                conversionRate: 0.12,       // 转化率
                avgScore: 0.85
            }],
            byCategory: [{
                category: 'development',
                recommendations: 1500,
                ctr: 0.35,
                popularApps: ['vscode', 'git', 'postman']
            }]
        }
    }
};
```

### 2.5 文件服务 (File Service)

#### 2.5.1 服务职责
- 应用图标存储和CDN
- 用户头像管理
- 配置文件备份
- 软件安装包缓存
- 静态资源服务

#### 2.5.2 API设计
```javascript
const fileRoutes = {
    // 上传应用图标 - 支持多种格式
    'POST /api/v1/files/icons': {
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        body: {
            file: 'icon-file',              // 图标文件
            appId: 'app-id',               // 关联应用ID
            size: '64x64'                  // 图标尺寸
        },
        response: {
            iconId: 'icon-uuid',
            url: 'https://cdn.example.com/icons/icon-uuid.png',
            sizes: {                        // 自动生成多种尺寸
                '16x16': 'url-16',
                '32x32': 'url-32',
                '64x64': 'url-64',
                '128x128': 'url-128'
            }
        }
    },

    // 获取应用图标 - CDN加速
    'GET /api/v1/files/icons/:iconId': {
        query: {
            size: '64x64',                  // 请求尺寸
            format: 'png'                   // 输出格式: png/jpg/webp
        }
    },

    // 配置备份上传
    'POST /api/v1/files/backup': {
        body: {
            backupType: 'full',             // 备份类型: full/incremental
            data: 'encrypted-config-data',  // 加密的配置数据
            checksum: 'sha256-hash',        // 数据校验和
            metadata: {
                version: '1.2.3',
                deviceId: 'device-uuid',
                timestamp: '2024-01-01T12:00:00Z'
            }
        }
    }
};
```

### 2.6 通知服务 (Notification Service)

#### 2.6.1 服务职责
- 实时消息推送
- 系统通知管理
- 软件更新提醒
- 用户消息中心
- WebSocket连接管理

#### 2.6.2 WebSocket API设计
```javascript
// WebSocket连接管理
class NotificationService {
    // 客户端连接认证
    onConnection(socket) {
        socket.on('authenticate', async (data) => {
            const { token, deviceId } = data;

            try {
                const user = await this.verifyToken(token);
                socket.userId = user.id;
                socket.deviceId = deviceId;

                // 加入用户房间
                socket.join(`user:${user.id}`);

                // 发送认证成功消息
                socket.emit('authenticated', {
                    success: true,
                    userId: user.id
                });

                // 发送离线消息
                await this.sendOfflineMessages(socket, user.id);

            } catch (error) {
                socket.emit('auth_error', {
                    message: '认证失败'
                });
                socket.disconnect();
            }
        });

        // 处理客户端消息
        socket.on('message', (data) => {
            this.handleClientMessage(socket, data);
        });

        // 连接断开处理
        socket.on('disconnect', () => {
            this.handleDisconnect(socket);
        });
    }

    // 推送通知到用户
    async pushNotification(userId, notification) {
        const message = {
            id: generateUUID(),
            type: notification.type,        // 通知类型
            title: notification.title,      // 标题
            content: notification.content,  // 内容
            data: notification.data,        // 附加数据
            timestamp: new Date().toISOString(),
            priority: notification.priority || 'normal' // 优先级
        };

        // 实时推送给在线用户
        this.io.to(`user:${userId}`).emit('notification', message);

        // 保存到数据库供离线查看
        await this.saveNotification(userId, message);

        // 如果用户离线，发送推送通知
        const isOnline = await this.isUserOnline(userId);
        if (!isOnline) {
            await this.sendPushNotification(userId, message);
        }
    }
}

// 通知类型定义
const NotificationTypes = {
    SOFTWARE_UPDATE: 'software_update',        // 软件更新
    NEW_RECOMMENDATION: 'new_recommendation',  // 新推荐
    SYNC_COMPLETE: 'sync_complete',           // 同步完成
    SYSTEM_MESSAGE: 'system_message',         // 系统消息
    SECURITY_ALERT: 'security_alert'          // 安全警告
};
```

## 3. 数据库设计

### 3.1 MongoDB集合设计

#### 3.1.1 软件库集合 (apps)
```javascript
const AppSchema = {
    _id: ObjectId,
    appId: String,                          // 应用唯一标识
    name: String,                           // 应用名称
    description: String,                    // 应用描述
    category: String,                       // 主分类
    subcategories: [String],                // 子分类

    // 基本信息
    developer: String,                      // 开发商
    website: String,                        // 官方网站
    version: String,                        // 最新版本
    releaseDate: Date,                      // 发布日期
    lastUpdated: Date,                      // 最后更新

    // 下载信息
    downloadUrl: String,                    // 下载链接
    alternativeUrls: [String],              // 备用下载链接
    fileSize: Number,                       // 文件大小(字节)
    checksum: String,                       // 文件校验和

    // 系统要求
    systemRequirements: {
        windows: {
            minVersion: String,             // 最低系统版本
            architecture: [String],         // 支持架构: x86/x64/arm64
            memory: String,                 // 内存要求
            disk: String,                   // 磁盘空间
            additional: [String]            // 其他要求
        },
        linux: {/* Linux要求 */},
        macos: {/* macOS要求 */}
    },

    // 功能特性
    features: [String],                     // 功能标签
    supportedFormats: [String],             // 支持的文件格式
    languages: [String],                    // 支持的语言
    license: String,                        // 许可证类型
    price: {
        type: String,                       // 价格类型: free/paid/freemium
        amount: Number,                     // 价格
        currency: String                    // 货币
    },

    // 统计信息
    stats: {
        downloads: Number,                  // 下载次数
        rating: Number,                     // 平均评分
        reviewCount: Number,                // 评论数量
        popularity: Number,                 // 流行度分数
        trendScore: Number                  // 趋势分数
    },

    // 媒体资源
    media: {
        icon: String,                       // 图标URL
        screenshots: [String],              // 截图URLs
        videos: [String]                    // 视频URLs
    },

    // 元数据
    metadata: {
        tags: [String],                     // 搜索标签
        keywords: [String],                 // 关键词
        alternativeNames: [String],         // 别名
        relatedApps: [String],             // 相关应用ID
        competitors: [String]               // 竞品应用ID
    },

    // 状态信息
    status: String,                         // 状态: active/deprecated/removed
    isVerified: Boolean,                    // 是否已验证
    isFeatured: Boolean,                    // 是否推荐

    createdAt: Date,
    updatedAt: Date
};
```

#### 3.1.2 用户行为集合 (user_behaviors)
```javascript
const UserBehaviorSchema = {
    _id: ObjectId,
    userId: ObjectId,                       // 用户ID
    deviceId: String,                       // 设备ID
    sessionId: String,                      // 会话ID

    // 事件信息
    eventType: String,                      // 事件类型
    timestamp: Date,                        // 事件时间

    // 应用相关
    appId: String,                          // 应用ID
    appName: String,                        // 应用名称
    category: String,                       // 应用分类

    // 行为数据
    action: String,                         // 具体行为: launch/search/install/uninstall
    method: String,                         // 操作方式: click/hotkey/search/recommendation

    // 上下文信息
    context: {
        fileType: String,                   // 当前文件类型
        clipboardContent: String,           // 剪贴板内容(脱敏)
        activeWindow: String,               // 活动窗口
        workingDirectory: String,           // 工作目录
        timeOfDay: String,                  // 时间段
        dayOfWeek: Number,                  // 星期几
        searchQuery: String,                // 搜索查询
        responseTime: Number,               // 响应时间
        position: Number                    // 在结果中的位置
    },

    // 推荐相关
    recommendation: {
        recommendationId: String,           // 推荐ID
        algorithm: String,                  // 推荐算法
        score: Number,                      // 推荐分数
        reasons: [String]                   // 推荐理由
    },

    // 反馈信息
    feedback: {
        rating: Number,                     // 用户评分
        satisfaction: Number,               // 满意度
        comment: String                     // 评论
    },

    // 技术信息
    technical: {
        userAgent: String,                  // 客户端信息
        clientVersion: String,              // 客户端版本
        platform: String,                  // 操作系统
        ipAddress: String,                  // IP地址(脱敏)
        location: {                         // 地理位置
            country: String,
            city: String
        }
    }
};
```

### 3.2 Redis缓存设计

#### 3.2.1 缓存策略
```javascript
// 缓存键命名规范
const CacheKeys = {
    // 用户相关
    USER_PROFILE: 'user:profile:{userId}',
    USER_CONFIG: 'user:config:{userId}',
    USER_DEVICES: 'user:devices:{userId}',

    // 推荐相关
    RECOMMENDATIONS: 'rec:user:{userId}:context:{hash}',
    TRENDING_APPS: 'trending:apps:{category}:{timeRange}',
    SIMILAR_APPS: 'similar:app:{appId}',

    // 应用相关
    APP_INFO: 'app:info:{appId}',
    APP_STATS: 'app:stats:{appId}',
    CATEGORY_APPS: 'category:{category}:apps',

    // 会话相关
    USER_SESSION: 'session:{sessionId}',
    ACTIVE_USERS: 'active:users',

    // 系统相关
    SYSTEM_CONFIG: 'system:config',
    API_RATE_LIMIT: 'rate:limit:{userId}:{endpoint}'
};

// 缓存TTL配置
const CacheTTL = {
    USER_PROFILE: 3600,                     // 1小时
    USER_CONFIG: 1800,                      // 30分钟
    RECOMMENDATIONS: 600,                   // 10分钟
    TRENDING_APPS: 3600,                    // 1小时
    APP_INFO: 86400,                        // 24小时
    SESSION: 7200                           // 2小时
};
```

## 4. 部署架构

### 4.1 Docker容器化

#### 4.1.1 服务容器配置
```dockerfile
# 用户服务 Dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production

# 复制源码
COPY src/ ./src/
COPY config/ ./config/

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# 启动服务
EXPOSE 3000
CMD ["node", "src/user-service/index.js"]
```

#### 4.1.2 Docker Compose配置
```yaml
version: '3.8'

services:
  # API网关
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - user-service
      - config-service
      - recommendation-service

  # 用户服务
  user-service:
    build:
      context: .
      dockerfile: services/user/Dockerfile
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/quicklaunch
      - REDIS_URI=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mongodb
      - redis
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # 配置服务
  config-service:
    build:
      context: .
      dockerfile: services/config/Dockerfile
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/quicklaunch
      - REDIS_URI=redis://redis:6379
    depends_on:
      - mongodb
      - redis

  # 推荐服务
  recommendation-service:
    build:
      context: .
      dockerfile: services/recommendation/Dockerfile
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/quicklaunch
      - REDIS_URI=redis://redis:6379
      - ML_MODEL_PATH=/app/models
    volumes:
      - ./models:/app/models
    depends_on:
      - mongodb
      - redis

  # 数据库
  mongodb:
    image: mongo:6.0
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    ports:
      - "27017:27017"

  # 缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"

  # 文件存储
  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"

volumes:
  mongodb_data:
  redis_data:
  minio_data:
```

### 4.2 Kubernetes部署

#### 4.2.1 服务部署配置
```yaml
# user-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  labels:
    app: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: quicklaunch/user-service:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: mongodb-uri
        - name: REDIS_URI
          valueFrom:
            secretKeyRef:
              name: cache-secret
              key: redis-uri
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: user-service
spec:
  selector:
    app: user-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
```

### 4.3 监控和日志

#### 4.3.1 Prometheus监控配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:3000']
    metrics_path: '/metrics'

  - job_name: 'config-service'
    static_configs:
      - targets: ['config-service:3000']

  - job_name: 'recommendation-service'
    static_configs:
      - targets: ['recommendation-service:3000']

  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb-exporter:9216']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

#### 4.3.2 ELK日志配置
```yaml
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "user-service" {
    json {
      source => "message"
    }

    date {
      match => [ "timestamp", "ISO8601" ]
    }

    mutate {
      add_field => { "service_name" => "user-service" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "quicklaunch-logs-%{+YYYY.MM.dd}"
  }
}
```

## 5. 安全设计

### 5.1 认证和授权

#### 5.1.1 JWT Token设计
```javascript
// JWT Payload结构
const jwtPayload = {
    sub: 'user-id',                         // 用户ID
    iat: 1640995200,                        // 签发时间
    exp: 1640998800,                        // 过期时间
    aud: 'quicklaunch-client',              // 受众
    iss: 'quicklaunch-auth',                // 签发者

    // 自定义声明
    deviceId: 'device-uuid',                // 设备ID
    permissions: [                          // 权限列表
        'config:read',
        'config:write',
        'recommendations:read'
    ],
    plan: 'premium',                        // 订阅计划
    features: [                             // 可用功能
        'sync',
        'advanced_recommendations',
        'analytics'
    ]
};

// Token验证中间件
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return res.status(401).json({ error: '缺少访问令牌' });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // 检查设备授权
        const isDeviceAuthorized = await checkDeviceAuthorization(
            decoded.sub,
            decoded.deviceId
        );

        if (!isDeviceAuthorized) {
            return res.status(403).json({ error: '设备未授权' });
        }

        req.user = decoded;
        next();

    } catch (error) {
        return res.status(403).json({ error: '无效的访问令牌' });
    }
};
```

### 5.2 数据加密

#### 5.2.1 敏感数据加密
```javascript
// 配置数据加密
class ConfigEncryption {
    constructor() {
        this.algorithm = 'aes-256-gcm';
        this.keyDerivation = 'pbkdf2';
    }

    // 加密用户配置
    async encryptConfig(config, userPassword) {
        try {
            // 生成随机盐值
            const salt = crypto.randomBytes(32);

            // 派生加密密钥
            const key = crypto.pbkdf2Sync(
                userPassword,
                salt,
                100000,
                32,
                'sha256'
            );

            // 生成随机IV
            const iv = crypto.randomBytes(16);

            // 创建加密器
            const cipher = crypto.createCipher(this.algorithm, key, iv);

            // 加密配置数据
            const configJson = JSON.stringify(config);
            let encrypted = cipher.update(configJson, 'utf8', 'hex');
            encrypted += cipher.final('hex');

            // 获取认证标签
            const authTag = cipher.getAuthTag();

            return {
                encrypted,
                salt: salt.toString('hex'),
                iv: iv.toString('hex'),
                authTag: authTag.toString('hex'),
                algorithm: this.algorithm
            };

        } catch (error) {
            throw new Error('配置加密失败: ' + error.message);
        }
    }

    // 解密用户配置
    async decryptConfig(encryptedData, userPassword) {
        try {
            const { encrypted, salt, iv, authTag, algorithm } = encryptedData;

            // 重新派生密钥
            const key = crypto.pbkdf2Sync(
                userPassword,
                Buffer.from(salt, 'hex'),
                100000,
                32,
                'sha256'
            );

            // 创建解密器
            const decipher = crypto.createDecipher(
                algorithm,
                key,
                Buffer.from(iv, 'hex')
            );

            // 设置认证标签
            decipher.setAuthTag(Buffer.from(authTag, 'hex'));

            // 解密数据
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');

            return JSON.parse(decrypted);

        } catch (error) {
            throw new Error('配置解密失败: ' + error.message);
        }
    }
}
```

### 5.3 API安全

#### 5.3.1 速率限制
```javascript
// Redis-based rate limiting
const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');

const createRateLimiter = (options) => {
    return rateLimit({
        store: new RedisStore({
            client: redisClient,
            prefix: 'rl:',
        }),
        windowMs: options.windowMs || 15 * 60 * 1000, // 15分钟
        max: options.max || 100,                       // 最大请求数
        message: {
            error: '请求过于频繁，请稍后再试',
            retryAfter: options.windowMs / 1000
        },
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => {
            // 基于用户ID和IP的复合键
            return `${req.user?.sub || 'anonymous'}:${req.ip}`;
        }
    });
};

// 不同端点的速率限制配置
const rateLimiters = {
    auth: createRateLimiter({
        windowMs: 15 * 60 * 1000,          // 15分钟
        max: 5                              // 最多5次登录尝试
    }),

    api: createRateLimiter({
        windowMs: 15 * 60 * 1000,          // 15分钟
        max: 1000                           // 普通API请求
    }),

    upload: createRateLimiter({
        windowMs: 60 * 60 * 1000,          // 1小时
        max: 10                             // 文件上传限制
    })
};
```

## 6. 性能优化

### 6.1 数据库优化

#### 6.1.1 MongoDB索引设计
```javascript
// 用户集合索引
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "status": 1, "lastLoginAt": -1 });

// 用户配置集合索引
db.user_configs.createIndex({ "userId": 1 }, { unique: true });
db.user_configs.createIndex({ "lastModified": -1 });
db.user_configs.createIndex({ "userId": 1, "version": 1 });

// 用户行为集合索引
db.user_behaviors.createIndex({ "userId": 1, "timestamp": -1 });
db.user_behaviors.createIndex({ "eventType": 1, "timestamp": -1 });
db.user_behaviors.createIndex({ "appId": 1, "timestamp": -1 });
db.user_behaviors.createIndex({
    "userId": 1,
    "eventType": 1,
    "timestamp": -1
});

// 应用集合索引
db.apps.createIndex({ "appId": 1 }, { unique: true });
db.apps.createIndex({ "category": 1, "stats.popularity": -1 });
db.apps.createIndex({ "status": 1, "isVerified": 1 });
db.apps.createIndex({
    "name": "text",
    "description": "text",
    "metadata.keywords": "text"
});

// 复合索引优化查询
db.apps.createIndex({
    "category": 1,
    "status": 1,
    "stats.rating": -1
});
```

### 6.2 缓存策略

#### 6.2.1 多级缓存架构
```javascript
// 缓存管理器
class CacheManager {
    constructor() {
        this.l1Cache = new Map();           // 内存缓存(L1)
        this.l2Cache = redisClient;         // Redis缓存(L2)
        this.maxL1Size = 1000;              // L1缓存最大条目数
    }

    // 获取缓存数据
    async get(key) {
        // 1. 尝试从L1缓存获取
        if (this.l1Cache.has(key)) {
            const item = this.l1Cache.get(key);
            if (item.expiry > Date.now()) {
                return item.value;
            } else {
                this.l1Cache.delete(key);
            }
        }

        // 2. 尝试从L2缓存获取
        const l2Value = await this.l2Cache.get(key);
        if (l2Value) {
            const value = JSON.parse(l2Value);

            // 回填到L1缓存
            this.setL1Cache(key, value, 300); // 5分钟

            return value;
        }

        return null;
    }

    // 设置缓存数据
    async set(key, value, ttl = 3600) {
        // 设置L1缓存
        this.setL1Cache(key, value, Math.min(ttl, 300));

        // 设置L2缓存
        await this.l2Cache.setex(key, ttl, JSON.stringify(value));
    }

    // 设置L1缓存
    setL1Cache(key, value, ttl) {
        // 检查缓存大小限制
        if (this.l1Cache.size >= this.maxL1Size) {
            // LRU淘汰策略
            const firstKey = this.l1Cache.keys().next().value;
            this.l1Cache.delete(firstKey);
        }

        this.l1Cache.set(key, {
            value,
            expiry: Date.now() + ttl * 1000
        });
    }

    // 缓存预热
    async warmup() {
        console.log('开始缓存预热...');

        // 预热热门应用数据
        const trendingApps = await this.getTrendingApps();
        for (const app of trendingApps) {
            await this.set(`app:info:${app.appId}`, app, 86400);
        }

        // 预热分类数据
        const categories = await this.getCategories();
        await this.set('app:categories', categories, 86400);

        console.log('缓存预热完成');
    }
}
```

---

**文档版本**: v1.0
**最后更新**: 2024年12月
**技术栈**: Node.js + Express + MongoDB + Redis
**维护团队**: 后端开发组
