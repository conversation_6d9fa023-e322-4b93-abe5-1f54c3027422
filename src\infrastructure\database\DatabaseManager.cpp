#include "DatabaseManager.h"
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDir>
#include <QFileInfo>
#include <QStandardPaths>
#include <QLoggingCategory>
#include <QElapsedTimer>
#include <QCoreApplication>

Q_LOGGING_CATEGORY(lcDatabase, "database")

// 静态成员初始化
const QString DatabaseManager::CONNECTION_NAME = "KKQuickLaunch_Main";
const int DatabaseManager::SLOW_QUERY_THRESHOLD = 1000; // 1秒
const int DatabaseManager::MAX_QUERY_LOG_SIZE = 1000;
const int DatabaseManager::MAINTENANCE_INTERVAL = 3600000; // 1小时

DatabaseManager::DatabaseManager(QObject *parent)
    : QObject(parent)
    , m_inTransaction(false)
    , m_queryLoggingEnabled(false)
    , m_maintenanceTimer(new QTimer(this))
{
    // 设置维护定时器
    m_maintenanceTimer->setInterval(MAINTENANCE_INTERVAL);
    m_maintenanceTimer->setSingleShot(false);
    connect(m_maintenanceTimer, &QTimer::timeout, this, &DatabaseManager::performMaintenance);
    
    qCDebug(lcDatabase) << "DatabaseManager created";
}

DatabaseManager::~DatabaseManager()
{
    close();
    qCDebug(lcDatabase) << "DatabaseManager destroyed";
}

bool DatabaseManager::initialize(const QString &databasePath)
{
    QWriteLocker locker(&m_lock);
    
    if (m_database.isOpen()) {
        qCWarning(lcDatabase) << "Database already initialized";
        return true;
    }
    
    m_databasePath = databasePath;
    
    // 创建数据库目录
    QFileInfo fileInfo(m_databasePath);
    QDir().mkpath(fileInfo.absolutePath());
    
    // 配置数据库连接
    m_database = QSqlDatabase::addDatabase("QSQLITE", CONNECTION_NAME);
    m_database.setDatabaseName(m_databasePath);
    
    // 设置连接选项
    m_database.setConnectOptions("QSQLITE_BUSY_TIMEOUT=30000"); // 30秒超时
    
    // 打开数据库
    if (!m_database.open()) {
        QString error = m_database.lastError().text();
        qCCritical(lcDatabase) << "Failed to open database:" << error;
        emit this->error(error);
        return false;
    }
    
    qCInfo(lcDatabase) << "Database opened:" << m_databasePath;
    
    // 初始化数据库设置
    if (!initializeSettings()) {
        qCCritical(lcDatabase) << "Failed to initialize database settings";
        return false;
    }
    
    // 创建表结构
    if (!createTables()) {
        qCCritical(lcDatabase) << "Failed to create database tables";
        return false;
    }
    
    // 加载迁移
    loadMigrations();
    
    // 运行迁移
    if (!runMigrations()) {
        qCWarning(lcDatabase) << "Some migrations failed";
    }
    
    // 启动维护定时器
    m_maintenanceTimer->start();
    
    emit connected();
    qCInfo(lcDatabase) << "Database initialized successfully";
    
    return true;
}

void DatabaseManager::close()
{
    QWriteLocker locker(&m_lock);
    
    if (!m_database.isOpen()) {
        return;
    }
    
    // 停止维护定时器
    m_maintenanceTimer->stop();
    
    // 如果在事务中，回滚
    if (m_inTransaction) {
        rollbackTransaction();
    }
    
    // 关闭数据库
    m_database.close();
    QSqlDatabase::removeDatabase(CONNECTION_NAME);
    
    emit disconnected();
    qCInfo(lcDatabase) << "Database closed";
}

bool DatabaseManager::isOpen() const
{
    QReadLocker locker(&m_lock);
    return m_database.isOpen();
}

QString DatabaseManager::getDatabasePath() const
{
    QReadLocker locker(&m_lock);
    return m_databasePath;
}

QSqlQuery DatabaseManager::executeQuery(const QString &sql, const QVariantList &parameters)
{
    QReadLocker locker(&m_lock);
    
    QElapsedTimer timer;
    timer.start();
    
    QSqlQuery query(m_database);
    query.prepare(sql);
    
    // 绑定参数
    for (int i = 0; i < parameters.size(); ++i) {
        query.bindValue(i, parameters[i]);
    }
    
    bool success = query.exec();
    qint64 executionTime = timer.elapsed();
    
    // 记录日志
    if (m_queryLoggingEnabled) {
        logQuery(sql, parameters, executionTime, success, 
                 success ? QString() : query.lastError().text());
    }
    
    // 更新统计
    updateStats(executionTime, success);
    
    // 检查慢查询
    if (executionTime > SLOW_QUERY_THRESHOLD) {
        QueryLog slowQuery;
        slowQuery.sql = sql;
        slowQuery.parameters = parameters;
        slowQuery.executionTime = executionTime;
        slowQuery.timestamp = QDateTime::currentDateTime();
        slowQuery.success = success;
        slowQuery.error = success ? QString() : query.lastError().text();
        
        emit slowQueryDetected(slowQuery);
        qCWarning(lcDatabase) << "Slow query detected:" << sql << "Time:" << executionTime << "ms";
    }
    
    if (!success) {
        QString errorText = query.lastError().text();
        qCWarning(lcDatabase) << "Query failed:" << sql << "Error:" << errorText;
        emit error(errorText);
    }
    
    return query;
}

bool DatabaseManager::executeNonQuery(const QString &sql, const QVariantList &parameters)
{
    QSqlQuery query = executeQuery(sql, parameters);
    return !query.lastError().isValid();
}

bool DatabaseManager::executeBatch(const QString &sql, const QList<QVariantList> &parametersList)
{
    QWriteLocker locker(&m_lock);
    
    QElapsedTimer timer;
    timer.start();
    
    QSqlQuery query(m_database);
    query.prepare(sql);
    
    bool success = true;
    
    // 开始事务
    bool wasInTransaction = m_inTransaction;
    if (!wasInTransaction) {
        beginTransaction();
    }
    
    try {
        for (const QVariantList &parameters : parametersList) {
            // 清除之前的绑定
            query.finish();
            
            // 绑定新参数
            for (int i = 0; i < parameters.size(); ++i) {
                query.bindValue(i, parameters[i]);
            }
            
            if (!query.exec()) {
                success = false;
                qCWarning(lcDatabase) << "Batch query failed:" << query.lastError().text();
                break;
            }
        }
        
        // 提交或回滚事务
        if (!wasInTransaction) {
            if (success) {
                commitTransaction();
            } else {
                rollbackTransaction();
            }
        }
        
    } catch (...) {
        if (!wasInTransaction) {
            rollbackTransaction();
        }
        success = false;
    }
    
    qint64 executionTime = timer.elapsed();
    
    // 记录日志
    if (m_queryLoggingEnabled) {
        logQuery(QString("BATCH: %1 (%2 items)").arg(sql).arg(parametersList.size()), 
                 QVariantList(), executionTime, success, 
                 success ? QString() : query.lastError().text());
    }
    
    // 更新统计
    updateStats(executionTime, success);
    
    return success;
}

bool DatabaseManager::beginTransaction()
{
    QWriteLocker locker(&m_lock);
    
    if (m_inTransaction) {
        qCWarning(lcDatabase) << "Already in transaction";
        return false;
    }
    
    if (!m_database.transaction()) {
        QString errorText = m_database.lastError().text();
        qCWarning(lcDatabase) << "Failed to begin transaction:" << errorText;
        emit error(errorText);
        return false;
    }
    
    m_inTransaction = true;
    qCDebug(lcDatabase) << "Transaction started";
    return true;
}

bool DatabaseManager::commitTransaction()
{
    QWriteLocker locker(&m_lock);
    
    if (!m_inTransaction) {
        qCWarning(lcDatabase) << "Not in transaction";
        return false;
    }
    
    if (!m_database.commit()) {
        QString errorText = m_database.lastError().text();
        qCWarning(lcDatabase) << "Failed to commit transaction:" << errorText;
        emit error(errorText);
        return false;
    }
    
    m_inTransaction = false;
    qCDebug(lcDatabase) << "Transaction committed";
    return true;
}

bool DatabaseManager::rollbackTransaction()
{
    QWriteLocker locker(&m_lock);
    
    if (!m_inTransaction) {
        qCWarning(lcDatabase) << "Not in transaction";
        return false;
    }
    
    if (!m_database.rollback()) {
        QString errorText = m_database.lastError().text();
        qCWarning(lcDatabase) << "Failed to rollback transaction:" << errorText;
        emit error(errorText);
        return false;
    }
    
    m_inTransaction = false;
    qCDebug(lcDatabase) << "Transaction rolled back";
    return true;
}

bool DatabaseManager::isInTransaction() const
{
    QReadLocker locker(&m_lock);
    return m_inTransaction;
}

bool DatabaseManager::vacuum()
{
    qCInfo(lcDatabase) << "Starting VACUUM operation";
    return executeNonQuery("VACUUM");
}

bool DatabaseManager::analyze()
{
    qCInfo(lcDatabase) << "Starting ANALYZE operation";
    return executeNonQuery("ANALYZE");
}

bool DatabaseManager::checkIntegrity()
{
    QSqlQuery query = executeQuery("PRAGMA integrity_check");

    if (!query.isActive()) {
        return false;
    }

    QStringList errors;
    while (query.next()) {
        QString result = query.value(0).toString();
        if (result != "ok") {
            errors.append(result);
        }
    }

    if (!errors.isEmpty()) {
        qCWarning(lcDatabase) << "Integrity check failed:" << errors;
        return false;
    }

    qCInfo(lcDatabase) << "Integrity check passed";
    return true;
}

QStringList DatabaseManager::getIntegrityErrors()
{
    QStringList errors;
    QSqlQuery query = executeQuery("PRAGMA integrity_check");

    while (query.next()) {
        QString result = query.value(0).toString();
        if (result != "ok") {
            errors.append(result);
        }
    }

    return errors;
}

bool DatabaseManager::backup(const QString &backupPath)
{
    QReadLocker locker(&m_lock);

    if (!m_database.isOpen()) {
        qCWarning(lcDatabase) << "Database not open";
        return false;
    }

    // 创建备份目录
    QFileInfo fileInfo(backupPath);
    QDir().mkpath(fileInfo.absolutePath());

    // 使用SQLite的备份API或简单的文件复制
    // 这里使用文件复制的简化实现
    if (QFile::exists(backupPath)) {
        QFile::remove(backupPath);
    }

    bool success = QFile::copy(m_databasePath, backupPath);

    if (success) {
        qCInfo(lcDatabase) << "Database backed up to:" << backupPath;
    } else {
        qCWarning(lcDatabase) << "Failed to backup database to:" << backupPath;
    }

    return success;
}

bool DatabaseManager::restore(const QString &backupPath)
{
    if (!QFile::exists(backupPath)) {
        qCWarning(lcDatabase) << "Backup file does not exist:" << backupPath;
        return false;
    }

    // 关闭当前数据库
    close();

    // 备份当前数据库
    QString currentBackup = m_databasePath + ".restore_backup";
    if (QFile::exists(m_databasePath)) {
        QFile::copy(m_databasePath, currentBackup);
    }

    // 恢复数据库
    if (QFile::exists(m_databasePath)) {
        QFile::remove(m_databasePath);
    }

    bool success = QFile::copy(backupPath, m_databasePath);

    if (success) {
        // 重新打开数据库
        success = initialize(m_databasePath);

        if (success) {
            qCInfo(lcDatabase) << "Database restored from:" << backupPath;
            // 删除临时备份
            QFile::remove(currentBackup);
        } else {
            // 恢复失败，还原原数据库
            QFile::remove(m_databasePath);
            QFile::copy(currentBackup, m_databasePath);
            QFile::remove(currentBackup);
            initialize(m_databasePath);
        }
    } else {
        qCWarning(lcDatabase) << "Failed to restore database from:" << backupPath;
        // 还原原数据库
        if (QFile::exists(currentBackup)) {
            QFile::copy(currentBackup, m_databasePath);
            QFile::remove(currentBackup);
            initialize(m_databasePath);
        }
    }

    return success;
}

bool DatabaseManager::runMigrations()
{
    qCInfo(lcDatabase) << "Running database migrations";

    bool allSuccess = true;

    for (const Migration &migration : m_migrations) {
        if (!executeMigration(migration)) {
            qCWarning(lcDatabase) << "Migration failed:" << migration.version;
            allSuccess = false;
            break;
        }
    }

    if (allSuccess) {
        qCInfo(lcDatabase) << "All migrations completed successfully";
    }

    return allSuccess;
}

QString DatabaseManager::getCurrentVersion() const
{
    QReadLocker locker(&m_lock);
    return m_currentVersion;
}

QStringList DatabaseManager::getPendingMigrations() const
{
    QStringList pending;

    // 获取当前版本
    QString currentVersion = getCurrentVersion();

    // 查找待执行的迁移
    bool foundCurrent = currentVersion.isEmpty();

    for (const Migration &migration : m_migrations) {
        if (foundCurrent) {
            pending.append(migration.version);
        } else if (migration.version == currentVersion) {
            foundCurrent = true;
        }
    }

    return pending;
}

void DatabaseManager::addMigration(const Migration &migration)
{
    m_migrations.append(migration);

    // 按版本号排序
    std::sort(m_migrations.begin(), m_migrations.end(),
              [](const Migration &a, const Migration &b) {
                  return a.version < b.version;
              });
}

DatabaseStats DatabaseManager::getStats() const
{
    QMutexLocker locker(&m_logMutex);

    DatabaseStats stats = m_stats;
    stats.databasePath = m_databasePath;
    stats.databaseSize = getDatabaseSize();
    stats.connectionCount = 1; // 简化实现

    return stats;
}

void DatabaseManager::enableQueryLogging(bool enabled)
{
    QMutexLocker locker(&m_logMutex);
    m_queryLoggingEnabled = enabled;

    if (!enabled) {
        m_queryLog.clear();
    }

    qCDebug(lcDatabase) << "Query logging" << (enabled ? "enabled" : "disabled");
}

QList<QueryLog> DatabaseManager::getSlowQueries(int thresholdMs) const
{
    QMutexLocker locker(&m_logMutex);

    QList<QueryLog> slowQueries;

    for (const QueryLog &log : m_queryLog) {
        if (log.executionTime >= thresholdMs) {
            slowQueries.append(log);
        }
    }

    return slowQueries;
}

void DatabaseManager::clearQueryLog()
{
    QMutexLocker locker(&m_logMutex);
    m_queryLog.clear();
    qCDebug(lcDatabase) << "Query log cleared";
}

bool DatabaseManager::tableExists(const QString &tableName) const
{
    QSqlQuery query = const_cast<DatabaseManager*>(this)->executeQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        {tableName});

    return query.next();
}

QStringList DatabaseManager::getTableColumns(const QString &tableName) const
{
    QStringList columns;

    QSqlQuery query = const_cast<DatabaseManager*>(this)->executeQuery(
        QString("PRAGMA table_info(%1)").arg(tableName));

    while (query.next()) {
        columns.append(query.value("name").toString());
    }

    return columns;
}

qint64 DatabaseManager::getDatabaseSize() const
{
    QFileInfo fileInfo(m_databasePath);
    return fileInfo.exists() ? fileInfo.size() : 0;
}

qint64 DatabaseManager::getLastInsertId() const
{
    QReadLocker locker(&m_lock);

    QSqlQuery query(m_database);
    query.exec("SELECT last_insert_rowid()");

    if (query.next()) {
        return query.value(0).toLongLong();
    }

    return -1;
}

void DatabaseManager::performMaintenance()
{
    qCDebug(lcDatabase) << "Performing database maintenance";

    // 清理查询日志
    {
        QMutexLocker locker(&m_logMutex);
        if (m_queryLog.size() > MAX_QUERY_LOG_SIZE) {
            int removeCount = m_queryLog.size() - MAX_QUERY_LOG_SIZE / 2;
            m_queryLog.erase(m_queryLog.begin(), m_queryLog.begin() + removeCount);
        }
    }

    // 定期执行ANALYZE
    static int maintenanceCount = 0;
    if (++maintenanceCount % 24 == 0) { // 每24小时执行一次
        analyze();
    }

    qCDebug(lcDatabase) << "Database maintenance completed";
}

bool DatabaseManager::createTables()
{
    qCInfo(lcDatabase) << "Creating database tables";

    // 创建版本表
    if (!executeNonQuery(R"(
        CREATE TABLE IF NOT EXISTS schema_versions (
            version TEXT PRIMARY KEY,
            applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            description TEXT
        )
    )")) {
        return false;
    }

    // 创建启动项表
    if (!executeNonQuery(R"(
        CREATE TABLE IF NOT EXISTS launch_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            path TEXT NOT NULL,
            description TEXT,
            icon_path TEXT,
            type INTEGER DEFAULT 0,
            category_id INTEGER DEFAULT -1,
            arguments TEXT,
            working_directory TEXT,
            run_as_user TEXT,
            run_as_admin BOOLEAN DEFAULT 0,
            run_in_terminal BOOLEAN DEFAULT 0,
            tags TEXT,
            keywords TEXT,
            shortcut TEXT,
            priority INTEGER DEFAULT 0,
            is_enabled BOOLEAN DEFAULT 1,
            is_visible BOOLEAN DEFAULT 1,
            is_pinned BOOLEAN DEFAULT 0,
            use_count INTEGER DEFAULT 0,
            last_used DATETIME,
            first_used DATETIME,
            total_run_time INTEGER DEFAULT 0,
            avg_run_time INTEGER DEFAULT 0,
            rating REAL DEFAULT 0.0,
            file_size INTEGER DEFAULT 0,
            file_modified DATETIME,
            file_version TEXT,
            file_description TEXT,
            file_company TEXT,
            file_copyright TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_by TEXT,
            source TEXT DEFAULT 'manual',
            metadata TEXT,
            settings TEXT
        )
    )")) {
        return false;
    }

    // 创建分类表
    if (!executeNonQuery(R"(
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            icon_path TEXT,
            color TEXT,
            parent_id INTEGER DEFAULT -1,
            sort_order INTEGER DEFAULT 0,
            is_visible BOOLEAN DEFAULT 1,
            is_expanded BOOLEAN DEFAULT 1,
            is_system BOOLEAN DEFAULT 0,
            is_default BOOLEAN DEFAULT 0,
            item_count INTEGER DEFAULT 0,
            total_item_count INTEGER DEFAULT 0,
            last_used DATETIME,
            use_count INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_by TEXT,
            metadata TEXT,
            settings TEXT,
            tags TEXT
        )
    )")) {
        return false;
    }

    // 创建用户行为表
    if (!executeNonQuery(R"(
        CREATE TABLE IF NOT EXISTS user_actions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            action_type TEXT NOT NULL,
            item_id INTEGER,
            item_name TEXT,
            item_path TEXT,
            category_id INTEGER,
            search_query TEXT,
            context_data TEXT,
            response_time INTEGER,
            success BOOLEAN DEFAULT 1,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            session_id TEXT,
            user_agent TEXT,
            ip_address TEXT
        )
    )")) {
        return false;
    }

    // 创建设置表
    if (!executeNonQuery(R"(
        CREATE TABLE IF NOT EXISTS settings (
            key TEXT PRIMARY KEY,
            value TEXT,
            type TEXT DEFAULT 'string',
            category TEXT DEFAULT 'general',
            description TEXT,
            is_user_configurable BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    )")) {
        return false;
    }

    // 创建索引
    createIndexes();

    qCInfo(lcDatabase) << "Database tables created successfully";
    return true;
}

bool DatabaseManager::createIndexes()
{
    qCDebug(lcDatabase) << "Creating database indexes";

    // 启动项表索引
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_launch_items_name ON launch_items(name)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_launch_items_path ON launch_items(path)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_launch_items_category ON launch_items(category_id)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_launch_items_type ON launch_items(type)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_launch_items_enabled ON launch_items(is_enabled)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_launch_items_visible ON launch_items(is_visible)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_launch_items_pinned ON launch_items(is_pinned)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_launch_items_use_count ON launch_items(use_count DESC)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_launch_items_last_used ON launch_items(last_used DESC)");

    // 分类表索引
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories(parent_id)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order)");

    // 用户行为表索引
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_user_actions_type ON user_actions(action_type)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_user_actions_item ON user_actions(item_id)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_user_actions_timestamp ON user_actions(timestamp DESC)");
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_user_actions_session ON user_actions(session_id)");

    // 设置表索引
    executeNonQuery("CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(category)");

    return true;
}

bool DatabaseManager::initializeSettings()
{
    qCDebug(lcDatabase) << "Initializing database settings";

    // 设置SQLite参数
    executeNonQuery("PRAGMA foreign_keys = ON");
    executeNonQuery("PRAGMA journal_mode = WAL");
    executeNonQuery("PRAGMA synchronous = NORMAL");
    executeNonQuery("PRAGMA cache_size = 10000");
    executeNonQuery("PRAGMA temp_store = MEMORY");
    executeNonQuery("PRAGMA mmap_size = 268435456"); // 256MB

    return true;
}

void DatabaseManager::loadMigrations()
{
    qCDebug(lcDatabase) << "Loading database migrations";

    // 这里可以从文件或资源中加载迁移
    // 暂时添加一些示例迁移

    Migration migration1;
    migration1.version = "1.0.0";
    migration1.description = "Initial database schema";
    migration1.upSql = ""; // 已在createTables中处理
    migration1.createdAt = QDateTime::currentDateTime();
    addMigration(migration1);

    Migration migration2;
    migration2.version = "1.0.1";
    migration2.description = "Add indexes for performance";
    migration2.upSql = ""; // 已在createIndexes中处理
    migration2.createdAt = QDateTime::currentDateTime();
    addMigration(migration2);
}

bool DatabaseManager::executeMigration(const Migration &migration)
{
    qCInfo(lcDatabase) << "Executing migration:" << migration.version;

    // 检查是否已经应用
    QSqlQuery checkQuery = executeQuery(
        "SELECT version FROM schema_versions WHERE version = ?",
        {migration.version});

    if (checkQuery.next()) {
        qCDebug(lcDatabase) << "Migration already applied:" << migration.version;
        return true;
    }

    // 执行迁移SQL
    if (!migration.upSql.isEmpty()) {
        if (!executeNonQuery(migration.upSql)) {
            qCWarning(lcDatabase) << "Migration SQL failed:" << migration.version;
            return false;
        }
    }

    // 记录迁移
    bool success = executeNonQuery(
        "INSERT INTO schema_versions (version, description) VALUES (?, ?)",
        {migration.version, migration.description});

    if (success) {
        m_currentVersion = migration.version;
        emit migrationCompleted(migration.version);
        qCInfo(lcDatabase) << "Migration completed:" << migration.version;
    }

    return success;
}

void DatabaseManager::logQuery(const QString &sql, const QVariantList &parameters,
                               qint64 executionTime, bool success, const QString &error)
{
    QMutexLocker locker(&m_logMutex);

    QueryLog log;
    log.sql = sql;
    log.parameters = parameters;
    log.executionTime = executionTime;
    log.timestamp = QDateTime::currentDateTime();
    log.success = success;
    log.error = error;

    m_queryLog.append(log);

    // 限制日志大小
    if (m_queryLog.size() > MAX_QUERY_LOG_SIZE) {
        m_queryLog.removeFirst();
    }
}

void DatabaseManager::updateStats(qint64 executionTime, bool success)
{
    QMutexLocker locker(&m_logMutex);

    m_stats.totalQueries++;
    m_stats.totalExecutionTime += executionTime;
    m_stats.lastQuery = QDateTime::currentDateTime();

    if (success) {
        m_stats.successfulQueries++;
    } else {
        m_stats.failedQueries++;
    }

    if (executionTime > SLOW_QUERY_THRESHOLD) {
        m_stats.slowQueries++;
    }

    // 计算平均执行时间
    if (m_stats.totalQueries > 0) {
        m_stats.avgExecutionTime = static_cast<double>(m_stats.totalExecutionTime) / m_stats.totalQueries;
    }
}
