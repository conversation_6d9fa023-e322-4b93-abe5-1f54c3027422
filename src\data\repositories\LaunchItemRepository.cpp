#include "LaunchItemRepository.h"
#include "infrastructure/database/DatabaseManager.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QVariant>
#include <QJsonDocument>
#include <QJsonObject>
#include <QLoggingCategory>
#include <QMutexLocker>
#include <QtConcurrent>

Q_LOGGING_CATEGORY(lcLaunchItemRepo, "launchitemrepository")

LaunchItemRepository::LaunchItemRepository(std::shared_ptr<DatabaseManager> databaseManager, 
                                           QObject *parent)
    : QObject(parent)
    , m_databaseManager(databaseManager)
{
    qCDebug(lcLaunchItemRepo) << "LaunchItemRepository created";
}

LaunchItemRepository::~LaunchItemRepository()
{
    qCDebug(lcLaunchItemRepo) << "LaunchItemRepository destroyed";
}

bool LaunchItemRepository::addItem(LaunchItem &item)
{
    QMutexLocker locker(&m_mutex);
    
    if (!validateItem(item)) {
        qCWarning(lcLaunchItemRepo) << "Invalid item, cannot add";
        return false;
    }
    
    const QString sql = R"(
        INSERT INTO launch_items (
            name, path, description, icon_path, type, category_id,
            arguments, working_directory, run_as_user, run_as_admin, run_in_terminal,
            tags, keywords, shortcut, priority, is_enabled, is_visible, is_pinned,
            use_count, last_used, first_used, total_run_time, avg_run_time, rating,
            file_size, file_modified, file_version, file_description, file_company, file_copyright,
            created_by, source, metadata, settings
        ) VALUES (
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?
        )
    )";
    
    QSqlQuery query = m_databaseManager->executeQuery(sql, {
        item.name, item.path, item.description, item.iconPath, static_cast<int>(item.type), item.categoryId,
        item.arguments, item.workingDirectory, item.runAsUser, item.runAsAdmin, item.runInTerminal,
        item.tags.join(","), item.keywords.join(","), item.shortcut, item.priority, 
        item.isEnabled, item.isVisible, item.isPinned,
        item.useCount, item.lastUsed, item.firstUsed, item.totalRunTime, item.avgRunTime, item.rating,
        item.fileSize, item.fileModified, item.fileVersion, item.fileDescription, item.fileCompany, item.fileCopyright,
        item.createdBy, item.source, 
        QJsonDocument::fromVariant(item.metadata).toJson(QJsonDocument::Compact),
        QJsonDocument::fromVariant(item.settings).toJson(QJsonDocument::Compact)
    });
    
    if (query.lastError().isValid()) {
        qCWarning(lcLaunchItemRepo) << "Failed to add item:" << query.lastError().text();
        return false;
    }
    
    // 获取生成的ID
    item.id = static_cast<int>(m_databaseManager->getLastInsertId());
    
    emit itemAdded(item);
    qCDebug(lcLaunchItemRepo) << "Item added successfully, ID:" << item.id;
    
    return true;
}

int LaunchItemRepository::addItems(QList<LaunchItem> &items)
{
    if (items.isEmpty()) {
        return 0;
    }
    
    int successCount = 0;
    
    bool success = executeInTransaction([&]() {
        for (LaunchItem &item : items) {
            if (addItem(item)) {
                successCount++;
            } else {
                return false; // 回滚事务
            }
        }
        return true;
    });
    
    if (success) {
        emit batchOperationCompleted("add", successCount);
        qCInfo(lcLaunchItemRepo) << "Batch add completed, success count:" << successCount;
    } else {
        qCWarning(lcLaunchItemRepo) << "Batch add failed, rolling back";
        successCount = 0;
    }
    
    return successCount;
}

bool LaunchItemRepository::updateItem(const LaunchItem &item)
{
    QMutexLocker locker(&m_mutex);
    
    if (item.id <= 0) {
        qCWarning(lcLaunchItemRepo) << "Invalid item ID for update";
        return false;
    }
    
    if (!validateItem(item)) {
        qCWarning(lcLaunchItemRepo) << "Invalid item, cannot update";
        return false;
    }
    
    const QString sql = R"(
        UPDATE launch_items SET
            name = ?, path = ?, description = ?, icon_path = ?, type = ?, category_id = ?,
            arguments = ?, working_directory = ?, run_as_user = ?, run_as_admin = ?, run_in_terminal = ?,
            tags = ?, keywords = ?, shortcut = ?, priority = ?, is_enabled = ?, is_visible = ?, is_pinned = ?,
            use_count = ?, last_used = ?, first_used = ?, total_run_time = ?, avg_run_time = ?, rating = ?,
            file_size = ?, file_modified = ?, file_version = ?, file_description = ?, file_company = ?, file_copyright = ?,
            updated_at = CURRENT_TIMESTAMP, source = ?, metadata = ?, settings = ?
        WHERE id = ?
    )";
    
    bool success = m_databaseManager->executeNonQuery(sql, {
        item.name, item.path, item.description, item.iconPath, static_cast<int>(item.type), item.categoryId,
        item.arguments, item.workingDirectory, item.runAsUser, item.runAsAdmin, item.runInTerminal,
        item.tags.join(","), item.keywords.join(","), item.shortcut, item.priority, 
        item.isEnabled, item.isVisible, item.isPinned,
        item.useCount, item.lastUsed, item.firstUsed, item.totalRunTime, item.avgRunTime, item.rating,
        item.fileSize, item.fileModified, item.fileVersion, item.fileDescription, item.fileCompany, item.fileCopyright,
        item.source,
        QJsonDocument::fromVariant(item.metadata).toJson(QJsonDocument::Compact),
        QJsonDocument::fromVariant(item.settings).toJson(QJsonDocument::Compact),
        item.id
    });
    
    if (success) {
        emit itemUpdated(item);
        qCDebug(lcLaunchItemRepo) << "Item updated successfully, ID:" << item.id;
    } else {
        qCWarning(lcLaunchItemRepo) << "Failed to update item, ID:" << item.id;
    }
    
    return success;
}

int LaunchItemRepository::updateItems(const QList<LaunchItem> &items)
{
    if (items.isEmpty()) {
        return 0;
    }
    
    int successCount = 0;
    
    bool success = executeInTransaction([&]() {
        for (const LaunchItem &item : items) {
            if (updateItem(item)) {
                successCount++;
            } else {
                return false; // 回滚事务
            }
        }
        return true;
    });
    
    if (success) {
        emit batchOperationCompleted("update", successCount);
        qCInfo(lcLaunchItemRepo) << "Batch update completed, success count:" << successCount;
    } else {
        qCWarning(lcLaunchItemRepo) << "Batch update failed, rolling back";
        successCount = 0;
    }
    
    return successCount;
}

bool LaunchItemRepository::removeItem(int id)
{
    QMutexLocker locker(&m_mutex);
    
    if (id <= 0) {
        qCWarning(lcLaunchItemRepo) << "Invalid item ID for removal";
        return false;
    }
    
    const QString sql = "DELETE FROM launch_items WHERE id = ?";
    bool success = m_databaseManager->executeNonQuery(sql, {id});
    
    if (success) {
        emit itemRemoved(id);
        qCDebug(lcLaunchItemRepo) << "Item removed successfully, ID:" << id;
    } else {
        qCWarning(lcLaunchItemRepo) << "Failed to remove item, ID:" << id;
    }
    
    return success;
}

int LaunchItemRepository::removeItems(const QList<int> &ids)
{
    if (ids.isEmpty()) {
        return 0;
    }
    
    int successCount = 0;
    
    bool success = executeInTransaction([&]() {
        for (int id : ids) {
            if (removeItem(id)) {
                successCount++;
            } else {
                return false; // 回滚事务
            }
        }
        return true;
    });
    
    if (success) {
        emit batchOperationCompleted("remove", successCount);
        qCInfo(lcLaunchItemRepo) << "Batch remove completed, success count:" << successCount;
    } else {
        qCWarning(lcLaunchItemRepo) << "Batch remove failed, rolling back";
        successCount = 0;
    }
    
    return successCount;
}

LaunchItem LaunchItemRepository::getItem(int id) const
{
    QMutexLocker locker(&m_mutex);
    
    const QString sql = "SELECT * FROM launch_items WHERE id = ?";
    QSqlQuery query = m_databaseManager->executeQuery(sql, {id});
    
    if (query.next()) {
        return createItemFromQuery(query);
    }
    
    qCDebug(lcLaunchItemRepo) << "Item not found, ID:" << id;
    return LaunchItem(); // 返回无效对象
}

LaunchItem LaunchItemRepository::getItemByPath(const QString &path) const
{
    QMutexLocker locker(&m_mutex);
    
    const QString sql = "SELECT * FROM launch_items WHERE path = ?";
    QSqlQuery query = m_databaseManager->executeQuery(sql, {path});
    
    if (query.next()) {
        return createItemFromQuery(query);
    }
    
    qCDebug(lcLaunchItemRepo) << "Item not found by path:" << path;
    return LaunchItem(); // 返回无效对象
}

QList<LaunchItem> LaunchItemRepository::getAllItems() const
{
    QMutexLocker locker(&m_mutex);
    
    const QString sql = R"(
        SELECT * FROM launch_items 
        WHERE is_visible = 1 
        ORDER BY is_pinned DESC, priority DESC, use_count DESC, name ASC
    )";
    
    QSqlQuery query = m_databaseManager->executeQuery(sql);
    QList<LaunchItem> items;
    
    while (query.next()) {
        items.append(createItemFromQuery(query));
    }
    
    qCDebug(lcLaunchItemRepo) << "Retrieved" << items.size() << "items";
    return items;
}

QList<LaunchItem> LaunchItemRepository::findItems(const LaunchItemQuery &query) const
{
    QMutexLocker locker(&m_mutex);

    auto sqlAndParams = buildQuerySql(query);
    QSqlQuery sqlQuery = m_databaseManager->executeQuery(sqlAndParams.first, sqlAndParams.second);

    QList<LaunchItem> items;
    while (sqlQuery.next()) {
        items.append(createItemFromQuery(sqlQuery));
    }

    qCDebug(lcLaunchItemRepo) << "Found" << items.size() << "items with query";
    return items;
}

QList<LaunchItem> LaunchItemRepository::getItemsByCategory(int categoryId, bool includeSubcategories) const
{
    QMutexLocker locker(&m_mutex);

    QString sql;
    QVariantList params;

    if (includeSubcategories) {
        // 递归查询子分类（简化实现，实际可能需要CTE）
        sql = R"(
            SELECT * FROM launch_items
            WHERE category_id = ? OR category_id IN (
                SELECT id FROM categories WHERE parent_id = ?
            )
            AND is_visible = 1
            ORDER BY is_pinned DESC, priority DESC, name ASC
        )";
        params = {categoryId, categoryId};
    } else {
        sql = R"(
            SELECT * FROM launch_items
            WHERE category_id = ? AND is_visible = 1
            ORDER BY is_pinned DESC, priority DESC, name ASC
        )";
        params = {categoryId};
    }

    QSqlQuery query = m_databaseManager->executeQuery(sql, params);
    QList<LaunchItem> items;

    while (query.next()) {
        items.append(createItemFromQuery(query));
    }

    qCDebug(lcLaunchItemRepo) << "Retrieved" << items.size() << "items for category" << categoryId;
    return items;
}

QList<LaunchItem> LaunchItemRepository::getItemsByType(LaunchItemType type) const
{
    QMutexLocker locker(&m_mutex);

    const QString sql = R"(
        SELECT * FROM launch_items
        WHERE type = ? AND is_visible = 1
        ORDER BY is_pinned DESC, priority DESC, use_count DESC, name ASC
    )";

    QSqlQuery query = m_databaseManager->executeQuery(sql, {static_cast<int>(type)});
    QList<LaunchItem> items;

    while (query.next()) {
        items.append(createItemFromQuery(query));
    }

    qCDebug(lcLaunchItemRepo) << "Retrieved" << items.size() << "items for type" << static_cast<int>(type);
    return items;
}

QList<LaunchItem> LaunchItemRepository::searchItems(const QString &searchText, bool fuzzy, int limit) const
{
    QMutexLocker locker(&m_mutex);

    if (searchText.isEmpty()) {
        return getAllItems();
    }

    QString sql;
    QVariantList params;

    if (fuzzy) {
        // 模糊搜索，使用LIKE和权重排序
        sql = R"(
            SELECT *,
                   CASE
                       WHEN name LIKE ? THEN 100
                       WHEN name LIKE ? THEN 90
                       WHEN path LIKE ? THEN 70
                       WHEN description LIKE ? THEN 60
                       WHEN tags LIKE ? THEN 50
                       WHEN keywords LIKE ? THEN 50
                       ELSE 0
                   END as relevance_score
            FROM launch_items
            WHERE (name LIKE ? OR path LIKE ? OR description LIKE ? OR tags LIKE ? OR keywords LIKE ?)
              AND is_visible = 1 AND is_enabled = 1
            ORDER BY relevance_score DESC, is_pinned DESC, use_count DESC, name ASC
            LIMIT ?
        )";

        QString exactMatch = searchText;
        QString prefixMatch = searchText + "%";
        QString containsMatch = "%" + searchText + "%";

        params = {
            exactMatch, prefixMatch, containsMatch, containsMatch, containsMatch, containsMatch,
            containsMatch, containsMatch, containsMatch, containsMatch, containsMatch,
            limit
        };
    } else {
        // 精确搜索
        sql = R"(
            SELECT * FROM launch_items
            WHERE (name = ? OR path = ? OR description = ?)
              AND is_visible = 1 AND is_enabled = 1
            ORDER BY is_pinned DESC, use_count DESC, name ASC
            LIMIT ?
        )";
        params = {searchText, searchText, searchText, limit};
    }

    QSqlQuery query = m_databaseManager->executeQuery(sql, params);
    QList<LaunchItem> items;

    while (query.next()) {
        LaunchItem item = createItemFromQuery(query);

        // 如果是模糊搜索，计算相关性分数
        if (fuzzy) {
            item.metadata["relevance_score"] = query.value("relevance_score").toDouble();
        }

        items.append(item);
    }

    qCDebug(lcLaunchItemRepo) << "Search found" << items.size() << "items for:" << searchText;
    return items;
}

QList<LaunchItem> LaunchItemRepository::getRecentlyUsedItems(int limit) const
{
    QMutexLocker locker(&m_mutex);

    const QString sql = R"(
        SELECT * FROM launch_items
        WHERE last_used IS NOT NULL AND is_visible = 1 AND is_enabled = 1
        ORDER BY last_used DESC, use_count DESC
        LIMIT ?
    )";

    QSqlQuery query = m_databaseManager->executeQuery(sql, {limit});
    QList<LaunchItem> items;

    while (query.next()) {
        items.append(createItemFromQuery(query));
    }

    qCDebug(lcLaunchItemRepo) << "Retrieved" << items.size() << "recently used items";
    return items;
}

QList<LaunchItem> LaunchItemRepository::getMostUsedItems(int limit) const
{
    QMutexLocker locker(&m_mutex);

    const QString sql = R"(
        SELECT * FROM launch_items
        WHERE use_count > 0 AND is_visible = 1 AND is_enabled = 1
        ORDER BY use_count DESC, last_used DESC
        LIMIT ?
    )";

    QSqlQuery query = m_databaseManager->executeQuery(sql, {limit});
    QList<LaunchItem> items;

    while (query.next()) {
        items.append(createItemFromQuery(query));
    }

    qCDebug(lcLaunchItemRepo) << "Retrieved" << items.size() << "most used items";
    return items;
}

QList<LaunchItem> LaunchItemRepository::getPinnedItems() const
{
    QMutexLocker locker(&m_mutex);

    const QString sql = R"(
        SELECT * FROM launch_items
        WHERE is_pinned = 1 AND is_visible = 1 AND is_enabled = 1
        ORDER BY priority DESC, name ASC
    )";

    QSqlQuery query = m_databaseManager->executeQuery(sql);
    QList<LaunchItem> items;

    while (query.next()) {
        items.append(createItemFromQuery(query));
    }

    qCDebug(lcLaunchItemRepo) << "Retrieved" << items.size() << "pinned items";
    return items;
}

QList<LaunchItem> LaunchItemRepository::getTopRatedItems(int limit) const
{
    QMutexLocker locker(&m_mutex);

    const QString sql = R"(
        SELECT * FROM launch_items
        WHERE rating > 0 AND is_visible = 1 AND is_enabled = 1
        ORDER BY rating DESC, use_count DESC
        LIMIT ?
    )";

    QSqlQuery query = m_databaseManager->executeQuery(sql, {limit});
    QList<LaunchItem> items;

    while (query.next()) {
        items.append(createItemFromQuery(query));
    }

    qCDebug(lcLaunchItemRepo) << "Retrieved" << items.size() << "top rated items";
    return items;
}

LaunchItemStats LaunchItemRepository::getStatistics() const
{
    QMutexLocker locker(&m_mutex);

    LaunchItemStats stats;

    // 基本统计
    QSqlQuery query = m_databaseManager->executeQuery(R"(
        SELECT
            COUNT(*) as total_items,
            COUNT(CASE WHEN is_enabled = 1 THEN 1 END) as enabled_items,
            COUNT(CASE WHEN is_pinned = 1 THEN 1 END) as pinned_items,
            AVG(rating) as avg_rating,
            SUM(use_count) as total_use_count,
            MAX(updated_at) as last_modified
        FROM launch_items
    )");

    if (query.next()) {
        stats.totalItems = query.value("total_items").toInt();
        stats.enabledItems = query.value("enabled_items").toInt();
        stats.pinnedItems = query.value("pinned_items").toInt();
        stats.avgRating = query.value("avg_rating").toDouble();
        stats.totalUseCount = query.value("total_use_count").toInt();
        stats.lastModified = query.value("last_modified").toDateTime();
    }

    // 按类型统计
    QSqlQuery typeQuery = m_databaseManager->executeQuery(R"(
        SELECT type, COUNT(*) as count
        FROM launch_items
        GROUP BY type
    )");

    while (typeQuery.next()) {
        LaunchItemType type = static_cast<LaunchItemType>(typeQuery.value("type").toInt());
        int count = typeQuery.value("count").toInt();
        stats.typeCount[type] = count;
    }

    // 按分类统计
    QSqlQuery categoryQuery = m_databaseManager->executeQuery(R"(
        SELECT category_id, COUNT(*) as count
        FROM launch_items
        WHERE category_id != -1
        GROUP BY category_id
    )");

    while (categoryQuery.next()) {
        int categoryId = categoryQuery.value("category_id").toInt();
        int count = categoryQuery.value("count").toInt();
        stats.categoryCount[categoryId] = count;
    }

    qCDebug(lcLaunchItemRepo) << "Statistics calculated - Total items:" << stats.totalItems;
    return stats;
}

int LaunchItemRepository::getItemCount() const
{
    QMutexLocker locker(&m_mutex);

    QSqlQuery query = m_databaseManager->executeQuery("SELECT COUNT(*) FROM launch_items");

    if (query.next()) {
        return query.value(0).toInt();
    }

    return 0;
}

int LaunchItemRepository::getItemCountByCategory(int categoryId) const
{
    QMutexLocker locker(&m_mutex);

    QSqlQuery query = m_databaseManager->executeQuery(
        "SELECT COUNT(*) FROM launch_items WHERE category_id = ?", {categoryId});

    if (query.next()) {
        return query.value(0).toInt();
    }

    return 0;
}

int LaunchItemRepository::getItemCountByType(LaunchItemType type) const
{
    QMutexLocker locker(&m_mutex);

    QSqlQuery query = m_databaseManager->executeQuery(
        "SELECT COUNT(*) FROM launch_items WHERE type = ?", {static_cast<int>(type)});

    if (query.next()) {
        return query.value(0).toInt();
    }

    return 0;
}

QList<int> LaunchItemRepository::validateItems() const
{
    QMutexLocker locker(&m_mutex);

    QList<int> invalidIds;

    QSqlQuery query = m_databaseManager->executeQuery(R"(
        SELECT id, path, type FROM launch_items
        WHERE is_enabled = 1
    )");

    while (query.next()) {
        int id = query.value("id").toInt();
        QString path = query.value("path").toString();
        LaunchItemType type = static_cast<LaunchItemType>(query.value("type").toInt());

        // 验证文件是否存在
        bool isValid = false;

        switch (type) {
        case LaunchItemType::Application:
        case LaunchItemType::Document:
        case LaunchItemType::Script:
            isValid = QFileInfo::exists(path);
            break;
        case LaunchItemType::Folder:
            isValid = QDir(path).exists();
            break;
        case LaunchItemType::Url:
            isValid = QUrl(path).isValid();
            break;
        case LaunchItemType::Command:
            isValid = !path.isEmpty(); // 命令行只需要非空
            break;
        }

        if (!isValid) {
            invalidIds.append(id);
        }
    }

    qCDebug(lcLaunchItemRepo) << "Validation found" << invalidIds.size() << "invalid items";
    return invalidIds;
}

int LaunchItemRepository::cleanupInvalidItems()
{
    QList<int> invalidIds = validateItems();

    if (invalidIds.isEmpty()) {
        return 0;
    }

    // 批量删除无效项目
    int cleanedCount = removeItems(invalidIds);

    qCInfo(lcLaunchItemRepo) << "Cleaned up" << cleanedCount << "invalid items";
    return cleanedCount;
}

bool LaunchItemRepository::executeInTransaction(std::function<bool()> operation)
{
    if (!m_databaseManager->beginTransaction()) {
        return false;
    }

    try {
        bool success = operation();

        if (success) {
            return m_databaseManager->commitTransaction();
        } else {
            m_databaseManager->rollbackTransaction();
            return false;
        }
    } catch (...) {
        m_databaseManager->rollbackTransaction();
        throw;
    }
}

LaunchItem LaunchItemRepository::createItemFromQuery(const QSqlQuery &query) const
{
    LaunchItem item;

    // 基本属性
    item.id = query.value("id").toInt();
    item.name = query.value("name").toString();
    item.path = query.value("path").toString();
    item.description = query.value("description").toString();
    item.iconPath = query.value("icon_path").toString();
    item.type = static_cast<LaunchItemType>(query.value("type").toInt());
    item.categoryId = query.value("category_id").toInt();

    // 启动参数
    item.arguments = query.value("arguments").toString();
    item.workingDirectory = query.value("working_directory").toString();
    item.runAsUser = query.value("run_as_user").toString();
    item.runAsAdmin = query.value("run_as_admin").toBool();
    item.runInTerminal = query.value("run_in_terminal").toBool();

    // 显示属性
    QString tagsStr = query.value("tags").toString();
    if (!tagsStr.isEmpty()) {
        item.tags = tagsStr.split(",", Qt::SkipEmptyParts);
    }

    QString keywordsStr = query.value("keywords").toString();
    if (!keywordsStr.isEmpty()) {
        item.keywords = keywordsStr.split(",", Qt::SkipEmptyParts);
    }

    item.shortcut = query.value("shortcut").toString();
    item.priority = query.value("priority").toInt();
    item.isEnabled = query.value("is_enabled").toBool();
    item.isVisible = query.value("is_visible").toBool();
    item.isPinned = query.value("is_pinned").toBool();

    // 统计信息
    item.useCount = query.value("use_count").toInt();
    item.lastUsed = query.value("last_used").toDateTime();
    item.firstUsed = query.value("first_used").toDateTime();
    item.totalRunTime = query.value("total_run_time").toInt();
    item.avgRunTime = query.value("avg_run_time").toInt();
    item.rating = query.value("rating").toDouble();

    // 文件信息
    item.fileSize = query.value("file_size").toLongLong();
    item.fileModified = query.value("file_modified").toDateTime();
    item.fileVersion = query.value("file_version").toString();
    item.fileDescription = query.value("file_description").toString();
    item.fileCompany = query.value("file_company").toString();
    item.fileCopyright = query.value("file_copyright").toString();

    // 系统信息
    item.createdAt = query.value("created_at").toDateTime();
    item.updatedAt = query.value("updated_at").toDateTime();
    item.createdBy = query.value("created_by").toString();
    item.source = query.value("source").toString();

    // 扩展属性
    QString metadataJson = query.value("metadata").toString();
    if (!metadataJson.isEmpty()) {
        QJsonDocument metadataDoc = QJsonDocument::fromJson(metadataJson.toUtf8());
        item.metadata = metadataDoc.object().toVariantMap();
    }

    QString settingsJson = query.value("settings").toString();
    if (!settingsJson.isEmpty()) {
        QJsonDocument settingsDoc = QJsonDocument::fromJson(settingsJson.toUtf8());
        item.settings = settingsDoc.object().toVariantMap();
    }

    return item;
}

QPair<QString, QVariantList> LaunchItemRepository::buildQuerySql(const LaunchItemQuery &query) const
{
    QString sql = "SELECT * FROM launch_items WHERE 1=1";
    QVariantList params;

    // 名称过滤
    if (!query.nameFilter.isEmpty()) {
        sql += " AND name LIKE ?";
        params.append("%" + query.nameFilter + "%");
    }

    // 路径过滤
    if (!query.pathFilter.isEmpty()) {
        sql += " AND path LIKE ?";
        params.append("%" + query.pathFilter + "%");
    }

    // 类型过滤
    if (!query.typeFilter.isEmpty()) {
        QStringList typePlaceholders;
        for (LaunchItemType type : query.typeFilter) {
            typePlaceholders.append("?");
            params.append(static_cast<int>(type));
        }
        sql += " AND type IN (" + typePlaceholders.join(",") + ")";
    }

    // 分类过滤
    if (!query.categoryFilter.isEmpty()) {
        QStringList categoryPlaceholders;
        for (int categoryId : query.categoryFilter) {
            categoryPlaceholders.append("?");
            params.append(categoryId);
        }
        sql += " AND category_id IN (" + categoryPlaceholders.join(",") + ")";
    }

    // 启用状态过滤
    if (query.enabledOnly) {
        sql += " AND is_enabled = 1";
    }

    // 可见性过滤
    if (query.visibleOnly) {
        sql += " AND is_visible = 1";
    }

    // 置顶过滤
    if (query.pinnedOnly) {
        sql += " AND is_pinned = 1";
    }

    // 修改时间过滤
    if (query.modifiedSince.isValid()) {
        sql += " AND updated_at >= ?";
        params.append(query.modifiedSince);
    }

    // 标签过滤
    if (!query.tags.isEmpty()) {
        for (const QString &tag : query.tags) {
            sql += " AND tags LIKE ?";
            params.append("%" + tag + "%");
        }
    }

    // 评分过滤
    if (query.minRating > 0) {
        sql += " AND rating >= ?";
        params.append(query.minRating);
    }

    // 使用次数过滤
    if (query.minUseCount > 0) {
        sql += " AND use_count >= ?";
        params.append(query.minUseCount);
    }

    // 排序
    sql += " ORDER BY ";
    if (query.sortBy == "name") {
        sql += "name";
    } else if (query.sortBy == "use_count") {
        sql += "use_count";
    } else if (query.sortBy == "last_used") {
        sql += "last_used";
    } else if (query.sortBy == "rating") {
        sql += "rating";
    } else if (query.sortBy == "created_at") {
        sql += "created_at";
    } else {
        sql += "name"; // 默认排序
    }

    sql += query.sortAscending ? " ASC" : " DESC";

    // 分页
    if (query.limit > 0) {
        sql += " LIMIT ?";
        params.append(query.limit);

        if (query.offset > 0) {
            sql += " OFFSET ?";
            params.append(query.offset);
        }
    }

    return qMakePair(sql, params);
}

bool LaunchItemRepository::validateItem(const LaunchItem &item) const
{
    // 基本验证
    if (item.name.isEmpty() || item.path.isEmpty()) {
        return false;
    }

    // 路径长度检查
    if (item.path.length() > 1000) {
        return false;
    }

    // 名称长度检查
    if (item.name.length() > 255) {
        return false;
    }

    // 类型验证
    if (static_cast<int>(item.type) < 0 || static_cast<int>(item.type) > 5) {
        return false;
    }

    // 评分范围检查
    if (item.rating < 0.0 || item.rating > 5.0) {
        return false;
    }

    // 优先级范围检查
    if (item.priority < -1000 || item.priority > 1000) {
        return false;
    }

    return true;
}
