#pragma once

#include <QObject>
#include <QSettings>
#include <QVariant>
#include <QTimer>
#include <QReadWriteLock>
#include <QFileSystemWatcher>
#include <memory>

/**
 * @brief 配置项信息
 */
struct ConfigItem {
    QString key;                    // 配置键
    QVariant value;                 // 配置值
    QVariant defaultValue;          // 默认值
    QString type;                   // 数据类型
    QString category;               // 分类
    QString description;            // 描述
    bool userConfigurable = true;   // 用户是否可配置
    bool requiresRestart = false;   // 是否需要重启
    QVariantList validValues;       // 有效值列表（用于枚举类型）
    QVariant minValue;              // 最小值
    QVariant maxValue;              // 最大值
};

/**
 * @brief 配置管理器
 * 
 * 负责应用程序配置的管理，包括：
 * - 配置的读取和写入
 * - 配置验证和类型转换
 * - 配置变更通知
 * - 配置文件监控
 * - 默认配置管理
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit ConfigManager(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~ConfigManager();

    /**
     * @brief 初始化配置管理器
     * @param configFile 配置文件路径（可选）
     * @return 是否成功
     */
    bool initialize(const QString &configFile = QString());

    // 基本配置操作
    
    /**
     * @brief 获取配置值
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    QVariant getValue(const QString &key, const QVariant &defaultValue = QVariant()) const;
    
    /**
     * @brief 设置配置值
     * @param key 配置键
     * @param value 配置值
     * @return 是否成功
     */
    bool setValue(const QString &key, const QVariant &value);
    
    /**
     * @brief 检查配置键是否存在
     * @param key 配置键
     * @return 是否存在
     */
    bool contains(const QString &key) const;
    
    /**
     * @brief 移除配置项
     * @param key 配置键
     * @return 是否成功
     */
    bool removeValue(const QString &key);
    
    /**
     * @brief 获取所有配置键
     * @return 配置键列表
     */
    QStringList getAllKeys() const;
    
    /**
     * @brief 根据分类获取配置键
     * @param category 分类名称
     * @return 配置键列表
     */
    QStringList getKeysByCategory(const QString &category) const;

    // 类型安全的配置访问
    
    /**
     * @brief 获取字符串配置
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 字符串值
     */
    QString getString(const QString &key, const QString &defaultValue = QString()) const;
    
    /**
     * @brief 获取整数配置
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 整数值
     */
    int getInt(const QString &key, int defaultValue = 0) const;
    
    /**
     * @brief 获取布尔配置
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 布尔值
     */
    bool getBool(const QString &key, bool defaultValue = false) const;
    
    /**
     * @brief 获取双精度配置
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 双精度值
     */
    double getDouble(const QString &key, double defaultValue = 0.0) const;
    
    /**
     * @brief 获取字符串列表配置
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 字符串列表
     */
    QStringList getStringList(const QString &key, const QStringList &defaultValue = QStringList()) const;

    // 配置项管理
    
    /**
     * @brief 注册配置项
     * @param item 配置项信息
     */
    void registerConfigItem(const ConfigItem &item);
    
    /**
     * @brief 获取配置项信息
     * @param key 配置键
     * @return 配置项信息
     */
    ConfigItem getConfigItem(const QString &key) const;
    
    /**
     * @brief 获取所有配置项
     * @return 配置项列表
     */
    QList<ConfigItem> getAllConfigItems() const;
    
    /**
     * @brief 根据分类获取配置项
     * @param category 分类名称
     * @return 配置项列表
     */
    QList<ConfigItem> getConfigItemsByCategory(const QString &category) const;
    
    /**
     * @brief 验证配置值
     * @param key 配置键
     * @param value 配置值
     * @return 是否有效
     */
    bool validateValue(const QString &key, const QVariant &value) const;

    // 配置文件操作
    
    /**
     * @brief 同步配置到文件
     */
    void sync();
    
    /**
     * @brief 重新加载配置
     * @return 是否成功
     */
    bool reload();
    
    /**
     * @brief 重置为默认配置
     * @param category 分类名称，空表示重置所有
     */
    void resetToDefaults(const QString &category = QString());
    
    /**
     * @brief 导出配置
     * @param filePath 文件路径
     * @param format 格式（json/ini/xml）
     * @return 是否成功
     */
    bool exportConfig(const QString &filePath, const QString &format = "json") const;
    
    /**
     * @brief 导入配置
     * @param filePath 文件路径
     * @param format 格式（json/ini/xml）
     * @param merge 是否合并
     * @return 是否成功
     */
    bool importConfig(const QString &filePath, const QString &format = "json", bool merge = true);

    // 配置监控
    
    /**
     * @brief 启用文件监控
     * @param enabled 是否启用
     */
    void setFileWatchingEnabled(bool enabled);
    
    /**
     * @brief 检查文件监控是否启用
     * @return 是否启用
     */
    bool isFileWatchingEnabled() const;
    
    /**
     * @brief 设置自动同步间隔
     * @param intervalMs 间隔时间（毫秒）
     */
    void setAutoSyncInterval(int intervalMs);

    // 便利方法
    
    /**
     * @brief 批量设置配置
     * @param values 配置键值对
     * @return 成功设置的数量
     */
    int setValues(const QVariantMap &values);
    
    /**
     * @brief 批量获取配置
     * @param keys 配置键列表
     * @return 配置键值对
     */
    QVariantMap getValues(const QStringList &keys) const;

signals:
    /**
     * @brief 配置值变更信号
     * @param key 配置键
     * @param value 新值
     * @param oldValue 旧值
     */
    void valueChanged(const QString &key, const QVariant &value, const QVariant &oldValue);
    
    /**
     * @brief 配置文件变更信号
     */
    void configFileChanged();
    
    /**
     * @brief 配置重新加载信号
     */
    void configReloaded();
    
    /**
     * @brief 配置重置信号
     * @param category 分类名称
     */
    void configReset(const QString &category);

private slots:
    /**
     * @brief 配置文件变更处理
     * @param path 文件路径
     */
    void onConfigFileChanged(const QString &path);
    
    /**
     * @brief 自动同步定时器超时
     */
    void onAutoSyncTimeout();

private:
    /**
     * @brief 加载默认配置
     */
    void loadDefaultConfig();
    
    /**
     * @brief 验证配置项
     * @param item 配置项
     * @return 是否有效
     */
    bool validateConfigItem(const ConfigItem &item) const;
    
    /**
     * @brief 转换配置值类型
     * @param value 原始值
     * @param targetType 目标类型
     * @return 转换后的值
     */
    QVariant convertValue(const QVariant &value, const QString &targetType) const;
    
    /**
     * @brief 获取配置文件路径
     * @return 文件路径
     */
    QString getConfigFilePath() const;
    
    /**
     * @brief 创建备份
     * @return 备份文件路径
     */
    QString createBackup() const;

private:
    std::unique_ptr<QSettings> m_settings;           // 配置存储
    QHash<QString, ConfigItem> m_configItems;       // 配置项定义
    std::unique_ptr<QFileSystemWatcher> m_fileWatcher; // 文件监控
    QTimer *m_autoSyncTimer;                         // 自动同步定时器
    
    mutable QReadWriteLock m_lock;                   // 线程安全锁
    bool m_fileWatchingEnabled = true;               // 文件监控开关
    bool m_autoSyncEnabled = true;                   // 自动同步开关
    int m_autoSyncInterval = 5000;                   // 自动同步间隔（毫秒）
    QString m_configFilePath;                        // 配置文件路径
    
    // 默认配置分类
    static const QString CATEGORY_GENERAL;
    static const QString CATEGORY_UI;
    static const QString CATEGORY_BEHAVIOR;
    static const QString CATEGORY_PERFORMANCE;
    static const QString CATEGORY_ADVANCED;
};
