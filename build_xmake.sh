#!/bin/bash

# KK QuickLaunch XMake 构建脚本
# 支持 Linux 和 macOS 平台的自动化构建

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示标题
echo
echo "========================================"
echo "   KK QuickLaunch XMake Build Script"
echo "========================================"
echo

# 检查xmake是否安装
if ! command -v xmake &> /dev/null; then
    log_error "XMake not found! Please install xmake first."
    echo "Install with: curl -fsSL https://xmake.io/shget.text | bash"
    exit 1
fi

# 显示xmake版本
log_info "XMake version:"
xmake --version
echo

# 默认参数
BUILD_MODE="release"
BUILD_TARGET="kkquicklaunch"
RUN_TESTS=false
CLEAN_BUILD=false
PACKAGE_BUILD=false
INSTALL_BUILD=false
SHOW_HELP=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        debug)
            BUILD_MODE="debug"
            shift
            ;;
        release)
            BUILD_MODE="release"
            shift
            ;;
        test|tests)
            RUN_TESTS=true
            shift
            ;;
        clean)
            CLEAN_BUILD=true
            shift
            ;;
        package)
            PACKAGE_BUILD=true
            shift
            ;;
        install)
            INSTALL_BUILD=true
            shift
            ;;
        help|-h|--help)
            SHOW_HELP=true
            shift
            ;;
        *)
            log_warning "Unknown option: $1"
            shift
            ;;
    esac
done

# 显示帮助信息
if [ "$SHOW_HELP" = true ]; then
    echo
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  debug       Build in debug mode"
    echo "  release     Build in release mode (default)"
    echo "  test        Build and run tests"
    echo "  clean       Clean previous build"
    echo "  package     Create distribution package"
    echo "  install     Install to system"
    echo "  help        Show this help message"
    echo
    echo "Examples:"
    echo "  $0                    # Release build"
    echo "  $0 debug              # Debug build"
    echo "  $0 release test       # Release build with tests"
    echo "  $0 clean release      # Clean and release build"
    echo "  $0 release package    # Release build and package"
    echo
    exit 0
fi

# 显示构建配置
log_info "Build Configuration:"
echo "  Mode: $BUILD_MODE"
echo "  Target: $BUILD_TARGET"
echo "  Run Tests: $RUN_TESTS"
echo "  Clean Build: $CLEAN_BUILD"
echo "  Package: $PACKAGE_BUILD"
echo "  Install: $INSTALL_BUILD"
echo

# 检测平台
PLATFORM=$(uname -s)
ARCH=$(uname -m)

case $PLATFORM in
    Linux*)
        PLATFORM_NAME="linux"
        ;;
    Darwin*)
        PLATFORM_NAME="macosx"
        ;;
    *)
        log_error "Unsupported platform: $PLATFORM"
        exit 1
        ;;
esac

log_info "Platform: $PLATFORM_NAME ($ARCH)"
echo

# 清理构建（如果需要）
if [ "$CLEAN_BUILD" = true ]; then
    log_info "Cleaning previous build..."
    xmake clean-all
    log_success "Clean completed."
    echo
fi

# 配置构建
log_info "Configuring build..."
xmake config -m $BUILD_MODE -p $PLATFORM_NAME -a $ARCH
log_success "Configuration completed."
echo

# 构建主程序
log_info "Building $BUILD_TARGET..."
xmake build $BUILD_TARGET
log_success "Build completed."
echo

# 运行测试（如果需要）
if [ "$RUN_TESTS" = true ]; then
    log_info "Building and running tests..."
    
    if xmake build kkquicklaunch_tests; then
        log_info "Running tests..."
        if xmake run kkquicklaunch_tests; then
            log_success "All tests passed."
        else
            log_warning "Some tests failed!"
        fi
    else
        log_error "Test build failed!"
        exit 1
    fi
    echo
fi

# 打包（如果需要）
if [ "$PACKAGE_BUILD" = true ]; then
    log_info "Creating package..."
    
    # 根据平台选择打包格式
    if [ "$PLATFORM_NAME" = "linux" ]; then
        PACKAGE_FORMAT="tar.gz"
    else
        PACKAGE_FORMAT="zip"
    fi
    
    xmake package -f $PACKAGE_FORMAT
    log_success "Package created."
    echo
fi

# 安装（如果需要）
if [ "$INSTALL_BUILD" = true ]; then
    log_info "Installing application..."
    
    # 检查是否需要sudo权限
    if [ -w "/usr/local" ]; then
        xmake install
    else
        log_info "Installing with sudo privileges..."
        sudo xmake install
    fi
    
    log_success "Installation completed."
    echo
fi

# 显示构建结果
log_info "Build artifacts:"
xmake show -t $BUILD_TARGET | sed 's/^/  /'
echo

# 显示构建统计
log_info "Build Statistics:"
echo "  Build Mode: $BUILD_MODE"
echo "  Platform: $PLATFORM_NAME $ARCH"
echo "  Compiler: GCC/Clang"
echo "  Qt Version: 6.x"
echo "  Build Time: $(date)"
echo

log_success "Build process completed successfully!"
echo

# 询问是否运行程序
if [ "$RUN_TESTS" = false ]; then
    read -p "Do you want to run the application? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Starting KK QuickLaunch..."
        xmake run $BUILD_TARGET
    fi
fi
