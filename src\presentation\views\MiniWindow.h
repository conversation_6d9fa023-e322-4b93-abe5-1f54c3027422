#pragma once

#include <QWidget>
#include <QLineEdit>
#include <QListWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsDropShadowEffect>
#include <QSystemTrayIcon>
#include <memory>

#include "business/entities/LaunchItem.h"

// 前置声明
class LaunchItemViewModel;
class DisplayModeManager;
class ConfigManager;

/**
 * @brief 迷你窗口
 * 
 * 提供紧凑的启动界面，包括：
 * - 简洁的搜索框
 * - 精简的结果列表
 * - 快速启动功能
 * - 自动隐藏机制
 * - 无边框设计
 */
class MiniWindow : public QWidget
{
    Q_OBJECT
    
    Q_PROPERTY(bool autoHide READ autoHide WRITE setAutoHide NOTIFY autoHideChanged)
    Q_PROPERTY(int maxResults READ maxResults WRITE setMaxResults NOTIFY maxResultsChanged)
    Q_PROPERTY(bool alwaysOnTop READ alwaysOnTop WRITE setAlwaysOnTop NOTIFY alwaysOnTopChanged)

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit MiniWindow(QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~MiniWindow();

    // 属性访问器
    bool autoHide() const;
    void setAutoHide(bool enabled);
    
    int maxResults() const;
    void setMaxResults(int count);
    
    bool alwaysOnTop() const;
    void setAlwaysOnTop(bool enabled);

    // 公共方法
    
    /**
     * @brief 显示并激活窗口
     */
    Q_INVOKABLE void showAndActivate();
    
    /**
     * @brief 隐藏窗口
     */
    Q_INVOKABLE void hideWindow();
    
    /**
     * @brief 切换可见性
     */
    Q_INVOKABLE void toggleVisibility();
    
    /**
     * @brief 聚焦搜索框
     */
    Q_INVOKABLE void focusSearchBox();
    
    /**
     * @brief 清空搜索
     */
    Q_INVOKABLE void clearSearch();
    
    /**
     * @brief 启动选中项目
     */
    Q_INVOKABLE void launchSelectedItem();
    
    /**
     * @brief 设置搜索文本
     * @param text 搜索文本
     */
    Q_INVOKABLE void setSearchText(const QString &text);
    
    /**
     * @brief 获取搜索文本
     * @return 搜索文本
     */
    QString getSearchText() const;

signals:
    /**
     * @brief 自动隐藏设置变更信号
     * @param enabled 是否启用
     */
    void autoHideChanged(bool enabled);
    
    /**
     * @brief 最大结果数变更信号
     * @param count 结果数
     */
    void maxResultsChanged(int count);
    
    /**
     * @brief 置顶设置变更信号
     * @param enabled 是否置顶
     */
    void alwaysOnTopChanged(bool enabled);
    
    /**
     * @brief 窗口显示状态变更信号
     * @param visible 是否可见
     */
    void visibilityChanged(bool visible);
    
    /**
     * @brief 项目启动信号
     * @param itemId 项目ID
     * @param success 是否成功
     */
    void itemLaunched(int itemId, bool success);
    
    /**
     * @brief 搜索文本变更信号
     * @param text 搜索文本
     */
    void searchTextChanged(const QString &text);
    
    /**
     * @brief 请求切换到完整模式信号
     */
    void switchToFullModeRequested();

protected:
    // 事件处理
    void showEvent(QShowEvent *event) override;
    void hideEvent(QHideEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void focusOutEvent(QFocusEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void enterEvent(QEnterEvent *event) override;
    void leaveEvent(QEvent *event) override;

private slots:
    /**
     * @brief 搜索文本变更处理
     * @param text 搜索文本
     */
    void onSearchTextChanged(const QString &text);
    
    /**
     * @brief 搜索延迟超时处理
     */
    void onSearchDelayTimeout();
    
    /**
     * @brief 项目激活处理
     * @param index 项目索引
     */
    void onItemActivated(int index);
    
    /**
     * @brief 项目选择变更处理
     * @param index 当前索引
     */
    void onItemSelectionChanged(int index);
    
    /**
     * @brief 自动隐藏超时处理
     */
    void onAutoHideTimeout();
    
    /**
     * @brief 展开按钮点击处理
     */
    void onExpandButtonClicked();
    
    /**
     * @brief 设置按钮点击处理
     */
    void onSettingsButtonClicked();
    
    /**
     * @brief 配置变更处理
     * @param key 配置键
     * @param value 配置值
     */
    void onConfigChanged(const QString &key, const QVariant &value);

private:
    /**
     * @brief 初始化UI
     */
    void setupUI();
    
    /**
     * @brief 创建搜索区域
     */
    void createSearchArea();
    
    /**
     * @brief 创建结果列表
     */
    void createResultsList();
    
    /**
     * @brief 创建工具栏
     */
    void createToolBar();
    
    /**
     * @brief 设置连接
     */
    void setupConnections();
    
    /**
     * @brief 设置样式
     */
    void setupStyles();
    
    /**
     * @brief 应用主题
     * @param themeName 主题名称
     */
    void applyTheme(const QString &themeName);
    
    /**
     * @brief 设置窗口属性
     */
    void setupWindowProperties();
    
    /**
     * @brief 设置窗口位置
     */
    void setWindowPosition();
    
    /**
     * @brief 更新结果列表
     */
    void updateResultsList();
    
    /**
     * @brief 更新选择状态
     * @param index 选中索引
     */
    void updateSelection(int index);
    
    /**
     * @brief 启动淡入动画
     */
    void startFadeInAnimation();
    
    /**
     * @brief 启动淡出动画
     */
    void startFadeOutAnimation();
    
    /**
     * @brief 重置自动隐藏定时器
     */
    void resetAutoHideTimer();
    
    /**
     * @brief 加载设置
     */
    void loadSettings();
    
    /**
     * @brief 保存设置
     */
    void saveSettings();
    
    /**
     * @brief 创建阴影效果
     */
    void createShadowEffect();

private:
    // 服务引用
    std::shared_ptr<LaunchItemViewModel> m_viewModel;
    std::shared_ptr<DisplayModeManager> m_displayManager;
    std::shared_ptr<ConfigManager> m_configManager;
    
    // UI组件
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_searchLayout;
    QHBoxLayout *m_toolLayout;
    
    QLineEdit *m_searchEdit;
    QListWidget *m_resultsList;
    QPushButton *m_expandButton;
    QPushButton *m_settingsButton;
    QLabel *m_statusLabel;
    
    // 动画和效果
    QPropertyAnimation *m_fadeAnimation;
    QGraphicsDropShadowEffect *m_shadowEffect;
    
    // 定时器
    QTimer *m_searchDelayTimer;
    QTimer *m_autoHideTimer;
    
    // 状态管理
    bool m_autoHide = true;
    int m_maxResults = 8;
    bool m_alwaysOnTop = true;
    int m_selectedIndex = -1;
    bool m_dragging = false;
    QPoint m_dragStartPosition;
    
    // 搜索状态
    QString m_currentSearchText;
    QList<LaunchItem> m_searchResults;
    
    // 常量
    static const int WINDOW_WIDTH = 400;
    static const int WINDOW_HEIGHT = 300;
    static const int SEARCH_DELAY = 200;
    static const int AUTO_HIDE_DELAY = 3000;
    static const int ANIMATION_DURATION = 200;
    static const int BORDER_RADIUS = 8;
    static const int SHADOW_BLUR_RADIUS = 20;
    static const int ITEM_HEIGHT = 32;
};
