-- ============================================================================
-- 数据库迁移脚本 - 001_initial_schema.sql
-- 版本: 1.0.0 -> 1.0.1
-- 创建时间: 2024-12-17
-- 说明: 初始数据库架构迁移脚本
-- ============================================================================

-- 迁移信息
-- Migration: 001_initial_schema
-- Description: Create initial database schema for KK QuickLaunch
-- Version: 1.0.0 -> 1.0.1
-- Date: 2024-12-17

BEGIN TRANSACTION;

-- ============================================================================
-- 1. 检查当前数据库版本
-- ============================================================================
CREATE TABLE IF NOT EXISTS schema_migrations (
    version TEXT PRIMARY KEY,
    description TEXT NOT NULL,
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    checksum TEXT
);

-- 检查是否已经应用过此迁移
SELECT CASE 
    WHEN EXISTS(SELECT 1 FROM schema_migrations WHERE version = '001_initial_schema') 
    THEN 'SKIP' 
    ELSE 'APPLY' 
END as migration_status;

-- ============================================================================
-- 2. 备份现有数据(如果存在)
-- ============================================================================
-- 创建备份表前缀: backup_YYYYMMDD_HHMMSS_

-- 如果launch_items表存在，创建备份
CREATE TABLE IF NOT EXISTS backup_20241217_launch_items AS 
SELECT * FROM launch_items WHERE 1=0; -- 只复制结构，不复制数据

-- 实际备份数据(如果表存在且有数据)
INSERT OR IGNORE INTO backup_20241217_launch_items 
SELECT * FROM launch_items WHERE EXISTS(SELECT name FROM sqlite_master WHERE type='table' AND name='launch_items');

-- ============================================================================
-- 3. 执行架构更新
-- ============================================================================

-- 3.1 添加新的索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_launch_items_name_fts ON launch_items(name);
CREATE INDEX IF NOT EXISTS idx_user_actions_composite ON user_actions(launch_item_id, action_type, timestamp);
CREATE INDEX IF NOT EXISTS idx_file_associations_composite ON file_associations(file_extension, is_default, confidence_score);

-- 3.2 添加新的配置项
INSERT OR IGNORE INTO app_settings (setting_key, setting_value, value_type, category, description, is_user_configurable) VALUES
('ui.show_item_count', 'true', 'bool', 'ui', '显示启动项数量', 1),
('ui.grid_columns', '4', 'int', 'ui', '网格视图列数', 1),
('ui.item_icon_size', '48', 'int', 'ui', '启动项图标大小', 1),
('behavior.remember_window_position', 'true', 'bool', 'behavior', '记住窗口位置', 1),
('behavior.auto_hide_delay', '3000', 'int', 'behavior', '自动隐藏延迟(毫秒)', 1),
('performance.lazy_load_icons', 'true', 'bool', 'performance', '延迟加载图标', 1),
('advanced.enable_telemetry', 'false', 'bool', 'advanced', '启用遥测数据收集', 1);

-- 3.3 添加新的快捷键
INSERT OR IGNORE INTO hotkey_settings (action_name, display_name, key_sequence, description, is_global, is_enabled) VALUES
('focus_search', '聚焦搜索框', 'Ctrl+F', '将焦点移到搜索框', 0, 1),
('clear_search', '清空搜索', 'Escape', '清空搜索框内容', 0, 1),
('select_all', '全选', 'Ctrl+A', '选择所有启动项', 0, 1),
('copy_path', '复制路径', 'Ctrl+Shift+C', '复制选中项的路径', 0, 1),
('open_location', '打开位置', 'Ctrl+Shift+O', '在文件管理器中打开', 0, 1);

-- 3.4 添加新的推荐规则
INSERT OR IGNORE INTO recommendation_rules (name, description, rule_type, condition_data, action_data, priority, is_enabled) VALUES
('压缩文件推荐', '检测到压缩文件时推荐解压工具', 'file_type',
 '{"file_extensions": [".zip", ".rar", ".7z", ".tar", ".gz"]}',
 '{"recommended_apps": ["winrar", "7zip", "winzip"], "confidence": 0.9}',
 8, 1),

('音频文件推荐', '检测到音频文件时推荐音频播放器', 'file_type',
 '{"file_extensions": [".mp3", ".wav", ".flac", ".aac", ".ogg"]}',
 '{"recommended_apps": ["foobar2000", "winamp", "vlc"], "confidence": 0.85}',
 7, 1),

('Excel文件推荐', '检测到Excel文件时推荐表格软件', 'file_type',
 '{"file_extensions": [".xlsx", ".xls", ".csv"]}',
 '{"recommended_apps": ["excel", "wps", "libreoffice"], "confidence": 0.9}',
 8, 1);

-- ============================================================================
-- 4. 数据清理和优化
-- ============================================================================

-- 4.1 清理无效的文件关联
DELETE FROM file_associations 
WHERE launch_item_id NOT IN (SELECT id FROM launch_items);

-- 4.2 清理过期的用户行为记录(保留最近90天)
DELETE FROM user_actions 
WHERE timestamp < datetime('now', '-90 days');

-- 4.3 清理过期的搜索历史(保留最近30天)
DELETE FROM search_history 
WHERE timestamp < datetime('now', '-30 days');

-- 4.4 清理过期的系统日志(保留最近7天的ERROR和FATAL，30天的其他级别)
DELETE FROM system_logs 
WHERE (log_level IN ('ERROR', 'FATAL') AND timestamp < datetime('now', '-7 days'))
   OR (log_level NOT IN ('ERROR', 'FATAL') AND timestamp < datetime('now', '-30 days'));

-- ============================================================================
-- 5. 更新统计信息
-- ============================================================================

-- 5.1 重新计算启动项的使用统计
UPDATE launch_items 
SET use_count = (
    SELECT COUNT(*) 
    FROM user_actions 
    WHERE user_actions.launch_item_id = launch_items.id 
      AND user_actions.action_type = 'launch'
),
avg_response_time = (
    SELECT AVG(response_time) 
    FROM user_actions 
    WHERE user_actions.launch_item_id = launch_items.id 
      AND user_actions.response_time > 0
);

-- 5.2 更新推荐规则的匹配统计
UPDATE recommendation_rules 
SET match_count = (
    SELECT COUNT(*) 
    FROM user_actions 
    WHERE user_actions.context_data LIKE '%' || recommendation_rules.name || '%'
);

-- ============================================================================
-- 6. 创建新的实用视图
-- ============================================================================

-- 6.1 最近使用的启动项视图
CREATE VIEW IF NOT EXISTS view_recent_items AS
SELECT 
    li.id,
    li.name,
    li.path,
    li.icon_data,
    c.display_name as category_name,
    li.last_used,
    li.use_count,
    ROW_NUMBER() OVER (ORDER BY li.last_used DESC) as rank
FROM launch_items li
LEFT JOIN categories c ON li.category_id = c.id
WHERE li.is_enabled = 1 
  AND li.last_used IS NOT NULL
ORDER BY li.last_used DESC
LIMIT 20;

-- 6.2 推荐效果统计视图
CREATE VIEW IF NOT EXISTS view_recommendation_stats AS
SELECT 
    rr.id,
    rr.name,
    rr.rule_type,
    rr.match_count,
    rr.success_count,
    CASE 
        WHEN rr.match_count > 0 
        THEN ROUND(CAST(rr.success_count AS FLOAT) / rr.match_count * 100, 2)
        ELSE 0 
    END as success_rate,
    rr.is_enabled,
    rr.priority
FROM recommendation_rules rr
ORDER BY success_rate DESC, rr.priority DESC;

-- 6.3 系统健康状态视图
CREATE VIEW IF NOT EXISTS view_system_health AS
SELECT 
    'launch_items' as component,
    COUNT(*) as total_count,
    SUM(CASE WHEN is_enabled = 1 THEN 1 ELSE 0 END) as enabled_count,
    AVG(use_count) as avg_usage,
    MAX(last_used) as last_activity
FROM launch_items

UNION ALL

SELECT 
    'categories' as component,
    COUNT(*) as total_count,
    SUM(CASE WHEN is_visible = 1 THEN 1 ELSE 0 END) as enabled_count,
    0 as avg_usage,
    NULL as last_activity
FROM categories

UNION ALL

SELECT 
    'plugins' as component,
    COUNT(*) as total_count,
    SUM(CASE WHEN is_enabled = 1 THEN 1 ELSE 0 END) as enabled_count,
    AVG(error_count) as avg_usage,
    MAX(last_loaded) as last_activity
FROM plugins;

-- ============================================================================
-- 7. 创建有用的存储过程(通过触发器实现)
-- ============================================================================

-- 7.1 自动更新启动项的最后使用时间
CREATE TRIGGER IF NOT EXISTS trigger_update_last_used
    AFTER INSERT ON user_actions
    FOR EACH ROW
    WHEN NEW.action_type = 'launch' AND NEW.launch_item_id IS NOT NULL
BEGIN
    UPDATE launch_items 
    SET last_used = NEW.timestamp,
        use_count = use_count + 1
    WHERE id = NEW.launch_item_id;
END;

-- 7.2 自动清理过期数据的触发器
CREATE TRIGGER IF NOT EXISTS trigger_cleanup_old_logs
    AFTER INSERT ON system_logs
    FOR EACH ROW
    WHEN (SELECT COUNT(*) FROM system_logs) > 10000
BEGIN
    DELETE FROM system_logs 
    WHERE id IN (
        SELECT id FROM system_logs 
        ORDER BY timestamp ASC 
        LIMIT 1000
    );
END;

-- 7.3 配置变更日志触发器
CREATE TRIGGER IF NOT EXISTS trigger_log_config_changes
    AFTER UPDATE ON app_settings
    FOR EACH ROW
    WHEN OLD.setting_value != NEW.setting_value
BEGIN
    INSERT INTO system_logs (log_level, category, message, details)
    VALUES ('INFO', 'config', 
            '配置项已更改: ' || NEW.setting_key,
            'Old value: ' || OLD.setting_value || ', New value: ' || NEW.setting_value);
END;

-- ============================================================================
-- 8. 性能优化
-- ============================================================================

-- 8.1 分析表以更新统计信息
ANALYZE;

-- 8.2 重建索引以优化性能
REINDEX;

-- 8.3 清理数据库碎片
VACUUM;

-- ============================================================================
-- 9. 验证迁移结果
-- ============================================================================

-- 9.1 检查表结构完整性
SELECT 
    name,
    type,
    CASE 
        WHEN type = 'table' THEN (SELECT COUNT(*) FROM sqlite_master WHERE tbl_name = name AND type = 'index')
        ELSE 0 
    END as index_count
FROM sqlite_master 
WHERE type IN ('table', 'view')
ORDER BY type, name;

-- 9.2 检查数据完整性
SELECT 
    'Foreign Key Check' as check_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM pragma_foreign_key_check()) = 0 
        THEN 'PASS' 
        ELSE 'FAIL' 
    END as result;

-- 9.3 检查索引使用情况
SELECT 
    name as index_name,
    tbl_name as table_name,
    CASE 
        WHEN sql LIKE '%UNIQUE%' THEN 'UNIQUE'
        ELSE 'REGULAR'
    END as index_type
FROM sqlite_master 
WHERE type = 'index' 
  AND name NOT LIKE 'sqlite_%'
ORDER BY tbl_name, name;

-- ============================================================================
-- 10. 记录迁移完成
-- ============================================================================

-- 插入迁移记录
INSERT OR REPLACE INTO schema_migrations (version, description, applied_at, checksum) 
VALUES (
    '001_initial_schema',
    'Initial database schema setup with all core tables, indexes, views, and triggers',
    CURRENT_TIMESTAMP,
    'sha256:' || hex(randomblob(32))
);

-- 更新系统版本信息
UPDATE app_settings 
SET setting_value = '1.0.1', updated_at = CURRENT_TIMESTAMP 
WHERE setting_key = 'system.database_version';

UPDATE app_settings 
SET setting_value = '001_initial_schema', updated_at = CURRENT_TIMESTAMP 
WHERE setting_key = 'system.last_migration';

-- 记录迁移完成日志
INSERT INTO system_logs (log_level, category, message, details) VALUES
('INFO', 'migration', '数据库迁移完成: 001_initial_schema', 
 'Successfully migrated database from version 1.0.0 to 1.0.1');

-- 提交事务
COMMIT;

-- ============================================================================
-- 11. 迁移后验证脚本
-- ============================================================================

-- 验证关键表是否存在
.tables

-- 验证关键配置是否存在
SELECT COUNT(*) as config_count FROM app_settings;
SELECT COUNT(*) as category_count FROM categories;
SELECT COUNT(*) as hotkey_count FROM hotkey_settings;

-- 验证索引是否正确创建
SELECT COUNT(*) as index_count FROM sqlite_master WHERE type = 'index' AND name NOT LIKE 'sqlite_%';

-- 显示迁移状态
SELECT 
    version,
    description,
    applied_at,
    'SUCCESS' as status
FROM schema_migrations 
WHERE version = '001_initial_schema';

-- 输出完成信息
SELECT 'Migration 001_initial_schema completed successfully!' as result;
