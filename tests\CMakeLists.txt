cmake_minimum_required(VERSION 3.20)

# 查找Qt测试模块
find_package(Qt6 REQUIRED COMPONENTS Test)

# 启用测试
enable_testing()

# 包含目录
include_directories(
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/src/application
    ${CMAKE_SOURCE_DIR}/src/infrastructure
)

# 测试工具函数
function(add_unit_test test_name test_source)
    add_executable(${test_name} ${test_source})
    
    target_link_libraries(${test_name}
        Qt6::Core
        Qt6::Test
        Qt6::Widgets
        Qt6::Sql
    )
    
    # 添加到CTest
    add_test(NAME ${test_name} COMMAND ${test_name})
    
    # 设置测试属性
    set_tests_properties(${test_name} PROPERTIES
        TIMEOUT 30
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    )
endfunction()

# 单元测试
add_unit_test(test_service_container unit/test_service_container.cpp)
add_unit_test(test_logger unit/test_logger.cpp)
add_unit_test(test_application unit/test_application.cpp)

# 集成测试
add_unit_test(test_integration integration/test_integration.cpp)

# 性能测试
add_unit_test(test_performance performance/test_performance.cpp)

# 自定义测试目标
add_custom_target(run_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --parallel 4
    DEPENDS 
        test_service_container
        test_logger
        test_application
        test_integration
        test_performance
    COMMENT "Running all tests"
)

# 测试覆盖率（如果支持）
if(CMAKE_BUILD_TYPE STREQUAL "Debug" AND CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    find_program(GCOV_PATH gcov)
    find_program(LCOV_PATH lcov)
    find_program(GENHTML_PATH genhtml)
    
    if(GCOV_PATH AND LCOV_PATH AND GENHTML_PATH)
        # 添加覆盖率编译选项
        target_compile_options(${PROJECT_NAME} PRIVATE --coverage)
        target_link_libraries(${PROJECT_NAME} --coverage)
        
        # 覆盖率报告目标
        add_custom_target(coverage
            COMMAND ${LCOV_PATH} --directory . --capture --output-file coverage.info
            COMMAND ${LCOV_PATH} --remove coverage.info '/usr/*' --output-file coverage.info
            COMMAND ${LCOV_PATH} --list coverage.info
            COMMAND ${GENHTML_PATH} -o coverage coverage.info
            WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
            COMMENT "Generating code coverage report"
        )
    endif()
endif()
