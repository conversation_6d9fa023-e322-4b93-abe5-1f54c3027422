#pragma once

#include <QObject>
#include <QTimer>
#include <QAudioInput>
#include <QAudioOutput>
#include <QMediaPlayer>
#include <QTextToSpeech>
#include <QSpeechRecognition>
#include <QAudioDevice>
#include <QAudioFormat>
#include <QBuffer>
#include <QThread>
#include <QMutex>
#include <memory>

// 前置声明
class ConfigManager;
class LaunchManager;

/**
 * @brief 语音识别状态
 */
enum class VoiceRecognitionState {
    Idle,           // 空闲
    Listening,      // 监听中
    Processing,     // 处理中
    Speaking,       // 播报中
    Error           // 错误
};

/**
 * @brief 语音命令类型
 */
enum class VoiceCommandType {
    Launch,         // 启动命令 "启动 应用名称"
    Search,         // 搜索命令 "搜索 关键词"
    Open,           // 打开命令 "打开 文件/文件夹"
    Show,           // 显示命令 "显示 主窗口/设置"
    Hide,           // 隐藏命令 "隐藏 窗口"
    Switch,         // 切换命令 "切换 模式"
    Help,           // 帮助命令 "帮助"
    Exit,           // 退出命令 "退出"
    Unknown         // 未知命令
};

/**
 * @brief 语音命令结构
 */
struct VoiceCommand {
    VoiceCommandType type = VoiceCommandType::Unknown;
    QString command;        // 原始命令文本
    QString target;         // 目标对象
    QStringList parameters; // 参数列表
    double confidence = 0.0; // 置信度
    QDateTime timestamp;    // 时间戳
};

/**
 * @brief 语音设置
 */
struct VoiceSettings {
    // 识别设置
    QString recognitionLanguage = "zh-CN";
    double recognitionThreshold = 0.6;
    int recognitionTimeout = 5000;
    bool continuousRecognition = false;
    
    // 合成设置
    QString synthesisVoice = "default";
    double synthesisRate = 0.0;
    double synthesisVolume = 0.8;
    double synthesisPitch = 0.0;
    
    // 音频设置
    QString inputDevice = "default";
    QString outputDevice = "default";
    int sampleRate = 16000;
    int channels = 1;
    
    // 行为设置
    bool wakeWordEnabled = true;
    QString wakeWord = "小K";
    bool confirmationEnabled = true;
    bool feedbackEnabled = true;
};

/**
 * @brief 语音管理器
 * 
 * 提供完整的语音交互功能，包括：
 * - 语音识别和命令解析
 * - 文本转语音播报
 * - 语音命令执行
 * - 唤醒词检测
 * - 多语言支持
 */
class VoiceManager : public QObject
{
    Q_OBJECT
    
    Q_PROPERTY(VoiceRecognitionState state READ state NOTIFY stateChanged)
    Q_PROPERTY(bool enabled READ isEnabled WRITE setEnabled NOTIFY enabledChanged)
    Q_PROPERTY(bool listening READ isListening NOTIFY listeningChanged)
    Q_PROPERTY(bool speaking READ isSpeaking NOTIFY speakingChanged)
    Q_PROPERTY(double volume READ volume WRITE setVolume NOTIFY volumeChanged)

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit VoiceManager(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~VoiceManager();

    /**
     * @brief 初始化语音管理器
     * @return 是否成功
     */
    bool initialize();

    // 属性访问器
    VoiceRecognitionState state() const;
    bool isEnabled() const;
    void setEnabled(bool enabled);
    
    bool isListening() const;
    bool isSpeaking() const;
    
    double volume() const;
    void setVolume(double volume);

    // 语音识别
    
    /**
     * @brief 开始语音识别
     * @param continuous 是否连续识别
     */
    Q_INVOKABLE void startRecognition(bool continuous = false);
    
    /**
     * @brief 停止语音识别
     */
    Q_INVOKABLE void stopRecognition();
    
    /**
     * @brief 暂停语音识别
     */
    Q_INVOKABLE void pauseRecognition();
    
    /**
     * @brief 恢复语音识别
     */
    Q_INVOKABLE void resumeRecognition();
    
    /**
     * @brief 检查语音识别是否可用
     * @return 是否可用
     */
    bool isRecognitionAvailable() const;

    // 语音合成
    
    /**
     * @brief 播报文本
     * @param text 要播报的文本
     * @param interrupt 是否中断当前播报
     */
    Q_INVOKABLE void speak(const QString &text, bool interrupt = false);
    
    /**
     * @brief 停止播报
     */
    Q_INVOKABLE void stopSpeaking();
    
    /**
     * @brief 暂停播报
     */
    Q_INVOKABLE void pauseSpeaking();
    
    /**
     * @brief 恢复播报
     */
    Q_INVOKABLE void resumeSpeaking();
    
    /**
     * @brief 检查语音合成是否可用
     * @return 是否可用
     */
    bool isSynthesisAvailable() const;

    // 命令处理
    
    /**
     * @brief 解析语音命令
     * @param text 识别的文本
     * @return 语音命令
     */
    VoiceCommand parseCommand(const QString &text);
    
    /**
     * @brief 执行语音命令
     * @param command 语音命令
     * @return 是否成功
     */
    bool executeCommand(const VoiceCommand &command);
    
    /**
     * @brief 注册自定义命令
     * @param pattern 命令模式
     * @param type 命令类型
     */
    void registerCommand(const QString &pattern, VoiceCommandType type);
    
    /**
     * @brief 获取支持的命令列表
     * @return 命令列表
     */
    QStringList getSupportedCommands() const;

    // 设置管理
    
    /**
     * @brief 获取语音设置
     * @return 语音设置
     */
    VoiceSettings getSettings() const;
    
    /**
     * @brief 应用语音设置
     * @param settings 语音设置
     */
    void applySettings(const VoiceSettings &settings);
    
    /**
     * @brief 重置为默认设置
     */
    void resetToDefaults();

    // 设备管理
    
    /**
     * @brief 获取可用的输入设备
     * @return 设备列表
     */
    QStringList getAvailableInputDevices() const;
    
    /**
     * @brief 获取可用的输出设备
     * @return 设备列表
     */
    QStringList getAvailableOutputDevices() const;
    
    /**
     * @brief 获取可用的语音
     * @return 语音列表
     */
    QStringList getAvailableVoices() const;
    
    /**
     * @brief 测试音频设备
     * @param deviceName 设备名称
     * @param input 是否为输入设备
     * @return 是否可用
     */
    bool testAudioDevice(const QString &deviceName, bool input = true);

signals:
    /**
     * @brief 状态变更信号
     * @param state 新状态
     */
    void stateChanged(VoiceRecognitionState state);
    
    /**
     * @brief 启用状态变更信号
     * @param enabled 是否启用
     */
    void enabledChanged(bool enabled);
    
    /**
     * @brief 监听状态变更信号
     * @param listening 是否监听
     */
    void listeningChanged(bool listening);
    
    /**
     * @brief 播报状态变更信号
     * @param speaking 是否播报
     */
    void speakingChanged(bool speaking);
    
    /**
     * @brief 音量变更信号
     * @param volume 音量
     */
    void volumeChanged(double volume);
    
    /**
     * @brief 语音识别结果信号
     * @param text 识别文本
     * @param confidence 置信度
     */
    void recognitionResult(const QString &text, double confidence);
    
    /**
     * @brief 语音命令识别信号
     * @param command 语音命令
     */
    void commandRecognized(const VoiceCommand &command);
    
    /**
     * @brief 语音命令执行完成信号
     * @param command 语音命令
     * @param success 是否成功
     */
    void commandExecuted(const VoiceCommand &command, bool success);
    
    /**
     * @brief 唤醒词检测信号
     * @param detected 是否检测到
     */
    void wakeWordDetected(bool detected);
    
    /**
     * @brief 错误信号
     * @param error 错误信息
     */
    void error(const QString &error);

private slots:
    /**
     * @brief 语音识别结果处理
     * @param text 识别文本
     */
    void onRecognitionResult(const QString &text);
    
    /**
     * @brief 语音识别错误处理
     * @param error 错误信息
     */
    void onRecognitionError(const QString &error);
    
    /**
     * @brief 语音合成状态变更处理
     * @param state 合成状态
     */
    void onSynthesisStateChanged(QTextToSpeech::State state);
    
    /**
     * @brief 音频输入数据处理
     */
    void onAudioInputData();
    
    /**
     * @brief 唤醒词检测超时处理
     */
    void onWakeWordTimeout();
    
    /**
     * @brief 配置变更处理
     * @param key 配置键
     * @param value 配置值
     */
    void onConfigChanged(const QString &key, const QVariant &value);

private:
    /**
     * @brief 初始化语音识别
     * @return 是否成功
     */
    bool initializeRecognition();
    
    /**
     * @brief 初始化语音合成
     * @return 是否成功
     */
    bool initializeSynthesis();
    
    /**
     * @brief 初始化音频设备
     * @return 是否成功
     */
    bool initializeAudioDevices();
    
    /**
     * @brief 加载命令模式
     */
    void loadCommandPatterns();
    
    /**
     * @brief 设置状态
     * @param state 新状态
     */
    void setState(VoiceRecognitionState state);
    
    /**
     * @brief 检测唤醒词
     * @param text 文本
     * @return 是否检测到
     */
    bool detectWakeWord(const QString &text);
    
    /**
     * @brief 预处理识别文本
     * @param text 原始文本
     * @return 处理后文本
     */
    QString preprocessRecognitionText(const QString &text);
    
    /**
     * @brief 生成反馈语音
     * @param command 命令
     * @param success 是否成功
     * @return 反馈文本
     */
    QString generateFeedback(const VoiceCommand &command, bool success);

private:
    // 服务引用
    std::shared_ptr<ConfigManager> m_configManager;
    std::shared_ptr<LaunchManager> m_launchManager;
    
    // 语音组件
    std::unique_ptr<QTextToSpeech> m_textToSpeech;
    std::unique_ptr<QAudioInput> m_audioInput;
    std::unique_ptr<QAudioOutput> m_audioOutput;
    std::unique_ptr<QBuffer> m_audioBuffer;
    
    // 状态管理
    VoiceRecognitionState m_state = VoiceRecognitionState::Idle;
    bool m_enabled = false;
    bool m_listening = false;
    bool m_speaking = false;
    double m_volume = 0.8;
    
    // 设置
    VoiceSettings m_settings;
    
    // 命令模式
    QHash<QString, VoiceCommandType> m_commandPatterns;
    QStringList m_supportedCommands;
    
    // 定时器
    QTimer *m_wakeWordTimer;
    QTimer *m_recognitionTimeoutTimer;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // 常量
    static const int WAKE_WORD_TIMEOUT = 3000;
    static const int RECOGNITION_TIMEOUT = 5000;
    static const double DEFAULT_CONFIDENCE_THRESHOLD = 0.6;
};
