#include "ConfigManager.h"
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QXmlStreamWriter>
#include <QXmlStreamReader>
#include <QLoggingCategory>
#include <QReadLocker>
#include <QWriteLocker>
#include <QCoreApplication>

Q_LOGGING_CATEGORY(lcConfig, "config")

// 静态成员初始化
const QString ConfigManager::CATEGORY_GENERAL = "general";
const QString ConfigManager::CATEGORY_UI = "ui";
const QString ConfigManager::CATEGORY_BEHAVIOR = "behavior";
const QString ConfigManager::CATEGORY_PERFORMANCE = "performance";
const QString ConfigManager::CATEGORY_ADVANCED = "advanced";

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent)
    , m_autoSyncTimer(new QTimer(this))
{
    // 设置自动同步定时器
    m_autoSyncTimer->setSingleShot(false);
    m_autoSyncTimer->setInterval(m_autoSyncInterval);
    connect(m_autoSyncTimer, &QTimer::timeout, this, &ConfigManager::onAutoSyncTimeout);
    
    qCDebug(lcConfig) << "ConfigManager created";
}

ConfigManager::~ConfigManager()
{
    if (m_settings) {
        sync();
    }
    qCDebug(lcConfig) << "ConfigManager destroyed";
}

bool ConfigManager::initialize(const QString &configFile)
{
    QWriteLocker locker(&m_lock);
    
    // 确定配置文件路径
    if (configFile.isEmpty()) {
        m_configFilePath = getConfigFilePath();
    } else {
        m_configFilePath = configFile;
    }
    
    // 创建配置目录
    QFileInfo fileInfo(m_configFilePath);
    QDir().mkpath(fileInfo.absolutePath());
    
    // 初始化QSettings
    m_settings = std::make_unique<QSettings>(m_configFilePath, QSettings::IniFormat);
    m_settings->setIniCodec("UTF-8");
    
    // 加载默认配置
    loadDefaultConfig();
    
    // 设置文件监控
    if (m_fileWatchingEnabled) {
        m_fileWatcher = std::make_unique<QFileSystemWatcher>();
        m_fileWatcher->addPath(m_configFilePath);
        connect(m_fileWatcher.get(), &QFileSystemWatcher::fileChanged,
                this, &ConfigManager::onConfigFileChanged);
    }
    
    // 启动自动同步
    if (m_autoSyncEnabled) {
        m_autoSyncTimer->start();
    }
    
    qCInfo(lcConfig) << "ConfigManager initialized with file:" << m_configFilePath;
    return true;
}

QVariant ConfigManager::getValue(const QString &key, const QVariant &defaultValue) const
{
    QReadLocker locker(&m_lock);
    
    if (!m_settings) {
        return defaultValue;
    }
    
    // 首先检查是否有注册的配置项
    auto it = m_configItems.find(key);
    if (it != m_configItems.end()) {
        QVariant value = m_settings->value(key, it->defaultValue);
        
        // 类型转换和验证
        if (validateValue(key, value)) {
            return convertValue(value, it->type);
        } else {
            qCWarning(lcConfig) << "Invalid value for key:" << key << "using default";
            return it->defaultValue;
        }
    }
    
    return m_settings->value(key, defaultValue);
}

bool ConfigManager::setValue(const QString &key, const QVariant &value)
{
    QWriteLocker locker(&m_lock);
    
    if (!m_settings) {
        return false;
    }
    
    // 验证值
    if (!validateValue(key, value)) {
        qCWarning(lcConfig) << "Invalid value for key:" << key;
        return false;
    }
    
    // 获取旧值
    QVariant oldValue = m_settings->value(key);
    
    // 设置新值
    m_settings->setValue(key, value);
    
    // 发送变更信号
    if (oldValue != value) {
        emit valueChanged(key, value, oldValue);
        qCDebug(lcConfig) << "Config changed:" << key << "=" << value;
    }
    
    return true;
}

bool ConfigManager::contains(const QString &key) const
{
    QReadLocker locker(&m_lock);
    
    if (!m_settings) {
        return false;
    }
    
    return m_settings->contains(key);
}

bool ConfigManager::removeValue(const QString &key)
{
    QWriteLocker locker(&m_lock);
    
    if (!m_settings) {
        return false;
    }
    
    if (!m_settings->contains(key)) {
        return false;
    }
    
    QVariant oldValue = m_settings->value(key);
    m_settings->remove(key);
    
    emit valueChanged(key, QVariant(), oldValue);
    qCDebug(lcConfig) << "Config removed:" << key;
    
    return true;
}

QStringList ConfigManager::getAllKeys() const
{
    QReadLocker locker(&m_lock);
    
    if (!m_settings) {
        return QStringList();
    }
    
    return m_settings->allKeys();
}

QStringList ConfigManager::getKeysByCategory(const QString &category) const
{
    QReadLocker locker(&m_lock);
    
    QStringList keys;
    
    for (auto it = m_configItems.begin(); it != m_configItems.end(); ++it) {
        if (it->category == category) {
            keys.append(it->key);
        }
    }
    
    return keys;
}

QString ConfigManager::getString(const QString &key, const QString &defaultValue) const
{
    return getValue(key, defaultValue).toString();
}

int ConfigManager::getInt(const QString &key, int defaultValue) const
{
    return getValue(key, defaultValue).toInt();
}

bool ConfigManager::getBool(const QString &key, bool defaultValue) const
{
    return getValue(key, defaultValue).toBool();
}

double ConfigManager::getDouble(const QString &key, double defaultValue) const
{
    return getValue(key, defaultValue).toDouble();
}

QStringList ConfigManager::getStringList(const QString &key, const QStringList &defaultValue) const
{
    return getValue(key, defaultValue).toStringList();
}

void ConfigManager::registerConfigItem(const ConfigItem &item)
{
    QWriteLocker locker(&m_lock);
    
    if (!validateConfigItem(item)) {
        qCWarning(lcConfig) << "Invalid config item:" << item.key;
        return;
    }
    
    m_configItems[item.key] = item;
    
    // 如果配置中没有这个键，设置默认值
    if (m_settings && !m_settings->contains(item.key)) {
        m_settings->setValue(item.key, item.defaultValue);
    }
    
    qCDebug(lcConfig) << "Config item registered:" << item.key;
}

ConfigItem ConfigManager::getConfigItem(const QString &key) const
{
    QReadLocker locker(&m_lock);
    
    auto it = m_configItems.find(key);
    if (it != m_configItems.end()) {
        return it.value();
    }
    
    return ConfigItem(); // 返回空的配置项
}

QList<ConfigItem> ConfigManager::getAllConfigItems() const
{
    QReadLocker locker(&m_lock);
    
    return m_configItems.values();
}

QList<ConfigItem> ConfigManager::getConfigItemsByCategory(const QString &category) const
{
    QReadLocker locker(&m_lock);
    
    QList<ConfigItem> items;
    
    for (auto it = m_configItems.begin(); it != m_configItems.end(); ++it) {
        if (it->category == category) {
            items.append(it.value());
        }
    }
    
    return items;
}

bool ConfigManager::validateValue(const QString &key, const QVariant &value) const
{
    auto it = m_configItems.find(key);
    if (it == m_configItems.end()) {
        return true; // 未注册的配置项不验证
    }
    
    const ConfigItem &item = it.value();
    
    // 类型检查
    if (!value.canConvert(QMetaType::Type(QMetaType::type(item.type.toUtf8())))) {
        return false;
    }
    
    // 枚举值检查
    if (!item.validValues.isEmpty()) {
        return item.validValues.contains(value);
    }
    
    // 范围检查
    if (item.minValue.isValid() && value < item.minValue) {
        return false;
    }
    
    if (item.maxValue.isValid() && value > item.maxValue) {
        return false;
    }
    
    return true;
}

void ConfigManager::resetToDefaults(const QString &category)
{
    QWriteLocker locker(&m_lock);

    if (!m_settings) {
        return;
    }

    QStringList keysToReset;

    if (category.isEmpty()) {
        // 重置所有配置
        keysToReset = m_configItems.keys();
    } else {
        // 重置指定分类的配置
        for (auto it = m_configItems.begin(); it != m_configItems.end(); ++it) {
            if (it->category == category) {
                keysToReset.append(it->key);
            }
        }
    }

    // 执行重置
    for (const QString &key : keysToReset) {
        auto it = m_configItems.find(key);
        if (it != m_configItems.end()) {
            QVariant oldValue = m_settings->value(key);
            m_settings->setValue(key, it->defaultValue);
            emit valueChanged(key, it->defaultValue, oldValue);
        }
    }

    emit configReset(category);
    qCInfo(lcConfig) << "Configuration reset for category:" << (category.isEmpty() ? "all" : category);
}

bool ConfigManager::exportConfig(const QString &filePath, const QString &format) const
{
    QReadLocker locker(&m_lock);

    if (!m_settings) {
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qCWarning(lcConfig) << "Cannot open file for export:" << filePath;
        return false;
    }

    if (format.toLower() == "json") {
        return exportToJson(file);
    } else if (format.toLower() == "xml") {
        return exportToXml(file);
    } else {
        // 默认使用INI格式
        return exportToIni(file);
    }
}

bool ConfigManager::importConfig(const QString &filePath, const QString &format, bool merge)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qCWarning(lcConfig) << "Cannot open file for import:" << filePath;
        return false;
    }

    QVariantMap importedConfig;

    if (format.toLower() == "json") {
        importedConfig = importFromJson(file);
    } else if (format.toLower() == "xml") {
        importedConfig = importFromXml(file);
    } else {
        importedConfig = importFromIni(file);
    }

    if (importedConfig.isEmpty()) {
        qCWarning(lcConfig) << "Failed to import configuration from:" << filePath;
        return false;
    }

    QWriteLocker locker(&m_lock);

    // 如果不是合并模式，先清空现有配置
    if (!merge) {
        m_settings->clear();
    }

    // 导入配置
    int importedCount = 0;
    for (auto it = importedConfig.begin(); it != importedConfig.end(); ++it) {
        if (validateValue(it.key(), it.value())) {
            QVariant oldValue = m_settings->value(it.key());
            m_settings->setValue(it.key(), it.value());
            emit valueChanged(it.key(), it.value(), oldValue);
            importedCount++;
        }
    }

    qCInfo(lcConfig) << "Imported" << importedCount << "configuration items from:" << filePath;
    return importedCount > 0;
}

void ConfigManager::setFileWatchingEnabled(bool enabled)
{
    QWriteLocker locker(&m_lock);

    m_fileWatchingEnabled = enabled;

    if (enabled && !m_fileWatcher && !m_configFilePath.isEmpty()) {
        m_fileWatcher = std::make_unique<QFileSystemWatcher>();
        m_fileWatcher->addPath(m_configFilePath);
        connect(m_fileWatcher.get(), &QFileSystemWatcher::fileChanged,
                this, &ConfigManager::onConfigFileChanged);
    } else if (!enabled && m_fileWatcher) {
        m_fileWatcher.reset();
    }

    qCDebug(lcConfig) << "File watching" << (enabled ? "enabled" : "disabled");
}

bool ConfigManager::isFileWatchingEnabled() const
{
    QReadLocker locker(&m_lock);
    return m_fileWatchingEnabled;
}

void ConfigManager::setAutoSyncInterval(int intervalMs)
{
    m_autoSyncInterval = intervalMs;
    m_autoSyncTimer->setInterval(intervalMs);

    qCDebug(lcConfig) << "Auto sync interval set to:" << intervalMs << "ms";
}

int ConfigManager::setValues(const QVariantMap &values)
{
    int successCount = 0;

    for (auto it = values.begin(); it != values.end(); ++it) {
        if (setValue(it.key(), it.value())) {
            successCount++;
        }
    }

    qCDebug(lcConfig) << "Batch set completed, success count:" << successCount;
    return successCount;
}

QVariantMap ConfigManager::getValues(const QStringList &keys) const
{
    QVariantMap values;

    for (const QString &key : keys) {
        if (contains(key)) {
            values[key] = getValue(key);
        }
    }

    return values;
}

void ConfigManager::onConfigFileChanged(const QString &path)
{
    Q_UNUSED(path)

    qCDebug(lcConfig) << "Configuration file changed, reloading";
    reload();
    emit configFileChanged();
}

void ConfigManager::onAutoSyncTimeout()
{
    sync();
}

void ConfigManager::loadDefaultConfig()
{
    // 注册默认配置项

    // 通用配置
    registerConfigItem({
        "general.language", "zh_CN", "zh_CN", "QString", CATEGORY_GENERAL,
        "界面语言", true, true, {"zh_CN", "en_US", "ja_JP"}
    });

    registerConfigItem({
        "general.auto_start", false, false, "bool", CATEGORY_GENERAL,
        "开机自启动", true, true
    });

    registerConfigItem({
        "general.check_updates", true, true, "bool", CATEGORY_GENERAL,
        "自动检查更新", true, false
    });

    // UI配置
    registerConfigItem({
        "ui.theme", "default", "default", "QString", CATEGORY_UI,
        "界面主题", true, false, {"default", "dark", "light", "high_contrast"}
    });

    registerConfigItem({
        "ui.window_opacity", 0.95, 0.95, "double", CATEGORY_UI,
        "窗口透明度", true, false, QVariantList(), 0.5, 1.0
    });

    registerConfigItem({
        "ui.show_on_startup", true, true, "bool", CATEGORY_UI,
        "启动时显示主窗口", true, false
    });

    registerConfigItem({
        "ui.minimize_to_tray", true, true, "bool", CATEGORY_UI,
        "最小化到系统托盘", true, false
    });

    registerConfigItem({
        "ui.close_to_tray", true, true, "bool", CATEGORY_UI,
        "关闭到系统托盘", true, false
    });

    // 行为配置
    registerConfigItem({
        "behavior.search_delay", 300, 300, "int", CATEGORY_BEHAVIOR,
        "搜索延迟(毫秒)", true, false, QVariantList(), 100, 1000
    });

    registerConfigItem({
        "behavior.max_search_results", 50, 50, "int", CATEGORY_BEHAVIOR,
        "最大搜索结果数", true, false, QVariantList(), 10, 200
    });

    registerConfigItem({
        "behavior.auto_hide_delay", 5000, 5000, "int", CATEGORY_BEHAVIOR,
        "自动隐藏延迟(毫秒)", true, false, QVariantList(), 1000, 30000
    });

    // 性能配置
    registerConfigItem({
        "performance.cache_size", 100, 100, "int", CATEGORY_PERFORMANCE,
        "缓存大小(MB)", true, true, QVariantList(), 50, 500
    });

    registerConfigItem({
        "performance.background_scan", true, true, "bool", CATEGORY_PERFORMANCE,
        "后台扫描", true, false
    });

    // 高级配置
    registerConfigItem({
        "advanced.debug_mode", false, false, "bool", CATEGORY_ADVANCED,
        "调试模式", true, true
    });

    registerConfigItem({
        "advanced.log_level", "info", "info", "QString", CATEGORY_ADVANCED,
        "日志级别", true, true, {"debug", "info", "warning", "error"}
    });

    qCDebug(lcConfig) << "Default configuration loaded";
}

bool ConfigManager::validateConfigItem(const ConfigItem &item) const
{
    // 基本验证
    if (item.key.isEmpty() || item.type.isEmpty()) {
        return false;
    }

    // 键名格式检查
    if (!item.key.contains('.')) {
        qCWarning(lcConfig) << "Config key should contain category:" << item.key;
    }

    // 类型检查
    QStringList validTypes = {"bool", "int", "double", "QString", "QStringList"};
    if (!validTypes.contains(item.type)) {
        qCWarning(lcConfig) << "Invalid config type:" << item.type;
        return false;
    }

    return true;
}

QVariant ConfigManager::convertValue(const QVariant &value, const QString &targetType) const
{
    if (targetType == "bool") {
        return value.toBool();
    } else if (targetType == "int") {
        return value.toInt();
    } else if (targetType == "double") {
        return value.toDouble();
    } else if (targetType == "QString") {
        return value.toString();
    } else if (targetType == "QStringList") {
        return value.toStringList();
    }

    return value;
}

QString ConfigManager::getConfigFilePath() const
{
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(appDataPath);
    return appDataPath + "/config.ini";
}

QString ConfigManager::createBackup() const
{
    if (!QFile::exists(m_configFilePath)) {
        return QString();
    }

    QString backupPath = m_configFilePath + ".backup." +
                        QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");

    if (QFile::copy(m_configFilePath, backupPath)) {
        qCDebug(lcConfig) << "Configuration backup created:" << backupPath;
        return backupPath;
    }

    qCWarning(lcConfig) << "Failed to create configuration backup";
    return QString();
}

bool ConfigManager::exportToJson(QFile &file) const
{
    QJsonObject jsonObj;

    QStringList keys = m_settings->allKeys();
    for (const QString &key : keys) {
        QVariant value = m_settings->value(key);

        // 将嵌套键转换为JSON对象结构
        QStringList keyParts = key.split('.');
        QJsonObject *currentObj = &jsonObj;

        for (int i = 0; i < keyParts.size() - 1; ++i) {
            const QString &part = keyParts[i];
            if (!currentObj->contains(part)) {
                currentObj->insert(part, QJsonObject());
            }
            QJsonValue val = currentObj->value(part);
            if (val.isObject()) {
                QJsonObject obj = val.toObject();
                currentObj = &obj;
                currentObj->insert(part, obj);
            }
        }

        // 设置最终值
        QString finalKey = keyParts.last();
        currentObj->insert(finalKey, QJsonValue::fromVariant(value));
    }

    QJsonDocument doc(jsonObj);
    file.write(doc.toJson());

    return true;
}

bool ConfigManager::exportToXml(QFile &file) const
{
    QXmlStreamWriter writer(&file);
    writer.setAutoFormatting(true);
    writer.writeStartDocument();
    writer.writeStartElement("configuration");

    QStringList keys = m_settings->allKeys();
    for (const QString &key : keys) {
        QVariant value = m_settings->value(key);

        writer.writeStartElement("item");
        writer.writeAttribute("key", key);
        writer.writeAttribute("type", value.typeName());
        writer.writeCharacters(value.toString());
        writer.writeEndElement();
    }

    writer.writeEndElement();
    writer.writeEndDocument();

    return true;
}

bool ConfigManager::exportToIni(QFile &file) const
{
    // 简单复制INI文件
    QFile sourceFile(m_configFilePath);
    if (sourceFile.open(QIODevice::ReadOnly)) {
        file.write(sourceFile.readAll());
        return true;
    }

    return false;
}

QVariantMap ConfigManager::importFromJson(QFile &file) const
{
    QVariantMap config;

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);

    if (error.error != QJsonParseError::NoError) {
        qCWarning(lcConfig) << "JSON parse error:" << error.errorString();
        return config;
    }

    // 将JSON对象扁平化为键值对
    flattenJsonObject(doc.object(), config);

    return config;
}

QVariantMap ConfigManager::importFromXml(QFile &file) const
{
    QVariantMap config;

    QXmlStreamReader reader(&file);

    while (!reader.atEnd()) {
        reader.readNext();

        if (reader.isStartElement() && reader.name() == "item") {
            QString key = reader.attributes().value("key").toString();
            QString type = reader.attributes().value("type").toString();
            QString valueStr = reader.readElementText();

            QVariant value = convertStringToVariant(valueStr, type);
            config[key] = value;
        }
    }

    if (reader.hasError()) {
        qCWarning(lcConfig) << "XML parse error:" << reader.errorString();
        return QVariantMap();
    }

    return config;
}

QVariantMap ConfigManager::importFromIni(QFile &file) const
{
    QVariantMap config;

    QSettings tempSettings(file.fileName(), QSettings::IniFormat);
    QStringList keys = tempSettings.allKeys();

    for (const QString &key : keys) {
        config[key] = tempSettings.value(key);
    }

    return config;
}

void ConfigManager::flattenJsonObject(const QJsonObject &obj, QVariantMap &result, const QString &prefix) const
{
    for (auto it = obj.begin(); it != obj.end(); ++it) {
        QString key = prefix.isEmpty() ? it.key() : prefix + "." + it.key();
        QJsonValue value = it.value();

        if (value.isObject()) {
            flattenJsonObject(value.toObject(), result, key);
        } else {
            result[key] = value.toVariant();
        }
    }
}

QVariant ConfigManager::convertStringToVariant(const QString &str, const QString &type) const
{
    if (type == "bool") {
        return str.toLower() == "true";
    } else if (type == "int") {
        return str.toInt();
    } else if (type == "double") {
        return str.toDouble();
    } else if (type == "QStringList") {
        return str.split(',', Qt::SkipEmptyParts);
    }

    return str; // 默认为字符串
}

void ConfigManager::sync()
{
    QReadLocker locker(&m_lock);
    
    if (m_settings) {
        m_settings->sync();
        qCDebug(lcConfig) << "Configuration synced to file";
    }
}

bool ConfigManager::reload()
{
    QWriteLocker locker(&m_lock);
    
    if (!m_settings) {
        return false;
    }
    
    // 重新读取配置文件
    m_settings->sync();
    
    emit configReloaded();
    qCInfo(lcConfig) << "Configuration reloaded";
    
    return true;
}
