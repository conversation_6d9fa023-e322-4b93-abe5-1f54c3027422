#include "BaseViewModel.h"
#include <QLoggingCategory>
#include <QtConcurrent>
#include <QThread>

Q_LOGGING_CATEGORY(lcBaseViewModel, "baseviewmodel")

BaseViewModel::BaseViewModel(QObject *parent)
    : QAbstractListModel(parent)
    , m_refreshTimer(new QTimer(this))
{
    // 设置延迟刷新定时器
    m_refreshTimer->setSingleShot(true);
    m_refreshTimer->setInterval(DEFAULT_REFRESH_DELAY);
    connect(m_refreshTimer, &QTimer::timeout, this, &BaseViewModel::onDelayedRefresh);
    
    qCDebug(lcBaseViewModel) << "BaseViewModel created";
}

BaseViewModel::~BaseViewModel()
{
    cancelLoad();
    qCDebug(lcBaseViewModel) << "BaseViewModel destroyed";
}

bool BaseViewModel::isLoading() const
{
    QReadLocker locker(&m_lock);
    return m_loading;
}

bool BaseViewModel::hasData() const
{
    return rowCount() > 0;
}

QString BaseViewModel::errorMessage() const
{
    QReadLocker locker(&m_lock);
    return m_errorMessage;
}

void BaseViewModel::refresh()
{
    qCDebug(lcBaseViewModel) << "Refresh requested";
    
    clearErrorMessage();
    loadAsync();
}

void BaseViewModel::clear()
{
    qCDebug(lcBaseViewModel) << "Clear requested";
    
    beginResetModel();
    
    // 子类应该重写此方法来清理具体数据
    
    endResetModel();
    
    clearErrorMessage();
    notifyDataChanged();
}

void BaseViewModel::loadAsync()
{
    qCDebug(lcBaseViewModel) << "Async load requested";
    
    // 子类应该重写此方法来实现具体的异步加载逻辑
    setLoading(true);
    
    // 模拟异步操作
    QTimer::singleShot(100, this, [this]() {
        setLoading(false);
        emit dataLoaded(true);
    });
}

void BaseViewModel::cancelLoad()
{
    qCDebug(lcBaseViewModel) << "Cancel load requested";
    
    setLoading(false);
    cancelScheduledRefresh();
}

void BaseViewModel::onDelayedRefresh()
{
    qCDebug(lcBaseViewModel) << "Delayed refresh triggered";
    refresh();
}

void BaseViewModel::setLoading(bool loading)
{
    {
        QWriteLocker locker(&m_lock);
        if (m_loading == loading) {
            return;
        }
        m_loading = loading;
    }
    
    emit loadingChanged(loading);
    qCDebug(lcBaseViewModel) << "Loading state changed:" << loading;
}

void BaseViewModel::setErrorMessage(const QString &message)
{
    {
        QWriteLocker locker(&m_lock);
        if (m_errorMessage == message) {
            return;
        }
        m_errorMessage = message;
    }
    
    emit errorMessageChanged(message);
    qCWarning(lcBaseViewModel) << "Error message set:" << message;
}

void BaseViewModel::clearErrorMessage()
{
    setErrorMessage(QString());
}

void BaseViewModel::beginUpdate()
{
    m_inBatchUpdate = true;
    qCDebug(lcBaseViewModel) << "Batch update started";
}

void BaseViewModel::endUpdate()
{
    if (m_inBatchUpdate) {
        m_inBatchUpdate = false;
        notifyDataChanged();
        qCDebug(lcBaseViewModel) << "Batch update ended";
    }
}

void BaseViewModel::scheduleRefresh(int delay)
{
    m_refreshTimer->setInterval(delay);
    m_refreshTimer->start();
    qCDebug(lcBaseViewModel) << "Refresh scheduled in" << delay << "ms";
}

void BaseViewModel::cancelScheduledRefresh()
{
    if (m_refreshTimer->isActive()) {
        m_refreshTimer->stop();
        qCDebug(lcBaseViewModel) << "Scheduled refresh cancelled";
    }
}

void BaseViewModel::notifyDataChanged()
{
    if (!m_inBatchUpdate) {
        emit dataChanged();
        emit hasDataChanged(hasData());
        qCDebug(lcBaseViewModel) << "Data changed notification sent";
    }
}

void BaseViewModel::notifyCountChanged()
{
    if (!m_inBatchUpdate) {
        emit countChanged(rowCount());
        emit hasDataChanged(hasData());
        qCDebug(lcBaseViewModel) << "Count changed notification sent, new count:" << rowCount();
    }
}

// BaseSortFilterProxyModel 实现

BaseSortFilterProxyModel::BaseSortFilterProxyModel(QObject *parent)
    : QSortFilterProxyModel(parent)
{
    // 设置默认排序
    setDynamicSortFilter(true);
    setSortCaseSensitivity(Qt::CaseInsensitive);
    setFilterCaseSensitivity(Qt::CaseInsensitive);
    
    qCDebug(lcBaseViewModel) << "BaseSortFilterProxyModel created";
}

QString BaseSortFilterProxyModel::filterText() const
{
    return m_filterText;
}

void BaseSortFilterProxyModel::setFilterText(const QString &text)
{
    if (m_filterText != text) {
        m_filterText = text;
        setFilterFixedString(text);
        emit filterTextChanged(text);
        qCDebug(lcBaseViewModel) << "Filter text changed:" << text;
    }
}

int BaseSortFilterProxyModel::sortColumn() const
{
    return QSortFilterProxyModel::sortColumn();
}

void BaseSortFilterProxyModel::setSortColumn(int column)
{
    if (sortColumn() != column) {
        sort(column, sortOrder());
        emit sortColumnChanged(column);
        qCDebug(lcBaseViewModel) << "Sort column changed:" << column;
    }
}

Qt::SortOrder BaseSortFilterProxyModel::sortOrder() const
{
    return QSortFilterProxyModel::sortOrder();
}

void BaseSortFilterProxyModel::setSortOrder(Qt::SortOrder order)
{
    if (sortOrder() != order) {
        sort(sortColumn(), order);
        emit sortOrderChanged(order);
        qCDebug(lcBaseViewModel) << "Sort order changed:" << (order == Qt::AscendingOrder ? "Ascending" : "Descending");
    }
}

void BaseSortFilterProxyModel::clearFilter()
{
    setFilterText(QString());
    qCDebug(lcBaseViewModel) << "Filter cleared";
}

void BaseSortFilterProxyModel::resetSort()
{
    sort(-1, Qt::AscendingOrder);
    qCDebug(lcBaseViewModel) << "Sort reset";
}

bool BaseSortFilterProxyModel::filterAcceptsRow(int sourceRow, const QModelIndex &sourceParent) const
{
    if (m_filterText.isEmpty()) {
        return true;
    }
    
    // 默认实现：检查所有列是否包含过滤文本
    QAbstractItemModel *model = sourceModel();
    if (!model) {
        return true;
    }
    
    int columnCount = model->columnCount(sourceParent);
    for (int column = 0; column < columnCount; ++column) {
        QModelIndex index = model->index(sourceRow, column, sourceParent);
        QString data = model->data(index, Qt::DisplayRole).toString();
        
        if (data.contains(m_filterText, Qt::CaseInsensitive)) {
            return true;
        }
    }
    
    return false;
}

bool BaseSortFilterProxyModel::lessThan(const QModelIndex &left, const QModelIndex &right) const
{
    // 默认实现：使用标准比较
    QVariant leftData = sourceModel()->data(left, Qt::DisplayRole);
    QVariant rightData = sourceModel()->data(right, Qt::DisplayRole);
    
    // 处理数字比较
    if (leftData.type() == QVariant::Int || leftData.type() == QVariant::Double) {
        return leftData.toDouble() < rightData.toDouble();
    }
    
    // 处理日期比较
    if (leftData.type() == QVariant::DateTime) {
        return leftData.toDateTime() < rightData.toDateTime();
    }
    
    // 默认字符串比较
    return QString::localeAwareCompare(leftData.toString(), rightData.toString()) < 0;
}
