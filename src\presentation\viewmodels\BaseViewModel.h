#pragma once

#include <QAbstractListModel>
#include <QSortFilterProxyModel>
#include <QTimer>
#include <QReadWriteLock>
#include <memory>

/**
 * @brief ViewModel基类
 * 
 * 提供MVVM模式的基础功能，包括：
 * - 数据绑定支持
 * - 异步数据加载
 * - 排序和过滤
 * - 变更通知
 */
class BaseViewModel : public QAbstractListModel
{
    Q_OBJECT
    
    // 通用属性
    Q_PROPERTY(bool loading READ isLoading NOTIFY loadingChanged)
    Q_PROPERTY(bool hasData READ hasData NOTIFY hasDataChanged)
    Q_PROPERTY(int count READ rowCount NOTIFY countChanged)
    Q_PROPERTY(QString errorMessage READ errorMessage NOTIFY errorMessageChanged)

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit BaseViewModel(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~BaseViewModel() override;

    // 属性访问器
    
    /**
     * @brief 检查是否正在加载
     * @return 是否正在加载
     */
    bool isLoading() const;
    
    /**
     * @brief 检查是否有数据
     * @return 是否有数据
     */
    bool hasData() const;
    
    /**
     * @brief 获取错误消息
     * @return 错误消息
     */
    QString errorMessage() const;

    // 公共方法
    
    /**
     * @brief 刷新数据
     */
    Q_INVOKABLE virtual void refresh();
    
    /**
     * @brief 清空数据
     */
    Q_INVOKABLE virtual void clear();
    
    /**
     * @brief 异步加载数据
     */
    Q_INVOKABLE virtual void loadAsync();
    
    /**
     * @brief 取消加载
     */
    Q_INVOKABLE virtual void cancelLoad();

signals:
    /**
     * @brief 加载状态变更信号
     * @param loading 是否正在加载
     */
    void loadingChanged(bool loading);
    
    /**
     * @brief 数据状态变更信号
     * @param hasData 是否有数据
     */
    void hasDataChanged(bool hasData);
    
    /**
     * @brief 数量变更信号
     * @param count 数量
     */
    void countChanged(int count);
    
    /**
     * @brief 错误消息变更信号
     * @param message 错误消息
     */
    void errorMessageChanged(const QString &message);
    
    /**
     * @brief 数据加载完成信号
     * @param success 是否成功
     */
    void dataLoaded(bool success);
    
    /**
     * @brief 数据变更信号
     */
    void dataChanged();

protected slots:
    /**
     * @brief 延迟刷新处理
     */
    virtual void onDelayedRefresh();

protected:
    /**
     * @brief 设置加载状态
     * @param loading 是否正在加载
     */
    void setLoading(bool loading);
    
    /**
     * @brief 设置错误消息
     * @param message 错误消息
     */
    void setErrorMessage(const QString &message);
    
    /**
     * @brief 清除错误消息
     */
    void clearErrorMessage();
    
    /**
     * @brief 开始批量更新
     */
    void beginUpdate();
    
    /**
     * @brief 结束批量更新
     */
    void endUpdate();
    
    /**
     * @brief 延迟刷新
     * @param delay 延迟时间（毫秒）
     */
    void scheduleRefresh(int delay = 100);
    
    /**
     * @brief 取消延迟刷新
     */
    void cancelScheduledRefresh();
    
    /**
     * @brief 通知数据变更
     */
    void notifyDataChanged();
    
    /**
     * @brief 通知行数变更
     */
    void notifyCountChanged();
    
    /**
     * @brief 执行异步操作
     * @param operation 操作函数
     */
    template<typename Func>
    void executeAsync(Func&& operation);
    
    /**
     * @brief 在主线程中执行
     * @param operation 操作函数
     */
    template<typename Func>
    void executeInMainThread(Func&& operation);

private:
    bool m_loading = false;              // 加载状态
    QString m_errorMessage;              // 错误消息
    bool m_inBatchUpdate = false;        // 批量更新状态
    
    QTimer *m_refreshTimer;              // 延迟刷新定时器
    mutable QReadWriteLock m_lock;       // 线程安全锁
    
    static const int DEFAULT_REFRESH_DELAY = 100; // 默认刷新延迟
};

/**
 * @brief 排序过滤代理模型基类
 */
class BaseSortFilterProxyModel : public QSortFilterProxyModel
{
    Q_OBJECT
    
    Q_PROPERTY(QString filterText READ filterText WRITE setFilterText NOTIFY filterTextChanged)
    Q_PROPERTY(int sortColumn READ sortColumn WRITE setSortColumn NOTIFY sortColumnChanged)
    Q_PROPERTY(Qt::SortOrder sortOrder READ sortOrder WRITE setSortOrder NOTIFY sortOrderChanged)

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit BaseSortFilterProxyModel(QObject *parent = nullptr);

    // 属性访问器
    QString filterText() const;
    void setFilterText(const QString &text);
    
    int sortColumn() const;
    void setSortColumn(int column);
    
    Qt::SortOrder sortOrder() const;
    void setSortOrder(Qt::SortOrder order);

    // 公共方法
    Q_INVOKABLE void clearFilter();
    Q_INVOKABLE void resetSort();

signals:
    void filterTextChanged(const QString &text);
    void sortColumnChanged(int column);
    void sortOrderChanged(Qt::SortOrder order);

protected:
    /**
     * @brief 过滤行
     * @param sourceRow 源行号
     * @param sourceParent 源父索引
     * @return 是否接受
     */
    bool filterAcceptsRow(int sourceRow, const QModelIndex &sourceParent) const override;
    
    /**
     * @brief 比较行
     * @param left 左索引
     * @param right 右索引
     * @return 比较结果
     */
    bool lessThan(const QModelIndex &left, const QModelIndex &right) const override;

private:
    QString m_filterText;
};

// 模板方法实现

template<typename Func>
void BaseViewModel::executeAsync(Func&& operation)
{
    setLoading(true);
    
    auto future = QtConcurrent::run([this, operation = std::forward<Func>(operation)]() {
        try {
            operation();
            
            // 在主线程中更新UI
            QMetaObject::invokeMethod(this, [this]() {
                setLoading(false);
                emit dataLoaded(true);
            }, Qt::QueuedConnection);
            
        } catch (const std::exception& e) {
            // 在主线程中处理错误
            QMetaObject::invokeMethod(this, [this, error = QString::fromStdString(e.what())]() {
                setLoading(false);
                setErrorMessage(error);
                emit dataLoaded(false);
            }, Qt::QueuedConnection);
        }
    });
}

template<typename Func>
void BaseViewModel::executeInMainThread(Func&& operation)
{
    if (QThread::currentThread() == this->thread()) {
        // 已在主线程中
        operation();
    } else {
        // 切换到主线程执行
        QMetaObject::invokeMethod(this, [operation = std::forward<Func>(operation)]() {
            operation();
        }, Qt::QueuedConnection);
    }
}
